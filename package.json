{"name": "gloopi-admin", "version": "0.1.0", "description": "this is Gloopi-admin built with react.js and tailwindcss", "private": true, "type": "module", "dependencies": {"@cloudinary/react": "^1.11.2", "@cloudinary/url-gen": "^1.10.1", "@headlessui/react": "^1.7.15", "@pathofdev/react-tag-input": "^1.0.7", "@preact/signals-react": "^1.3.6", "@react-pdf/renderer": "^3.1.6", "@reduxjs/toolkit": "^1.9.7", "@tanstack/react-query": "^5.65.1", "@tinymce/tinymce-react": "^6.3.0", "@windmill/react-ui": "^0.6.0", "ajv": "^8.12.0", "axios": "^1.4.0", "browser-image-compression": "^2.0.2", "chart.js": "^4.4.0", "cloudinary": "^1.40.0", "combinate": "^1.1.11", "csvtojson": "^2.0.10", "dayjs": "^1.11.8", "dotenv": "^16.1.4", "export-from-json": "^1.7.2", "i18next": "^22.5.1", "i18next-browser-languagedetector": "^7.0.2", "immutability-helper": "^3.1.1", "js-cookie": "^3.0.5", "local-storage": "^2.0.0", "million": "^2.2.1", "multiselect-react-dropdown": "^2.0.25", "pica": "^9.0.1", "rc-drawer": "^4.4.3", "rc-switch": "^4.1.0", "rc-tree": "^5.7.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-custom-scrollbars-2": "^4.5.0", "react-datepicker": "^4.13.0", "react-device-detect": "^2.2.3", "react-dnd": "^14.0.5", "react-dnd-html5-backend": "^14.1.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-flags-select": "^2.2.3", "react-ga4": "^2.1.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.44.3", "react-i18next": "^12.3.1", "react-icons": "^4.9.0", "react-jwt": "^1.2.0", "react-loading-skeleton": "^3.3.1", "react-multi-select-component": "^4.3.4", "react-redux": "^8.1.0", "react-responsive-modal": "^6.4.1", "react-router-dom": "^5.3.4", "react-simple-keyboard": "^3.6.15", "react-spinners": "^0.13.8", "react-switch": "^7.0.0", "react-tabs": "^6.0.1", "react-tag-input": "^6.8.1", "react-tag-input-component": "^2.0.2", "react-to-print": "^2.14.13", "react-toastify": "^9.1.3", "react-tooltip": "^5.28.0", "react-transition-group": "^4.4.5", "react-use-cart": "^1.13.0", "redux": "^4.2.1", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "socket.io-client": "^4.7.2", "sweetalert": "^2.1.2", "tailwind-scrollbar-hide": "^1.1.7"}, "resolutions": {"@react-pdf/font": "^3.1.12", "react-error-overlay": "6.0.9"}, "scripts": {"dev": "vite --force --port 4100", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "author": "HtmlLover", "license": "Regular", "devDependencies": {"@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "jsdom": "^22.1.0", "postcss": "^8.4.31", "react-error-overlay": "^6.0.9", "rollup": "^3.26.1", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.3", "vite": "^7.0.6", "vite-plugin-compression2": "^0.11.0", "vite-plugin-css-injected-by-js": "^3.3.0", "vite-plugin-pwa": "^1.0.2", "vitest": "^3.2.4", "webpack-bundle-analyzer": "^4.9.0", "workbox-window": "^7.0.0"}, "release-it": {"github": {"release": true}, "npm": {"publish": false}}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}