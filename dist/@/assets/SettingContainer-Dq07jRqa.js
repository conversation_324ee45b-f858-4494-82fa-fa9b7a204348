import{k as l,j as s,h as e}from"./index-DpMxJ5Hx.js";import{z as m}from"./Layout-B-UGxzbM.js";import{s as o}from"./spinner-CkndCogW.js";const p=({isSave:a,title:i,children:n,isSubmitting:r})=>{const{t}=l();return s.jsx("div",{className:"grid grid-cols-12 font-sans pr-4",children:s.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12",children:[s.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:r?s.jsxs(e.<PERSON><PERSON>,{disabled:!0,type:"button",className:"h-10 px-6",children:[s.jsx("img",{src:o,alt:"Loading",width:20,height:10}),s.jsx("span",{className:"font-serif ml-2 font-light",children:t("Processing")})]}):s.jsx(e.<PERSON><PERSON>,{type:"submit",className:"h-10 px-6 ",children:t(a?"SaveBtn":"UpdateBtn")})}),s.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[s.jsx(m,{className:"mt-1 mr-2"}),i]}),s.jsx("hr",{className:"md:mb-12 mb-3"}),n]})})};export{p as S};
