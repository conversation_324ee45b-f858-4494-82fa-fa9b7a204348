import{r as N,h as s,k as y,j as e,S as T,A as E}from"./index-DpMxJ5Hx.js";import{k as A,l as P,r as F,u as B,i as z,g as M}from"./Layout-B-UGxzbM.js";import{u as L}from"./useAsync-Hr4bbxCm.js";import{u as U}from"./useFilter-Qjmg-ZWR.js";import{T as v,D as Z,u as O,M as D}from"./DrawerButton-CKK8nF3h.js";import{j as _}from"./index-BX3AmOEM.js";import{E as w}from"./index.esm-B8kiXavo.js";import{I as S}from"./InputArea-BGdt-vbi.js";import{u as q}from"./useStaffSubmit-BEjPyIe8.js";import{S as J}from"./SelectRole-iRRB4wHv.js";import{L as p}from"./LabelArea-CQP0v-a8.js";import{U as V}from"./Uploader-sUWHaRyq.js";import{T as $}from"./TableLoading-C5i6hQGj.js";import{S as H}from"./Status-BjgMFBDv.js";import{T as W}from"./Tooltip-BQ_BZ_s8.js";import{D as G,E as K}from"./EditDeleteButton-DsdeAqDJ.js";import{a as Q}from"./index.prod-BR0InCj9.js";import{A as k}from"./AdminServices-CIs7colP.js";import{a as C,n as X}from"./toast-Be5Wd3gm.js";import{N as Y}from"./NotFound-BXAjvYR4.js";import{P as ee}from"./PageTitle-D-hGib5s.js";import{A as se}from"./AnimatedContent-DVQRys_r.js";import"./iconBase-BUmmAlr8.js";import"./ProductServices-CnM1m97m.js";import"./index-C148XJoK.js";import"./useDisableForDemo-DczgqPm6.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";import"./SelectLanguageTwo-CnZMFe5S.js";import"./spinner-CkndCogW.js";import"./useTranslationValue-DM8I18Uu.js";import"./_commonjs-dynamic-modules-CNspEXFA.js";const I=({id:i})=>{const{role:o}=A(),{mode:d}=N.useContext(s.WindmillContext),{register:t,handleSubmit:m,onSubmit:c,errors:n,imageUrl:x,setImageUrl:g,isSubmitting:r,selectedDate:h,setSelectedDate:j,accessedRoutes:u,setAccessedRoutes:f,handleSelectLanguage:b}=q(i),{t:a}=y();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:i?e.jsx(v,{register:t,handleSelectLanguage:b,title:a("UpdateStaff"),description:a("UpdateStaffdescription")}):e.jsx(v,{register:t,handleSelectLanguage:b,title:a("AddStaffTitle"),description:a("AddStaffdescription")})}),e.jsx(P.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsx(s.Card,{className:"overflow-y-scroll flex-grow scrollbar-hide w-full max-h-full",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:m(c),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Staff Image"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(V,{imageUrl:x,setImageUrl:g,folder:"admin",targetWidth:238,targetHeight:238})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Name"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(S,{required:!0,register:t,label:"Name",name:"name",type:"text",autoComplete:"username",placeholder:"Staff name"}),e.jsx(w,{errorName:n.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Email"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(S,{required:!0,register:t,label:"Email",name:"email",type:"text",autoComplete:"username",pattern:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,placeholder:"Email"}),e.jsx(w,{errorName:n.email})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Password"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[i?e.jsx(S,{register:t,label:"Password",name:"password",type:"password",autoComplete:"current-password",placeholder:"Password"}):e.jsx(S,{required:!0,register:t,label:"Password",name:"password",type:"password",autoComplete:"current-password",placeholder:"Password"}),e.jsx(w,{errorName:n.password})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Contact Number"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(S,{required:!0,register:t,label:"Contact Number",name:"phone",pattern:/^[+]?\d*$/,minLength:6,maxLength:15,type:"text",placeholder:"Phone number"}),e.jsx(w,{errorName:n.phone})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Joining Date"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(s.Input,{onChange:l=>j(l.target.value),label:"Joining Date",name:"joiningDate",value:h,type:"date",placeholder:a("StaffJoiningDate")}),e.jsx(w,{errorName:n.joiningDate})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Staff Role"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(J,{register:t,label:"Role",name:"role"}),e.jsx(w,{errorName:n.role})]})]}),o==="Admin"||o==="Super Admin"&&e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(p,{label:"Select Routes to given Access"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(_,{options:F,value:u,className:d,onChange:l=>f(l),labelledBy:"Select Coupon"})})]})]}),e.jsx(Z,{id:i,title:"Staff",zIndex:"z-5",isSubmitting:r})]})})})})]})},ae=({id:i,status:o,option:d,staff:t})=>{const{setIsUpdate:m}=N.useContext(T),{role:c}=A(),n=async(x,g)=>{if(!(c==="Super Admin"||c==="Admin"))return C("Only Super Admin and Admin can enable/disable any staff!");try{let r;o==="Active"?r="Inactive":r="Active";const h=await k.updateStaffStatus(x,{status:r});m(!0),X(h.message);return}catch(r){C(r?r?.response?.data?.message:r?.message)}};return e.jsx(e.Fragment,{children:e.jsx(Q,{onChange:()=>n(i),checked:o==="Active",className:"react-switch md:ml-0",uncheckedIcon:e.jsx("div",{style:{display:"flex",alignItems:"center",height:"100%",width:120,fontSize:14,color:"white",paddingRight:22,paddingTop:1}}),width:30,height:15,handleDiameter:13,offColor:"#E53E3E",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:73,height:"100%",fontSize:14,color:"white",paddingLeft:20,paddingTop:1}})})})},le=({isOpen:i,onClose:o,staff:d,showingTranslateValue:t})=>e.jsxs(s.Modal,{isOpen:i,onClose:o,children:[e.jsxs("h1",{className:"text-xl font-medium text-center pb-6 dark:text-gray-300",children:["List of route access for"," ",e.jsx("span",{className:"text-emerald-600",children:t(d?.name)})]}),e.jsx(s.ModalBody,{children:d?.access_list?.length>0?e.jsx("ol",{className:"list-disc pl-5",children:d?.access_list?.map((m,c)=>e.jsx("li",{className:"text-sm text-gray-700 dark:text-gray-300 capitalize",children:m},c))}):e.jsx("p",{className:"text-orange-500 py-10 text-lg text-center",children:"This staff not have any route access!"})}),e.jsx(s.ModalFooter,{className:"justify-end",children:e.jsx(s.Button,{className:"w-full sm:w-auto bg-red-400 text-white hover:bg-red-500",layout:"delete",onClick:o,children:"Close"})})]}),te=({staffs:i,lang:o})=>{const{title:d,serviceId:t,handleModalOpen:m,handleUpdate:c,isSubmitting:n,handleResetPassword:x}=O(),{showDateFormat:g,showingTranslateValue:r}=B(),[h,j]=N.useState(null),[u,f]=N.useState(!1),b=l=>{j(l),f(!0)},a=()=>{j(null),f(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(G,{id:t,title:d}),u&&e.jsx(le,{staff:h,isOpen:u,onClose:a,showingTranslateValue:r}),e.jsx(D,{children:e.jsx(I,{id:t})}),e.jsx(s.TableBody,{children:i?.map(l=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(s.Avatar,{className:"hidden mr-3 md:block bg-gray-50",src:l.image,alt:"staff"}),e.jsx("div",{children:e.jsx("h2",{className:"text-sm font-medium",children:r(l?.name)})})]})}),e.jsxs(s.TableCell,{children:[e.jsx("span",{className:"text-sm",children:l.email})," "]}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm ",children:l.phone})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:g(l.joiningData)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm font-semibold",children:l?.role})}),e.jsx(s.TableCell,{className:"text-center text-xs",children:e.jsx(H,{status:l.status})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(ae,{id:l?._id,staff:l,option:"staff",status:l.status})}),e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>b(l),className:"text-gray-400",children:e.jsx(W,{id:"view",Icon:z,title:"View Access Route",bgColor:"#059669"})}),e.jsx(K,{id:l._id,staff:l,isSubmitting:n,handleUpdate:c,handleModalOpen:m,handleResetPassword:x,title:r(l?.name)})]})})]},l._id))})]})},Me=()=>{const{state:i}=N.useContext(E),{adminInfo:o}=i,{toggleDrawer:d,lang:t}=N.useContext(T),{data:m,loading:c,error:n}=L(()=>k.getAllStaff({email:o.email})),{userRef:x,setRole:g,totalResults:r,resultsPerPage:h,dataTable:j,serviceData:u,handleChangePage:f,handleSubmitUser:b}=U(m),{t:a}=y(),l=()=>{g(""),x.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsxs(ee,{children:[a("StaffPageTitle")," "]}),e.jsx(D,{children:e.jsx(I,{})}),e.jsx(se,{children:e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:b,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsxs("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx(s.Input,{ref:x,type:"search",name:"search",placeholder:a("StaffSearchBy")}),e.jsx("button",{type:"submit",className:"absolute right-0 top-0 mt-5 mr-1"})]}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(s.Select,{onChange:R=>g(R.target.value),children:[e.jsx("option",{value:"All",defaultValue:!0,hidden:!0,children:a("StaffRole")}),e.jsx("option",{value:"Admin",children:a("StaffRoleAdmin")}),e.jsx("option",{value:"Cashier",children:a("SelectCashiers")}),e.jsx("option",{value:"Super Admin",children:a("SelectSuperAdmin")})]})}),e.jsx("div",{className:"w-full md:w-56 lg:w-56 xl:w-56",children:e.jsxs(s.Button,{onClick:d,className:"w-full rounded-md h-12",children:[e.jsx("span",{className:"mr-3",children:e.jsx(M,{})}),a("AddStaff")]})}),e.jsxs("div",{className:"mt-2 md:mt-0 flex items-center xl:gap-x-4 gap-x-1 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full",children:e.jsx(s.Button,{layout:"outline",onClick:l,type:"reset",className:"px-4 md:py-1 py-3 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})}),c?e.jsx($,{row:12,col:7,width:163,height:20}):n?e.jsx("span",{className:"text-center mx-auto text-red-500",children:n}):u?.length!==0?e.jsxs(s.TableContainer,{className:"mb-8 rounded-b-lg",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:a("StaffNameTbl")}),e.jsx(s.TableCell,{children:a("StaffEmailTbl")}),e.jsx(s.TableCell,{children:a("StaffContactTbl")}),e.jsx(s.TableCell,{children:a("StaffJoiningDateTbl")}),e.jsx(s.TableCell,{children:a("StaffRoleTbl")}),e.jsx(s.TableCell,{className:"text-center",children:a("OderStatusTbl")}),e.jsx(s.TableCell,{className:"text-center",children:a("PublishedTbl")}),e.jsx(s.TableCell,{className:"text-center",children:a("StaffActionsTbl")})]})}),e.jsx(te,{staffs:j,lang:t})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:r,resultsPerPage:h,onChange:f,label:"Table navigation"})})]}):e.jsx(Y,{title:"Sorry, There are no staff right now."})]})};export{Me as default};
