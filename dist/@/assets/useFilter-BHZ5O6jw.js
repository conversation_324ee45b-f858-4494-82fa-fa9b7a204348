import{a as ze,P as Ze,c as $e,C as Ge,b as He,A as Xe}from"./ProductServices-CGXRs0W4.js";import{d as I,u as ke}from"./Layout-f_j_aP34.js";import{c as ce,g as pe,r as n,u as Ke,S as Qe,L as We}from"./index-DD5OQCzb.js";import{u as et}from"./useDisableForDemo-aTnQzb5-.js";import{C as tt}from"./CouponServices-BvJiM6D0.js";import{C as st}from"./CurrencyServices-CicNeQxs.js";import{a as c,n as R}from"./toast-C_V_NPJL.js";var me={exports:{}};(function(w,O){(function(A,y){w.exports=y()})(ce,function(){return function(A,y,h){y.prototype.isBetween=function(x,T,g,C){var P=h(x),p=h(T),_=(C=C||"()")[0]==="(",j=C[1]===")";return(_?this.isAfter(P,g):!this.isBefore(P,g))&&(j?this.isBefore(p,g):!this.isAfter(p,g))||(_?this.isBefore(P,g):!this.isAfter(P,g))&&(j?this.isAfter(p,g):!this.isBefore(p,g))}}})})(me);var nt=me.exports;const ot=pe(nt);var fe={exports:{}};(function(w,O){(function(A,y){w.exports=y()})(ce,function(){return function(A,y,h){y.prototype.isToday=function(){var x="YYYY-MM-DD",T=h();return this.format(x)===T.format(x)}}})})(fe);var at=fe.exports;const rt=pe(at),it={type:"object",properties:{_id:{type:"string"},name:{type:"object"},description:{type:"object"},icon:{type:"string"},status:{type:"string"}},required:["name"]},ut={type:"object",properties:{status:{type:"string"},title:{type:"object"},name:{type:"object"},variants:{type:"array"},option:{type:"string"},type:{type:"string"}},required:["name","title"]},dt={type:"object",properties:{title:{type:"object"},couponCode:{type:"string"},endTime:{type:"string"},discountPercentage:{type:"number"},minimumAmount:{type:"number"},productType:{type:"string"},logo:{type:"string"},discountType:{type:"object"},status:{type:"string"}},required:["title","couponCode","endTime","status"]},lt={type:"object",properties:{name:{type:"string"},email:{type:"string"}},required:["name","email"]},_t=w=>{const O=new ze({allErrors:!0}),[A,y]=n.useState(""),[h,x]=n.useState(""),[T,g]=n.useState(""),[C,P]=n.useState(""),[p,_]=n.useState(""),[j,ge]=n.useState(""),[b,Q]=n.useState(""),[D,W]=n.useState(""),[B,ee]=n.useState(""),[Se,ye]=n.useState(""),[U,Ce]=n.useState(""),[Y,we]=n.useState(""),[he,be]=n.useState([]),[De,Le]=n.useState([]),[Ae,Te]=n.useState([]),[z,Pe]=n.useState(""),[Z,Re]=n.useState(""),[v,Oe]=n.useState(""),[$,xe]=n.useState(1),[je,te]=n.useState([]),[Fe,Ne]=n.useState(""),[_e,Be]=n.useState(""),[Ue,Je]=n.useState(""),[S,G]=n.useState([]),[Ee,M]=n.useState(""),[Ie,V]=n.useState(!1),[H,ve]=n.useState(""),[X]=n.useState([]),se=n.useRef(""),ne=n.useRef(""),oe=n.useRef(""),ae=n.useRef(""),re=n.useRef(""),k=n.useRef(""),ie=n.useRef(""),ue=n.useRef(""),de=n.useRef(""),Me=n.useRef(""),le=n.useRef("");I.extend(ot),I.extend(rt);const d=Ke(),{lang:J,setIsUpdate:F,setLoading:m}=n.useContext(Qe),{globalSetting:N}=ke(),{handleDisableForDemo:K}=et(),L=n.useMemo(()=>{const a=new Date;a.setDate(a.getDate()-v);let t=w==null?void 0:w.map(e=>{const r=new Date(e==null?void 0:e.updatedAt).toLocaleString("en-US",{timeZone:N==null?void 0:N.default_time_zone});return{...e,updatedDate:r==="Invalid Date"?"":r}});if(d.pathname==="/dashboard"){const e=t==null?void 0:t.filter(l=>l.status==="Pending");be(e);const r=t==null?void 0:t.filter(l=>l.status==="Processing");Le(r);const s=t==null?void 0:t.filter(l=>l.status==="Delivered");Te(s);const u=t==null?void 0:t.filter(l=>I(l.createdAt).isToday()),f=u==null?void 0:u.reduce((l,E)=>l+E.total,0);Ne(f);const o=t==null?void 0:t.filter(l=>I(l.createdAt).isBetween(new Date().setDate(new Date().getDate()-30),new Date)),i=o==null?void 0:o.reduce((l,E)=>l+E.total,0);Be(i);const Ye=t==null?void 0:t.reduce((l,E)=>l+E.total,0);Je(Ye)}return A&&(t=t.filter(e=>e.parent===A)),h==="Low"&&(t=t.sort((e,r)=>e.price<r.price&&-1)),h==="High"&&(t=t.sort((e,r)=>e.price>r.price&&-1)),T&&(t=t.filter(e=>{var r;return(r=e==null?void 0:e.title)==null?void 0:r.toLowerCase().includes(T.toLowerCase())})),D&&(t=t.filter(e=>{var r,s,u;return((s=(r=e==null?void 0:e.title[J])==null?void 0:r.toLowerCase())==null?void 0:s.includes(D==null?void 0:D.toLowerCase()))||((u=e==null?void 0:e.attribute)==null?void 0:u.toLowerCase().includes(D==null?void 0:D.toLowerCase()))})),b&&(t=t.filter(e=>{var r,s,u;return((s=(r=e==null?void 0:e.name[J])==null?void 0:r.toLowerCase())==null?void 0:s.includes(b==null?void 0:b.toLowerCase()))||((u=e==null?void 0:e.category)==null?void 0:u.toLowerCase().includes(b==null?void 0:b.toLowerCase()))})),Z&&(t=t.filter(e=>e.role===Z)),C&&(t=t.filter(e=>{var r,s,u;return((r=e==null?void 0:e.name[J])==null?void 0:r.toLowerCase().includes(C.toLowerCase()))||((s=e==null?void 0:e.phone)==null?void 0:s.toLowerCase().includes(C.toLowerCase()))||((u=e==null?void 0:e.email)==null?void 0:u.toLowerCase().includes(C.toLowerCase()))})),p&&(t=t==null?void 0:t.filter(e=>{var r,s,u;return((s=(r=e==null?void 0:e.title[J])==null?void 0:r.toLowerCase())==null?void 0:s.includes(p==null?void 0:p.toLowerCase()))||((u=e==null?void 0:e.couponCode)==null?void 0:u.toLowerCase().includes(p==null?void 0:p.toLowerCase()))})),z&&(t=t.filter(e=>e.status===z)),j&&(t=t.filter(e=>e.contact.toLowerCase().includes(j.toLowerCase()))),v&&(t=t.filter(e=>I(e.createdAt).isBetween(a,new Date))),B&&(t=t.filter(e=>{var r,s;return((r=e==null?void 0:e.name)==null?void 0:r.toLowerCase().includes(B.toLowerCase()))||((s=e==null?void 0:e.iso_code)==null?void 0:s.toLowerCase().includes(B.toLowerCase()))})),H&&(t=t.filter(e=>e==null?void 0:e.name.toLowerCase().includes(H.toLowerCase()))),U&&(t=t.filter(e=>e.name.toLowerCase().includes(U.toLowerCase())||e.iso_code.toLowerCase().includes(U.toLowerCase())||e.language_code.toLowerCase().includes(U.toLowerCase()))),Y&&(t=t.filter(e=>e.iso_code.toLowerCase().includes(Y.toLowerCase()))),t},[v,w,d.pathname,A,h,T,D,b,Z,C,p,z,j,B,H,U,Y,k,N==null?void 0:N.default_time_zone,J]),q=20,Ve=L==null?void 0:L.length,qe=a=>{xe(a)};return n.useEffect(()=>{te(L==null?void 0:L.slice(($-1)*q,$*q))},[L,$,q]),{userRef:oe,searchRef:ne,couponRef:ae,orderRef:re,categoryRef:k,attributeRef:ie,pending:he,processing:De,delivered:Ae,todayOrder:Fe,monthlyOrder:_e,totalOrder:Ue,setFilter:y,setSortedField:x,setStatus:Pe,setRole:Re,time:v,zone:Se,setTime:Oe,taxRef:Me,setZone:ye,filename:Ee,countryRef:ue,dataTable:je,serviceData:L,country:B,setSearchText:g,setCountry:ee,isDisabled:Ie,languageRef:de,currencyRef:se,shippingRef:le,setSearchUser:P,setDataTable:te,setCategoryType:Q,handleChangePage:qe,totalResults:Ve,resultsPerPage:q,handleOnDrop:a=>{for(let t=0;t<a.length;t++)X.push(a[t].data)},setSearchCoupon:_,setAttributeTitle:W,handleSelectFile:a=>{var r;if(a.preventDefault(),K())return;const t=new FileReader,e=(r=a.target)==null?void 0:r.files[0];e&&e.type==="application/json"?(M(e==null?void 0:e.name),V(!0),console.log("if"),t.readAsText(e,"UTF-8"),t.onload=s=>{let u=JSON.parse(s.target.result),f=[];d.pathname==="/categories"&&(f=u.map(o=>({_id:o._id,id:o.id,status:o.status,name:o.name,description:o.description,parentName:o.parentName,parentId:o.parentId,icon:o.icon}))),d.pathname==="/attributes"&&(f=u.map(o=>({_id:o._id,status:o.status,title:o.title,name:o.name,variants:o.variants,option:o.option,type:o.type}))),d.pathname==="/coupons"&&(f=u.map(o=>({title:o.title,couponCode:o.couponCode,endTime:o.endTime,discountPercentage:o.discountPercentage,minimumAmount:o.minimumAmount,productType:o.productType,logo:o.logo,discountType:o.discountType,status:o.status}))),d.pathname==="/customers"&&(f=u.map(o=>({name:o.name,email:o.email,password:o.password,phone:o.phone}))),G(f)}):e&&e.type==="text/csv"?(M(e==null?void 0:e.name),V(!0),console.log("else if"),t.onload=async s=>{const u=s.target.result,f=await $e().fromString(u);let o=[];d.pathname==="/categories"&&(o=f.map(i=>({_id:i._id,id:i.id,status:i.status,name:JSON.parse(i.name),description:JSON.parse(i.description),parentName:i.parentName,parentId:i.parentId,icon:i.icon}))),d.pathname==="/attributes"&&(o=f.map(i=>({status:i.status,title:JSON.parse(i.title),name:JSON.parse(i.name),variants:JSON.parse(i.variants),option:i.option,type:i.type}))),d.pathname==="/coupons"&&(o=f.map(i=>({title:JSON.parse(i.title),couponCode:i.couponCode,endTime:i.endTime,discountPercentage:i.discountPercentage?JSON.parse(i.discountPercentage):0,minimumAmount:i.minimumAmount?JSON.parse(i.minimumAmount):0,productType:i.productType,logo:i.logo,status:i.status}))),d.pathname==="/customers"&&(o=f.map(i=>({name:i.name,email:i.email,password:i.password,phone:i.phone}))),G(o)},t.readAsText(e)):(M(e==null?void 0:e.name),V(!0),c("Unsupported file type!"))},handleSubmitUser:a=>{a.preventDefault(),P(oe.current.value)},handleSubmitForAll:a=>{a.preventDefault(),g(ne.current.value)},handleSubmitCoupon:a=>{a.preventDefault(),_(ae.current.value)},handleSubmitOrder:a=>{a.preventDefault(),ge(re.current.value)},handleSubmitCategory:a=>{a.preventDefault(),Q(k.current.value)},handleSubmitAttribute:a=>{a.preventDefault(),W(ie.current.value)},handleUploadProducts:()=>{if(X.length<1)c("Please upload/select csv file first!");else{if(K())return;Ze.addAllProducts(X).then(a=>{R(a.message)}).catch(a=>c(a.message))}},handleSubmitCountry:a=>{a.preventDefault(),ee(ue.current.value)},handleSubmitCurrency:a=>{a.preventDefault(),we(se.current.value)},handleSubmitShipping:a=>{a.preventDefault(),ve(le.current.value)},handleSubmitLanguage:a=>{a.preventDefault(),Ce(de.current.value)},handleUploadMultiple:a=>{if(!K())if(S.length>1){if(d.pathname==="/categories"){m(!0);let t=S.map(s=>O.validate(it,s));const e=s=>s===!0;t.every(e)?Ge.addAllCategory(S).then(s=>{m(!1),F(!0),R(s.message)}).catch(s=>{m(!1),c(s?s.response.data.message:s.message)}):c("Please enter valid data!")}if(d.pathname==="/customers"){m(!0);let t=S.map(s=>O.validate(lt,s));const e=s=>s===!0;t.every(e)?He.addAllCustomers(S).then(s=>{m(!1),F(!0),R(s.message)}).catch(s=>{m(!1),c(s?s.response.data.message:s.message)}):c("Please enter valid data!")}if(d.pathname==="/coupons"){m(!0);let t=S.map(s=>O.validate(dt,s));const e=s=>s===!0;t.every(e)?tt.addAllCoupon(S).then(s=>{m(!1),F(!0),R(s.message)}).catch(s=>{m(!1),c(s?s.response.data.message:s.message)}):c("Please enter valid data!")}if(d.pathname==="/attributes"){m(!0);let t=S.map(s=>O.validate(ut,s));const e=s=>s===!0;t.every(e)?Xe.addAllAttributes(S).then(s=>{m(!1),F(!0),R(s.message)}).catch(s=>{m(!1),c(s?s.response.data.message:s.message)}):c("Please enter valid data!")}d.pathname==="/languages"&&We.addAllLanguage(S).then(t=>{F(!0),R(t.message)}).catch(t=>c(t?t.response.data.message:t.message)),d.pathname==="/currencies"&&st.addAllCurrency(S).then(t=>{F(!0),R(t.message)}).catch(t=>c(t?t.response.data.message:t.message))}else c("Please select a valid .JSON/.CSV/.XLS file first!")},handleRemoveSelectFile:a=>{M(""),G([]),setTimeout(()=>V(!1),1e3)}}};export{rt as a,ot as i,_t as u};
