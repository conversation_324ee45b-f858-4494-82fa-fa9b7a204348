import{j as e,h as s,s as d,k as x}from"./index-DpMxJ5Hx.js";import{u as h,m as j}from"./Layout-B-UGxzbM.js";import{u}from"./useAsync-Hr4bbxCm.js";import{O as b}from"./OrderServices-ByScXALP.js";import{u as p}from"./useFilter-Qjmg-ZWR.js";import{P as C}from"./PageTitle-D-hGib5s.js";import{L as T}from"./Loading-DJVLGGxg.js";import{S as g}from"./Status-BjgMFBDv.js";import{S as f}from"./SelectStatus-DFwM1XF_.js";import"./iconBase-BUmmAlr8.js";import"./ProductServices-CnM1m97m.js";import"./index-C148XJoK.js";import"./useDisableForDemo-DczgqPm6.js";import"./toast-Be5Wd3gm.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";const N=({orders:n})=>{const{showDateTimeFormat:l,getNumberTwo:r,currency:a}=h();return e.jsx(e.Fragment,{children:e.jsx(s.TableBody,{children:n?.map(t=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx("span",{className:"font-semibold uppercase text-xs",children:t?._id?.substring(20,24)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:l(t.createdAt)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:t?.user_info?.address})}),e.jsxs(s.TableCell,{children:[" ",e.jsx("span",{className:"text-sm",children:t.user_info?.contact})," "]}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm font-semibold",children:t.paymentMethod})}),e.jsxs(s.TableCell,{children:[" ",e.jsxs("span",{className:"text-sm font-semibold",children:[a,r(t.total)]})," "]}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(g,{status:t.status})}),e.jsx(s.TableCell,{className:"text-right",children:e.jsx(f,{id:t._id,order:t})})]},t._id))})})},M=()=>{const{id:n}=d(),{t:l}=x(),{data:r,loading:a,error:t}=u(()=>b.getOrderCustomer(n)),{handleChangePage:m,totalResults:o,resultsPerPage:c,dataTable:i}=p(r);return e.jsxs(e.Fragment,{children:[e.jsx(C,{children:l("CustomerOrderList")}),a&&e.jsx(T,{loading:a}),!t&&!a&&i.length===0&&e.jsx("div",{className:"w-full bg-white rounded-md dark:bg-gray-800",children:e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("span",{className:"flex justify-center my-30 text-red-500 font-semibold text-6xl",children:e.jsx(j,{})}),e.jsx("h2",{className:"font-medium text-base mt-4 text-gray-600",children:l("CustomerOrderEmpty")})]})}),r.length>0&&!t&&!a?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsxs(s.TableCell,{children:[" ",l("CustomerOrderId")," "]}),e.jsx(s.TableCell,{children:l("CustomerOrderTime")}),e.jsx(s.TableCell,{children:l("CustomerShippingAddress")}),e.jsxs(s.TableCell,{children:[l("Phone")," "]}),e.jsxs(s.TableCell,{children:[l("CustomerOrderMethod")," "]}),e.jsx(s.TableCell,{children:l("Amount")}),e.jsxs(s.TableCell,{className:"text-center",children:[" ",l("CustomerOrderStatus")," "]}),e.jsx(s.TableCell,{className:"text-center",children:l("CustomerOrderAction")})]})}),e.jsx(N,{orders:i})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:o,resultsPerPage:c,onChange:m,label:"Table navigation"})})]}):null]})};export{M as default};
