import{r as i,S as u,j as r}from"./index-DD5OQCzb.js";import{u as c}from"./Layout-f_j_aP34.js";const m=({handleSelectLanguage:t,register:s})=>{const{languages:o,langError:n,langLoading:d}=c(),{lang:a}=i.useContext(u);return r.jsx(r.Fragment,{children:r.jsxs("select",{name:"language",...s("language",{required:"language is required!"}),onChange:e=>t(e.target.value),className:"block w-20 h-10 border border-emerald-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 text-sm dark:text-gray-300 focus:outline-none rounded-md form-select focus:bg-white dark:focus:bg-gray-700",children:[r.jsx("option",{value:a,defaultChecked:!0,hidden:!0,children:a}),!n&&!d&&(o==null?void 0:o.map(e=>r.jsxs("option",{value:e.iso_code,children:[e.iso_code," "]},e._id)))]})})};export{m as S};
