import{j as e}from"./index-DD5OQCzb.js";import{a as l}from"./index.prod-CisttSXz.js";const r=({id:i,title:t,handleProcess:n,processOption:s})=>e.jsx(e.Fragment,{children:e.jsx("div",{className:"mb-3",children:e.jsxs("div",{className:"flex flex-wrap items-center",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1",children:t}),e.jsx(l,{id:i||t||"",onChange:n,checked:s,className:"react-switch md:ml-0 ml-3",uncheckedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:14,color:"white",paddingRight:5,paddingTop:1},children:"No"}),width:80,height:30,handleDiameter:28,offColor:"#E53E3E",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:14,color:"white",paddingLeft:8,paddingTop:1},children:"Yes"})})]})})});export{r as S};
