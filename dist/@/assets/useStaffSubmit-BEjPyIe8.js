import{d as S}from"./Layout-B-UGxzbM.js";import{r as t,A as F,S as G,u as H,i as A}from"./index-DpMxJ5Hx.js";import{u as J}from"./index.esm-B8kiXavo.js";import{A as d}from"./AdminServices-CIs7colP.js";import{a as E,n as M}from"./toast-Be5Wd3gm.js";import{u as q}from"./useTranslationValue-DM8I18Uu.js";const $=i=>{const{state:U,dispatch:_}=t.useContext(F),{adminInfo:c}=U,{isDrawerOpen:D,closeDrawer:p,setIsUpdate:h,lang:C}=t.useContext(G),[w,l]=t.useState(""),[g,y]=t.useState(S(new Date).format("YYYY-MM-DD")),[s,Y]=t.useState("en"),[u,b]=t.useState({}),[L,f]=t.useState(!1),[j,x]=t.useState([]),I=H(),{handlerTextTranslateHandler:R}=q(),{register:k,handleSubmit:v,setValue:a,clearErrors:n,formState:{errors:N}}=J(),V=async e=>{try{f(!0);const m={name:{...await R(e.name,s,u?.name),[s]:e.name},email:e.email,password:e.password,phone:e.phone,role:e.role,access_list:j?.map(r=>r.value),joiningDate:g||S(new Date).format("YYYY-MM-DD"),image:w,lang:s},T=c?._id===u?._id;if(i){const r=await d.updateStaff(i,m);T&&(_({type:"USER_LOGIN",payload:r}),A.set("adminInfo",JSON.stringify(r),{expires:.5,sameSite:"None",secure:!0})),h(!0),f(!1),M("Staff Updated Successfully!"),p()}else{const r=await d.addStaff(m);h(!0),f(!1),M(r.message),p()}}catch(o){E(o?o?.response?.data?.message:o?.message),f(!1),p()}},O=async()=>{try{const e=await d.getStaffById(i,{email:c.email});if(e){b(e),a("name",e.name[s||"en"]),a("email",e.email),a("password"),a("phone",e.phone),a("role",e.role),y(S(e.joiningData).format("YYYY-MM-DD")),l(e.image);const o=e?.access_list?.map(m=>({label:m,value:m}));x(o)}}catch(e){E(e?e?.response?.data?.message:e?.message)}},B=e=>{Y(e),Object.keys(u).length>0&&a("name",u.name[e||"en"])};return t.useEffect(()=>{if(!D){b({}),a("name"),a("email"),a("password"),a("phone"),a("role"),a("joiningDate"),l(""),n("name"),n("email"),n("password"),n("phone"),n("role"),n("joiningDate"),l(""),Y(C),a("language",s);return}i&&O()},[i,a,D,c.email,n]),t.useEffect(()=>{I.pathname==="/edit-profile"&&A.get("adminInfo")&&O()},[I.pathname,a]),{register:k,handleSubmit:v,onSubmit:V,language:s,errors:N,adminInfo:c,setImageUrl:l,imageUrl:w,selectedDate:g,setSelectedDate:y,isSubmitting:L,accessedRoutes:j,setAccessedRoutes:x,handleSelectLanguage:B}};export{$ as u};
