import{j as e,h as s,t as F,l as Q,r as y,S as U,k as I}from"./index-DpMxJ5Hx.js";import{u as J,i as W,e as X,f as Y,g as ee}from"./Layout-B-UGxzbM.js";import{u as R}from"./useAsync-Hr4bbxCm.js";import{u as M,M as _}from"./DrawerButton-CKK8nF3h.js";import{U as se}from"./UploadMany-BRz6m-Uw.js";import{N as te}from"./NotFound-BXAjvYR4.js";import{a as le,P as A,c as ae,C as re}from"./ProductServices-CnM1m97m.js";import{P as ie}from"./PageTitle-D-hGib5s.js";import{P as E}from"./ProductDrawer-C50xafGT.js";import{C as L,S as oe,B as ne}from"./BulkActionDrawer-ChuFcR4n.js";import{D as V,E as de}from"./EditDeleteButton-DsdeAqDJ.js";import{T as ce}from"./Tooltip-BQ_BZ_s8.js";import{n as O,a as T}from"./toast-Be5Wd3gm.js";import{u as xe}from"./useDisableForDemo-DczgqPm6.js";import{T as me}from"./TableLoading-C5i6hQGj.js";import{A as he}from"./AnimatedContent-DVQRys_r.js";import"./iconBase-BUmmAlr8.js";import"./SelectLanguageTwo-CnZMFe5S.js";import"./spinner-CkndCogW.js";import"./exportFromJSON-fDIoOtpr.js";import"./index-C148XJoK.js";import"./ParentCategory-C2ZW3iQo.js";import"./index-BX3AmOEM.js";import"./index.esm-B8kiXavo.js";import"./InputArea-BGdt-vbi.js";import"./LabelArea-CQP0v-a8.js";import"./InputValue-D_fzDMKQ.js";import"./useTranslationValue-DM8I18Uu.js";import"./Uploader-sUWHaRyq.js";import"./_commonjs-dynamic-modules-CNspEXFA.js";import"./index.prod-BR0InCj9.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";import"./SwitchToggle-CmuQM---.js";import"./AdminServices-CIs7colP.js";const ge=({products:g,isCheck:d,setIsCheck:c})=>{const{title:C,serviceId:o,handleModalOpen:l,handleUpdate:N}=M(),{currency:p,showingTranslateValue:h,getNumberTwo:u}=J(),j=t=>{const{id:f,checked:S}=t.target;c([...d,f]),S||c(d.filter(P=>P!==f))};return e.jsxs(e.Fragment,{children:[d?.length<1&&e.jsx(V,{id:o,title:C}),d?.length<2&&e.jsx(_,{children:e.jsx(E,{currency:p,id:o})}),e.jsx(s.TableBody,{children:g?.map((t,f)=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx(L,{type:"checkbox",name:t?.title?.en,id:t._id,handleClick:j,isChecked:d?.includes(t._id)})}),e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex items-center",children:[t?.image[0]?e.jsx(s.Avatar,{className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none",src:t?.image[0],alt:"product"}):e.jsx(s.Avatar,{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product"}),e.jsx("div",{children:e.jsx("h2",{className:`text-sm font-medium ${t?.title.length>30?"wrap-long-title":""}`,children:h(t?.title)?.substring(0,28)})})]})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:h(t?.category?.name)})}),e.jsx(s.TableCell,{children:e.jsxs("span",{className:"text-sm font-semibold",children:[p,t?.isCombination?u(t?.variants[0]?.originalPrice):u(t?.prices?.originalPrice)]})}),e.jsx(s.TableCell,{children:e.jsxs("span",{className:"text-sm font-semibold",children:[p,t?.isCombination?u(t?.variants[0]?.price):u(t?.prices?.price)]})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:t.stock})}),e.jsx(s.TableCell,{children:t.stock>0?e.jsx(s.Badge,{type:"success",children:F("Selling")}):e.jsx(s.Badge,{type:"danger",children:F("SoldOut")})}),e.jsx(s.TableCell,{children:e.jsx(Q,{to:`/product/${t._id}`,className:"flex justify-center text-gray-400 hover:text-emerald-600",children:e.jsx(ce,{id:"view",Icon:W,title:F("DetailsTbl"),bgColor:"#10B981"})})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(oe,{id:t._id,status:t.status})}),e.jsx(s.TableCell,{children:e.jsx(de,{id:t._id,product:t,isCheck:d,handleUpdate:N,handleModalOpen:l,title:h(t?.title)})})]},f+1))})]})},pe={type:"object",properties:{categories:{type:"array"},image:{type:"array"},tag:{type:"array"},variants:{type:"array"},show:{type:"array"},status:{type:"string"},prices:{type:"object"},isCombination:{type:"boolean"},title:{type:"object"},productId:{type:"string"},slug:{type:"string"},category:{type:"object"},stock:{type:"number"},description:{type:"object"}},required:["categories","category","prices","title"]},ue=g=>{const d=new le({allErrors:!0}),{setLoading:c,setIsUpdate:C}=y.useContext(U),[o]=y.useState([]),[l,N]=y.useState([]),[p,h]=y.useState(""),[u,j]=y.useState(!1),{handleDisableForDemo:t}=xe();return{data:g,filename:p,isDisabled:u,handleSelectFile:async r=>{r.preventDefault();const x=new FileReader,n=r.target?.files[0];n&&n.type==="application/json"?(h(n?.name),j(!0),x.readAsText(n,"UTF-8"),x.onload=b=>{const w=JSON.parse(b.target.result).map(a=>({categories:a.categories,image:a.image,barcode:a.barcode,tag:a.tag,variants:a.variants,status:a.status,prices:a.prices,isCombination:JSON.parse(a.isCombination.toLowerCase()),title:a.title,productId:a.productId,slug:a.slug,sku:a.sku,category:a.category,stock:a.stock,description:a.description}));N(w)}):n&&n.type==="text/csv"?(h(n?.name),j(!0),x.onload=async b=>{const m=b.target.result,a=(await ae().fromString(m)).map(i=>({categories:JSON.parse(i.categories),image:JSON.parse(i.image),barcode:i.barcode,tag:JSON.parse(i.tag),variants:JSON.parse(i.variants),status:i.status,prices:JSON.parse(i.prices),isCombination:JSON.parse(i.isCombination.toLowerCase()),title:JSON.parse(i.title),productId:i.productId,slug:i.slug,sku:i.sku,category:JSON.parse(i.category),stock:JSON.parse(i.stock),description:JSON.parse(i.description)}));N(a)},x.readAsText(n)):(h(n?.name),j(!0),T("Unsupported file type!"))},serviceData:g,handleOnDrop:r=>{for(let x=0;x<r.length;x++)o.push(r[x].data)},handleUploadProducts:()=>{if(!t())if(o.length<1)T("Please upload/select csv file first!");else{if(t())return;A.addAllProducts(o).then(r=>{O(r.message)}).catch(r=>T(r.message))}},handleRemoveSelectFile:r=>{h(""),N([]),setTimeout(()=>j(!1),1e3)},handleUploadMultiple:r=>{if(!t())if(l.length>1){c(!0);let x=l.map(m=>d.validate(pe,m));const n=m=>m===!0;x.every(n)?A.addAllProducts(l).then(m=>{C(!0),c(!1),O(m.message)}).catch(m=>{c(!1),T(m.message)}):(c(!1),T("Please enter valid data!"))}else c(!1),T("Please select a valid json, csv & xls file first!")}}},je=({setCategory:g})=>{const{t:d}=I(),{data:c}=R(re.getAllCategories),{showingTranslateValue:C}=J();return e.jsx(e.Fragment,{children:e.jsxs(s.Select,{onChange:o=>g(o.target.value),children:[e.jsx("option",{value:"All",defaultValue:!0,hidden:!0,children:d("Category")}),c?.map(o=>e.jsx("option",{value:o._id,children:C(o?.name)},o._id))]})})},Ye=()=>{const{title:g,allId:d,serviceId:c,handleDeleteMany:C,handleUpdateMany:o}=M(),{t:l}=I(),{toggleDrawer:N,lang:p,currentPage:h,handleChangePage:u,searchText:j,category:t,setCategory:f,searchRef:S,handleSubmitForAll:P,sortedField:B,setSortedField:v,limitData:D}=y.useContext(U),{data:r,loading:x,error:n}=R(()=>A.getAllProducts({page:h,limit:D,category:t,title:j,price:B})),[b,m]=y.useState(!1),[w,a]=y.useState([]),i=()=>{m(!b),a(r?.products.map(k=>k._id)),b&&a([])},H=()=>{f(""),v(""),S.current.value=""},{serviceData:$,filename:q,isDisabled:Z,handleSelectFile:z,handleUploadMultiple:G,handleRemoveSelectFile:K}=ue(r?.products);return e.jsxs(e.Fragment,{children:[e.jsx(ie,{children:l("ProductsPage")}),e.jsx(V,{ids:d,setIsCheck:a,title:g}),e.jsx(ne,{ids:d,title:"Products"}),e.jsx(_,{children:e.jsx(E,{id:c})}),e.jsxs(he,{children:[e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{className:"",children:e.jsxs("form",{onSubmit:P,className:"py-3 md:pb-0 grid gap-4 lg:gap-6 xl:gap-6 xl:flex",children:[e.jsx("div",{className:"flex-grow-0 sm:flex-grow md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(se,{title:"Products",filename:q,isDisabled:Z,totalDoc:r?.totalDoc,handleSelectFile:z,handleUploadMultiple:G,handleRemoveSelectFile:K})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(s.Button,{disabled:w.length<1,onClick:()=>o(w),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(X,{})}),l("BulkAction")]})}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(s.Button,{disabled:w?.length<1,onClick:()=>C(w,r.products),className:"w-full rounded-md h-12 bg-red-300 disabled btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(Y,{})}),l("Delete")]})}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(s.Button,{onClick:N,className:"w-full rounded-md h-12",children:[e.jsx("span",{className:"mr-2",children:e.jsx(ee,{})}),l("AddProduct")]})})]})]})})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 rounded-t-lg rounded-0 mb-4",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:P,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsxs("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx(s.Input,{ref:S,type:"search",name:"search",placeholder:"Search Product"}),e.jsx("button",{type:"submit",className:"absolute right-0 top-0 mt-5 mr-1"})]}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(je,{setCategory:f,lang:p})}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(s.Select,{onChange:k=>v(k.target.value),children:[e.jsx("option",{value:"All",defaultValue:!0,hidden:!0,children:l("Price")}),e.jsx("option",{value:"low",children:l("LowtoHigh")}),e.jsx("option",{value:"high",children:l("HightoLow")}),e.jsx("option",{value:"published",children:l("Published")}),e.jsx("option",{value:"unPublished",children:l("Unpublished")}),e.jsx("option",{value:"status-selling",children:l("StatusSelling")}),e.jsx("option",{value:"status-out-of-stock",children:l("StatusStock")}),e.jsx("option",{value:"date-added-asc",children:l("DateAddedAsc")}),e.jsx("option",{value:"date-added-desc",children:l("DateAddedDesc")}),e.jsx("option",{value:"date-updated-asc",children:l("DateUpdatedAsc")}),e.jsx("option",{value:"date-updated-desc",children:l("DateUpdatedDesc")})]})}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{layout:"outline",onClick:H,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),x?e.jsx(me,{row:12,col:7,width:160,height:20}):n?e.jsx("span",{className:"text-center mx-auto text-red-500",children:n}):$?.length!==0?e.jsxs(s.TableContainer,{className:"mb-8 rounded-b-lg",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(L,{type:"checkbox",name:"selectAll",id:"selectAll",isChecked:b,handleClick:i})}),e.jsx(s.TableCell,{children:l("ProductNameTbl")}),e.jsx(s.TableCell,{children:l("CategoryTbl")}),e.jsx(s.TableCell,{children:l("PriceTbl")}),e.jsx(s.TableCell,{children:"Sale Price"}),e.jsx(s.TableCell,{children:l("StockTbl")}),e.jsx(s.TableCell,{children:l("StatusTbl")}),e.jsx(s.TableCell,{className:"text-center",children:l("DetailsTbl")}),e.jsx(s.TableCell,{className:"text-center",children:l("PublishedTbl")}),e.jsx(s.TableCell,{className:"text-right",children:l("ActionsTbl")})]})}),e.jsx(ge,{lang:p,isCheck:w,products:r?.products,setIsCheck:a})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:r?.totalDoc,resultsPerPage:D,onChange:u,label:"Product Page Navigation"})})]}):e.jsx(te,{title:"Product"})]})};export{Ye as default};
