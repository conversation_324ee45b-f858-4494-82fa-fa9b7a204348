import{r as N,f as s,i as T,j as e,S as A,A as P}from"./index-DD5OQCzb.js";import{k,l as E,r as F,u as B,i as z,g as M}from"./Layout-f_j_aP34.js";import{u as L}from"./useAsync-CdFiuEZy.js";import{u as U}from"./useFilter-BHZ5O6jw.js";import{T as v,D as Z,u as O,M as D}from"./DrawerButton-C1kY46U5.js";import{j as _}from"./index-CgLdKLua.js";import{E as w}from"./index.esm-BPZGYcl8.js";import{I as C}from"./InputArea-Cu6xCoGw.js";import{u as q}from"./useStaffSubmit-D1nPnR2p.js";import{S as J}from"./SelectRole-C6-enEQn.js";import{L as j}from"./LabelArea-Zz4acQmF.js";import{U as V}from"./Uploader-Bz_6zC0v.js";import{T as $}from"./TableLoading-D3D_P-TQ.js";import{S as H}from"./Status-dJqttBU1.js";import{T as W}from"./Tooltip-DrdTd94n.js";import{D as G,E as K}from"./EditDeleteButton-B2PjzFtp.js";import{a as Q}from"./index.prod-CisttSXz.js";import{A as I}from"./AdminServices-Crgje1Fu.js";import{a as y,n as X}from"./toast-C_V_NPJL.js";import{N as Y}from"./NotFound-DG_8Itz7.js";import{P as ee}from"./PageTitle-DUWCiaui.js";import{A as se}from"./AnimatedContent-DbKaf3qr.js";import"./iconBase-DTk8F31e.js";import"./ProductServices-CGXRs0W4.js";import"./index-0EvDzr9j.js";import"./useDisableForDemo-aTnQzb5-.js";import"./CouponServices-BvJiM6D0.js";import"./CurrencyServices-CicNeQxs.js";import"./SelectLanguageTwo-CQlbeojL.js";import"./spinner-CkndCogW.js";import"./useTranslationValue-d_-eYXcs.js";import"./_commonjs-dynamic-modules-LM44EJN2.js";const R=({id:o})=>{const{role:d}=k(),{mode:r}=N.useContext(s.WindmillContext),{register:n,handleSubmit:m,onSubmit:c,errors:i,adminInfo:g,imageUrl:u,setImageUrl:x,isSubmitting:h,selectedDate:t,setSelectedDate:p,accessedRoutes:b,setAccessedRoutes:S,handleSelectLanguage:a}=q(o),{t:l}=T();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:o?e.jsx(v,{register:n,handleSelectLanguage:a,title:l("UpdateStaff"),description:l("UpdateStaffdescription")}):e.jsx(v,{register:n,handleSelectLanguage:a,title:l("AddStaffTitle"),description:l("AddStaffdescription")})}),e.jsx(E.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsx(s.Card,{className:"overflow-y-scroll flex-grow scrollbar-hide w-full max-h-full",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:m(c),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Staff Image"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(V,{imageUrl:u,setImageUrl:x,folder:"admin",targetWidth:238,targetHeight:238})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Name"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(C,{required:!0,register:n,label:"Name",name:"name",type:"text",autoComplete:"username",placeholder:"Staff name"}),e.jsx(w,{errorName:i.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Email"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(C,{required:!0,register:n,label:"Email",name:"email",type:"text",autoComplete:"username",pattern:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,placeholder:"Email"}),e.jsx(w,{errorName:i.email})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Password"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[o?e.jsx(C,{register:n,label:"Password",name:"password",type:"password",autoComplete:"current-password",placeholder:"Password"}):e.jsx(C,{required:!0,register:n,label:"Password",name:"password",type:"password",autoComplete:"current-password",placeholder:"Password"}),e.jsx(w,{errorName:i.password})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Contact Number"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(C,{required:!0,register:n,label:"Contact Number",name:"phone",pattern:/^[+]?\d*$/,minLength:6,maxLength:15,type:"text",placeholder:"Phone number"}),e.jsx(w,{errorName:i.phone})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Joining Date"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(s.Input,{onChange:f=>p(f.target.value),label:"Joining Date",name:"joiningDate",value:t,type:"date",placeholder:l("StaffJoiningDate")}),e.jsx(w,{errorName:i.joiningDate})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Staff Role"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(J,{register:n,label:"Role",name:"role"}),e.jsx(w,{errorName:i.role})]})]}),d==="Admin"||d==="Super Admin"&&e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(j,{label:"Select Routes to given Access"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(_,{options:F,value:b,className:r,onChange:f=>S(f),labelledBy:"Select Coupon"})})]})]}),e.jsx(Z,{id:o,title:"Staff",zIndex:"z-5",isSubmitting:h})]})})})})]})},le=({id:o,status:d,option:r,staff:n})=>{const{setIsUpdate:m}=N.useContext(A),{role:c}=k(),i=async(g,u)=>{var x,h;if(!(c==="Super Admin"||c==="Admin"))return y("Only Super Admin and Admin can enable/disable any staff!");try{let t;d==="Active"?t="Inactive":t="Active";const p=await I.updateStaffStatus(g,{status:t});m(!0),X(p.message);return}catch(t){y(t?(h=(x=t==null?void 0:t.response)==null?void 0:x.data)==null?void 0:h.message:t==null?void 0:t.message)}};return e.jsx(e.Fragment,{children:e.jsx(Q,{onChange:()=>i(o),checked:d==="Active",className:"react-switch md:ml-0",uncheckedIcon:e.jsx("div",{style:{display:"flex",alignItems:"center",height:"100%",width:120,fontSize:14,color:"white",paddingRight:22,paddingTop:1}}),width:30,height:15,handleDiameter:13,offColor:"#E53E3E",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:73,height:"100%",fontSize:14,color:"white",paddingLeft:20,paddingTop:1}})})})},ae=({isOpen:o,onClose:d,staff:r,showingTranslateValue:n})=>{var m,c;return e.jsxs(s.Modal,{isOpen:o,onClose:d,children:[e.jsxs("h1",{className:"text-xl font-medium text-center pb-6 dark:text-gray-300",children:["List of route access for"," ",e.jsx("span",{className:"text-emerald-600",children:n(r==null?void 0:r.name)})]}),e.jsx(s.ModalBody,{children:((m=r==null?void 0:r.access_list)==null?void 0:m.length)>0?e.jsx("ol",{className:"list-disc pl-5",children:(c=r==null?void 0:r.access_list)==null?void 0:c.map((i,g)=>e.jsx("li",{className:"text-sm text-gray-700 dark:text-gray-300 capitalize",children:i},g))}):e.jsx("p",{className:"text-orange-500 py-10 text-lg text-center",children:"This staff not have any route access!"})}),e.jsx(s.ModalFooter,{className:"justify-end",children:e.jsx(s.Button,{className:"w-full sm:w-auto bg-red-400 text-white hover:bg-red-500",layout:"delete",onClick:d,children:"Close"})})]})},te=({staffs:o,lang:d})=>{const{title:r,serviceId:n,handleModalOpen:m,handleUpdate:c,isSubmitting:i,handleResetPassword:g}=O(),{showDateFormat:u,showingTranslateValue:x}=B(),[h,t]=N.useState(null),[p,b]=N.useState(!1),S=l=>{t(l),b(!0)},a=()=>{t(null),b(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(G,{id:n,title:r}),p&&e.jsx(ae,{staff:h,isOpen:p,onClose:a,showingTranslateValue:x}),e.jsx(D,{children:e.jsx(R,{id:n})}),e.jsx(s.TableBody,{children:o==null?void 0:o.map(l=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(s.Avatar,{className:"hidden mr-3 md:block bg-gray-50",src:l.image,alt:"staff"}),e.jsx("div",{children:e.jsx("h2",{className:"text-sm font-medium",children:x(l==null?void 0:l.name)})})]})}),e.jsxs(s.TableCell,{children:[e.jsx("span",{className:"text-sm",children:l.email})," "]}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm ",children:l.phone})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:u(l.joiningData)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm font-semibold",children:l==null?void 0:l.role})}),e.jsx(s.TableCell,{className:"text-center text-xs",children:e.jsx(H,{status:l.status})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(le,{id:l==null?void 0:l._id,staff:l,option:"staff",status:l.status})}),e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>S(l),className:"text-gray-400",children:e.jsx(W,{id:"view",Icon:z,title:"View Access Route",bgColor:"#059669"})}),e.jsx(K,{id:l._id,staff:l,isSubmitting:i,handleUpdate:c,handleModalOpen:m,handleResetPassword:g,title:x(l==null?void 0:l.name)})]})})]},l._id))})]})},Me=()=>{const{state:o}=N.useContext(P),{adminInfo:d}=o,{toggleDrawer:r,lang:n}=N.useContext(A),{data:m,loading:c,error:i}=L(()=>I.getAllStaff({email:d.email})),{userRef:g,setRole:u,totalResults:x,resultsPerPage:h,dataTable:t,serviceData:p,handleChangePage:b,handleSubmitUser:S}=U(m),{t:a}=T(),l=()=>{u(""),g.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsxs(ee,{children:[a("StaffPageTitle")," "]}),e.jsx(D,{children:e.jsx(R,{})}),e.jsx(se,{children:e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:S,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsxs("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx(s.Input,{ref:g,type:"search",name:"search",placeholder:a("StaffSearchBy")}),e.jsx("button",{type:"submit",className:"absolute right-0 top-0 mt-5 mr-1"})]}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(s.Select,{onChange:f=>u(f.target.value),children:[e.jsx("option",{value:"All",defaultValue:!0,hidden:!0,children:a("StaffRole")}),e.jsx("option",{value:"Admin",children:a("StaffRoleAdmin")}),e.jsx("option",{value:"Cashier",children:a("SelectCashiers")}),e.jsx("option",{value:"Super Admin",children:a("SelectSuperAdmin")})]})}),e.jsx("div",{className:"w-full md:w-56 lg:w-56 xl:w-56",children:e.jsxs(s.Button,{onClick:r,className:"w-full rounded-md h-12",children:[e.jsx("span",{className:"mr-3",children:e.jsx(M,{})}),a("AddStaff")]})}),e.jsxs("div",{className:"mt-2 md:mt-0 flex items-center xl:gap-x-4 gap-x-1 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full",children:e.jsx(s.Button,{layout:"outline",onClick:l,type:"reset",className:"px-4 md:py-1 py-3 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})}),c?e.jsx($,{row:12,col:7,width:163,height:20}):i?e.jsx("span",{className:"text-center mx-auto text-red-500",children:i}):(p==null?void 0:p.length)!==0?e.jsxs(s.TableContainer,{className:"mb-8 rounded-b-lg",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:a("StaffNameTbl")}),e.jsx(s.TableCell,{children:a("StaffEmailTbl")}),e.jsx(s.TableCell,{children:a("StaffContactTbl")}),e.jsx(s.TableCell,{children:a("StaffJoiningDateTbl")}),e.jsx(s.TableCell,{children:a("StaffRoleTbl")}),e.jsx(s.TableCell,{className:"text-center",children:a("OderStatusTbl")}),e.jsx(s.TableCell,{className:"text-center",children:a("PublishedTbl")}),e.jsx(s.TableCell,{className:"text-center",children:a("StaffActionsTbl")})]})}),e.jsx(te,{staffs:t,lang:n})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:x,resultsPerPage:h,onChange:b,label:"Table navigation"})})]}):e.jsx(Y,{title:"Sorry, There are no staff right now."})]})};export{Me as default};
