import{P as q,r as M,o as nn,W as an,j as W,g as wr,G as on,t as xt}from"./index-DD5OQCzb.js";import{a as he,b as pe,d as kt,e as Qt,c as Se}from"./_commonjs-dynamic-modules-LM44EJN2.js";import{y as Er,u as un,x as sn}from"./Layout-f_j_aP34.js";import{a as le,n as cn}from"./toast-C_V_NPJL.js";import{$ as fn}from"./index-0EvDzr9j.js";var ln=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function Ie(e,r){var t=dn(e);if(typeof t.path!="string"){var n=e.webkitRelativePath;Object.defineProperty(t,"path",{value:typeof r=="string"?r:typeof n=="string"&&n.length>0?n:e.name,writable:!1,configurable:!1,enumerable:!0})}return t}function dn(e){var r=e.name,t=r&&r.lastIndexOf(".")!==-1;if(t&&!e.type){var n=r.split(".").pop().toLowerCase(),a=ln.get(n);a&&Object.defineProperty(e,"type",{value:a,writable:!1,configurable:!1,enumerable:!0})}return e}var gn=[".DS_Store","Thumbs.db"];function hn(e){return he(this,void 0,void 0,function(){return pe(this,function(r){return Fe(e)&&pn(e.dataTransfer)?[2,yn(e.dataTransfer,e.type)]:vn(e)?[2,An(e)]:Array.isArray(e)&&e.every(function(t){return"getFile"in t&&typeof t.getFile=="function"})?[2,mn(e)]:[2,[]]})})}function pn(e){return Fe(e)}function vn(e){return Fe(e)&&Fe(e.target)}function Fe(e){return typeof e=="object"&&e!==null}function An(e){return st(e.target.files).map(function(r){return Ie(r)})}function mn(e){return he(this,void 0,void 0,function(){var r;return pe(this,function(t){switch(t.label){case 0:return[4,Promise.all(e.map(function(n){return n.getFile()}))];case 1:return r=t.sent(),[2,r.map(function(n){return Ie(n)})]}})})}function yn(e,r){return he(this,void 0,void 0,function(){var t,n;return pe(this,function(a){switch(a.label){case 0:return e.items?(t=st(e.items).filter(function(i){return i.kind==="file"}),r!=="drop"?[2,t]:[4,Promise.all(t.map(bn))]):[3,2];case 1:return n=a.sent(),[2,Mt(Dr(n))];case 2:return[2,Mt(st(e.files).map(function(i){return Ie(i)}))]}})})}function Mt(e){return e.filter(function(r){return gn.indexOf(r.name)===-1})}function st(e){if(e===null)return[];for(var r=[],t=0;t<e.length;t++){var n=e[t];r.push(n)}return r}function bn(e){if(typeof e.webkitGetAsEntry!="function")return Rt(e);var r=e.webkitGetAsEntry();return r&&r.isDirectory?Cr(r):Rt(e)}function Dr(e){return e.reduce(function(r,t){return kt(kt([],Qt(r),!1),Qt(Array.isArray(t)?Dr(t):[t]),!1)},[])}function Rt(e){var r=e.getAsFile();if(!r)return Promise.reject("".concat(e," is not a File"));var t=Ie(r);return Promise.resolve(t)}function In(e){return he(this,void 0,void 0,function(){return pe(this,function(r){return[2,e.isDirectory?Cr(e):wn(e)]})})}function Cr(e){var r=e.createReader();return new Promise(function(t,n){var a=[];function i(){var u=this;r.readEntries(function(o){return he(u,void 0,void 0,function(){var s,f,d;return pe(this,function(c){switch(c.label){case 0:if(o.length)return[3,5];c.label=1;case 1:return c.trys.push([1,3,,4]),[4,Promise.all(a)];case 2:return s=c.sent(),t(s),[3,4];case 3:return f=c.sent(),n(f),[3,4];case 4:return[3,6];case 5:d=Promise.all(o.map(In)),a.push(d),i(),c.label=6;case 6:return[2]}})})},function(o){n(o)})}i()})}function wn(e){return he(this,void 0,void 0,function(){return pe(this,function(r){return[2,new Promise(function(t,n){e.file(function(a){var i=Ie(a,e.fullPath);t(i)},function(a){n(a)})})]})})}var En=function(e,r){if(e&&r){var t=Array.isArray(r)?r:r.split(","),n=e.name||"",a=(e.type||"").toLowerCase(),i=a.replace(/\/.*$/,"");return t.some(function(u){var o=u.trim().toLowerCase();return o.charAt(0)==="."?n.toLowerCase().endsWith(o):o.endsWith("/*")?i===o.replace(/\/.*$/,""):a===o})}return!0};function Ft(e){return _n(e)||Cn(e)||Or(e)||Dn()}function Dn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cn(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _n(e){if(Array.isArray(e))return ct(e)}function Nt(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function jt(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Nt(Object(t),!0).forEach(function(n){_r(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Nt(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function _r(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function be(e,r){return Tn(e)||Sn(e,r)||Or(e,r)||On()}function On(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Or(e,r){if(e){if(typeof e=="string")return ct(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ct(e,r)}}function ct(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function Sn(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function Tn(e){if(Array.isArray(e))return e}var Bn="file-invalid-type",Pn="file-too-large",xn="file-too-small",kn="too-many-files",Qn=function(r){r=Array.isArray(r)&&r.length===1?r[0]:r;var t=Array.isArray(r)?"one of ".concat(r.join(", ")):r;return{code:Bn,message:"File type must be ".concat(t)}},Lt=function(r){return{code:Pn,message:"File is larger than ".concat(r," ").concat(r===1?"byte":"bytes")}},Ht=function(r){return{code:xn,message:"File is smaller than ".concat(r," ").concat(r===1?"byte":"bytes")}},Mn={code:kn,message:"Too many files"};function Sr(e,r){var t=e.type==="application/x-moz-file"||En(e,r);return[t,t?null:Qn(r)]}function Tr(e,r,t){if(oe(e.size))if(oe(r)&&oe(t)){if(e.size>t)return[!1,Lt(t)];if(e.size<r)return[!1,Ht(r)]}else{if(oe(r)&&e.size<r)return[!1,Ht(r)];if(oe(t)&&e.size>t)return[!1,Lt(t)]}return[!0,null]}function oe(e){return e!=null}function Rn(e){var r=e.files,t=e.accept,n=e.minSize,a=e.maxSize,i=e.multiple,u=e.maxFiles,o=e.validator;return!i&&r.length>1||i&&u>=1&&r.length>u?!1:r.every(function(s){var f=Sr(s,t),d=be(f,1),c=d[0],g=Tr(s,n,a),A=be(g,1),p=A[0],m=o?o(s):null;return c&&p&&!m})}function Ne(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Te(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(r){return r==="Files"||r==="application/x-moz-file"}):!!e.target&&!!e.target.files}function $t(e){e.preventDefault()}function Fn(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Nn(e){return e.indexOf("Edge/")!==-1}function jn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Fn(e)||Nn(e)}function ne(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return function(n){for(var a=arguments.length,i=new Array(a>1?a-1:0),u=1;u<a;u++)i[u-1]=arguments[u];return r.some(function(o){return!Ne(n)&&o&&o.apply(void 0,[n].concat(i)),Ne(n)})}}function Ln(){return"showOpenFilePicker"in window}function Hn(e){if(oe(e)){var r=Object.entries(e).filter(function(t){var n=be(t,2),a=n[0],i=n[1],u=!0;return Br(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),u=!1),(!Array.isArray(i)||!i.every(Pr))&&(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),u=!1),u}).reduce(function(t,n){var a=be(n,2),i=a[0],u=a[1];return jt(jt({},t),{},_r({},i,u))},{});return[{description:"Files",accept:r}]}return e}function $n(e){if(oe(e))return Object.entries(e).reduce(function(r,t){var n=be(t,2),a=n[0],i=n[1];return[].concat(Ft(r),[a],Ft(i))},[]).filter(function(r){return Br(r)||Pr(r)}).join(",")}function Un(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Gn(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function Br(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||/\w+\/[-+.\w]+/g.test(e)}function Pr(e){return/^.*\.[\w]+$/.test(e)}var zn=["children"],Wn=["open"],qn=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Yn=["refKey","onChange","onClick"];function Xn(e){return Vn(e)||Jn(e)||xr(e)||Kn()}function Kn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Jn(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Vn(e){if(Array.isArray(e))return ft(e)}function et(e,r){return ta(e)||ea(e,r)||xr(e,r)||Zn()}function Zn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xr(e,r){if(e){if(typeof e=="string")return ft(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ft(e,r)}}function ft(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function ea(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function ta(e){if(Array.isArray(e))return e}function Ut(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function Y(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Ut(Object(t),!0).forEach(function(n){lt(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ut(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function lt(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function je(e,r){if(e==null)return{};var t=ra(e,r),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function ra(e,r){if(e==null)return{};var t={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(r.indexOf(a)>=0)&&(t[a]=e[a]);return t}var bt=M.forwardRef(function(e,r){var t=e.children,n=je(e,zn),a=Qr(n),i=a.open,u=je(a,Wn);return M.useImperativeHandle(r,function(){return{open:i}},[i]),nn.createElement(M.Fragment,null,t(Y(Y({},u),{},{open:i})))});bt.displayName="Dropzone";var kr={disabled:!1,getFilesFromEvent:hn,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!0,autoFocus:!1};bt.defaultProps=kr;bt.propTypes={children:q.func,accept:q.objectOf(q.arrayOf(q.string)),multiple:q.bool,preventDropOnDocument:q.bool,noClick:q.bool,noKeyboard:q.bool,noDrag:q.bool,noDragEventsBubbling:q.bool,minSize:q.number,maxSize:q.number,maxFiles:q.number,disabled:q.bool,getFilesFromEvent:q.func,onFileDialogCancel:q.func,onFileDialogOpen:q.func,useFsAccessApi:q.bool,autoFocus:q.bool,onDragEnter:q.func,onDragLeave:q.func,onDragOver:q.func,onDrop:q.func,onDropAccepted:q.func,onDropRejected:q.func,onError:q.func,validator:q.func};var dt={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Qr(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=Y(Y({},kr),e),t=r.accept,n=r.disabled,a=r.getFilesFromEvent,i=r.maxSize,u=r.minSize,o=r.multiple,s=r.maxFiles,f=r.onDragEnter,d=r.onDragLeave,c=r.onDragOver,g=r.onDrop,A=r.onDropAccepted,p=r.onDropRejected,m=r.onFileDialogCancel,y=r.onFileDialogOpen,I=r.useFsAccessApi,E=r.autoFocus,_=r.preventDropOnDocument,b=r.noClick,P=r.noKeyboard,x=r.noDrag,h=r.noDragEventsBubbling,l=r.onError,D=r.validator,B=M.useMemo(function(){return $n(t)},[t]),C=M.useMemo(function(){return Hn(t)},[t]),R=M.useMemo(function(){return typeof y=="function"?y:Gt},[y]),Q=M.useMemo(function(){return typeof m=="function"?m:Gt},[m]),$=M.useRef(null),F=M.useRef(null),v=M.useReducer(na,dt),S=et(v,2),w=S[0],O=S[1],T=w.isFocused,U=w.isFileDialogActive,N=M.useRef(typeof window<"u"&&window.isSecureContext&&I&&Ln()),L=function(){!N.current&&U&&setTimeout(function(){if(F.current){var j=F.current.files;j.length||(O({type:"closeDialog"}),Q())}},300)};M.useEffect(function(){return window.addEventListener("focus",L,!1),function(){window.removeEventListener("focus",L,!1)}},[F,U,Q,N]);var z=M.useRef([]),X=function(j){$.current&&$.current.contains(j.target)||(j.preventDefault(),z.current=[])};M.useEffect(function(){return _&&(document.addEventListener("dragover",$t,!1),document.addEventListener("drop",X,!1)),function(){_&&(document.removeEventListener("dragover",$t),document.removeEventListener("drop",X))}},[$,_]),M.useEffect(function(){return!n&&E&&$.current&&$.current.focus(),function(){}},[$,E,n]);var ee=M.useCallback(function(k){l?l(k):console.error(k)},[l]),ie=M.useCallback(function(k){k.preventDefault(),k.persist(),_e(k),z.current=[].concat(Xn(z.current),[k.target]),Te(k)&&Promise.resolve(a(k)).then(function(j){if(!(Ne(k)&&!h)){var K=j.length,J=K>0&&Rn({files:j,accept:B,minSize:u,maxSize:i,multiple:o,maxFiles:s,validator:D}),Z=K>0&&!J;O({isDragAccept:J,isDragReject:Z,isDragActive:!0,type:"setDraggedFiles"}),f&&f(k)}}).catch(function(j){return ee(j)})},[a,f,ee,h,B,u,i,o,s,D]),we=M.useCallback(function(k){k.preventDefault(),k.persist(),_e(k);var j=Te(k);if(j&&k.dataTransfer)try{k.dataTransfer.dropEffect="copy"}catch{}return j&&c&&c(k),!1},[c,h]),Ot=M.useCallback(function(k){k.preventDefault(),k.persist(),_e(k);var j=z.current.filter(function(J){return $.current&&$.current.contains(J)}),K=j.indexOf(k.target);K!==-1&&j.splice(K,1),z.current=j,!(j.length>0)&&(O({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Te(k)&&d&&d(k))},[$,d,h]),Ee=M.useCallback(function(k,j){var K=[],J=[];k.forEach(function(Z){var Ae=Sr(Z,B),fe=et(Ae,2),qe=fe[0],Ye=fe[1],Xe=Tr(Z,u,i),Oe=et(Xe,2),Ke=Oe[0],Je=Oe[1],Ve=D?D(Z):null;if(qe&&Ke&&!Ve)K.push(Z);else{var Ze=[Ye,Je];Ve&&(Ze=Ze.concat(Ve)),J.push({file:Z,errors:Ze.filter(function(rn){return rn})})}}),(!o&&K.length>1||o&&s>=1&&K.length>s)&&(K.forEach(function(Z){J.push({file:Z,errors:[Mn]})}),K.splice(0)),O({acceptedFiles:K,fileRejections:J,type:"setFiles"}),g&&g(K,J,j),J.length>0&&p&&p(J,j),K.length>0&&A&&A(K,j)},[O,o,B,u,i,s,g,A,p,D]),De=M.useCallback(function(k){k.preventDefault(),k.persist(),_e(k),z.current=[],Te(k)&&Promise.resolve(a(k)).then(function(j){Ne(k)&&!h||Ee(j,k)}).catch(function(j){return ee(j)}),O({type:"reset"})},[a,Ee,ee,h]),se=M.useCallback(function(){if(N.current){O({type:"openDialog"}),R();var k={multiple:o,types:C};window.showOpenFilePicker(k).then(function(j){return a(j)}).then(function(j){Ee(j,null),O({type:"closeDialog"})}).catch(function(j){Un(j)?(Q(j),O({type:"closeDialog"})):Gn(j)?(N.current=!1,F.current?(F.current.value=null,F.current.click()):ee(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ee(j)});return}F.current&&(O({type:"openDialog"}),R(),F.current.value=null,F.current.click())},[O,R,Q,I,Ee,ee,C,o]),St=M.useCallback(function(k){!$.current||!$.current.isEqualNode(k.target)||(k.key===" "||k.key==="Enter"||k.keyCode===32||k.keyCode===13)&&(k.preventDefault(),se())},[$,se]),Tt=M.useCallback(function(){O({type:"focus"})},[]),Bt=M.useCallback(function(){O({type:"blur"})},[]),Pt=M.useCallback(function(){b||(jn()?setTimeout(se,0):se())},[b,se]),ce=function(j){return n?null:j},We=function(j){return P?null:ce(j)},Ce=function(j){return x?null:ce(j)},_e=function(j){h&&j.stopPropagation()},Zr=M.useMemo(function(){return function(){var k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},j=k.refKey,K=j===void 0?"ref":j,J=k.role,Z=k.onKeyDown,Ae=k.onFocus,fe=k.onBlur,qe=k.onClick,Ye=k.onDragEnter,Xe=k.onDragOver,Oe=k.onDragLeave,Ke=k.onDrop,Je=je(k,qn);return Y(Y(lt({onKeyDown:We(ne(Z,St)),onFocus:We(ne(Ae,Tt)),onBlur:We(ne(fe,Bt)),onClick:ce(ne(qe,Pt)),onDragEnter:Ce(ne(Ye,ie)),onDragOver:Ce(ne(Xe,we)),onDragLeave:Ce(ne(Oe,Ot)),onDrop:Ce(ne(Ke,De)),role:typeof J=="string"&&J!==""?J:"presentation"},K,$),!n&&!P?{tabIndex:0}:{}),Je)}},[$,St,Tt,Bt,Pt,ie,we,Ot,De,P,x,n]),en=M.useCallback(function(k){k.stopPropagation()},[]),tn=M.useMemo(function(){return function(){var k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},j=k.refKey,K=j===void 0?"ref":j,J=k.onChange,Z=k.onClick,Ae=je(k,Yn),fe=lt({accept:B,multiple:o,type:"file",style:{display:"none"},onChange:ce(ne(J,De)),onClick:ce(ne(Z,en)),tabIndex:-1},K,F);return Y(Y({},fe),Ae)}},[F,t,o,De,n]);return Y(Y({},w),{},{isFocused:T&&!n,getRootProps:Zr,getInputProps:tn,rootRef:$,inputRef:F,open:ce(se)})}function na(e,r){switch(r.type){case"focus":return Y(Y({},e),{},{isFocused:!0});case"blur":return Y(Y({},e),{},{isFocused:!1});case"openDialog":return Y(Y({},dt),{},{isFileDialogActive:!0});case"closeDialog":return Y(Y({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return Y(Y({},e),{},{isDragActive:r.isDragActive,isDragAccept:r.isDragAccept,isDragReject:r.isDragReject});case"setFiles":return Y(Y({},e),{},{acceptedFiles:r.acceptedFiles,fileRejections:r.fileRejections});case"reset":return Y({},dt);default:return e}}function Gt(){}var Mr=M.createContext({dragDropManager:void 0}),re;(function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"})(re||(re={}));function H(e,r){for(var t=arguments.length,n=new Array(t>2?t-2:0),a=2;a<t;a++)n[a-2]=arguments[a];if(!e){var i;if(r===void 0)i=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=0;i=new Error(r.replace(/%s/g,function(){return n[u++]})),i.name="Invariant Violation"}throw i.framesToPop=1,i}}var It="dnd-core/INIT_COORDS",He="dnd-core/BEGIN_DRAG",wt="dnd-core/PUBLISH_DRAG_SOURCE",$e="dnd-core/HOVER",Ue="dnd-core/DROP",Ge="dnd-core/END_DRAG";function zt(e,r){return{type:It,payload:{sourceClientOffset:r||null,clientOffset:e||null}}}function xe(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?xe=function(t){return typeof t}:xe=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xe(e)}function aa(e,r,t){return r.split(".").reduce(function(n,a){return n&&n[a]?n[a]:t||null},e)}function ia(e,r){return e.filter(function(t){return t!==r})}function Rr(e){return xe(e)==="object"}function oa(e,r){var t=new Map,n=function(u){t.set(u,t.has(u)?t.get(u)+1:1)};e.forEach(n),r.forEach(n);var a=[];return t.forEach(function(i,u){i===1&&a.push(u)}),a}function ua(e,r){return e.filter(function(t){return r.indexOf(t)>-1})}var sa={type:It,payload:{clientOffset:null,sourceClientOffset:null}};function ca(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{publishSource:!0},a=n.publishSource,i=a===void 0?!0:a,u=n.clientOffset,o=n.getSourceClientOffset,s=e.getMonitor(),f=e.getRegistry();e.dispatch(zt(u)),fa(t,s,f);var d=ga(t,s);if(d===null){e.dispatch(sa);return}var c=null;if(u){if(!o)throw new Error("getSourceClientOffset must be defined");la(o),c=o(d)}e.dispatch(zt(u,c));var g=f.getSource(d),A=g.beginDrag(s,d);if(A!=null){da(A),f.pinSource(d);var p=f.getSourceType(d);return{type:He,payload:{itemType:p,item:A,sourceId:d,clientOffset:u||null,sourceClientOffset:c||null,isSourcePublic:!!i}}}}}function fa(e,r,t){H(!r.isDragging(),"Cannot call beginDrag while dragging."),e.forEach(function(n){H(t.getSource(n),"Expected sourceIds to be registered.")})}function la(e){H(typeof e=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function da(e){H(Rr(e),"Item must be an object.")}function ga(e,r){for(var t=null,n=e.length-1;n>=0;n--)if(r.canDragSource(e[n])){t=e[n];break}return t}function ha(e){return function(){var t=e.getMonitor();if(t.isDragging())return{type:wt}}}function gt(e,r){return r===null?e===null:Array.isArray(e)?e.some(function(t){return t===r}):e===r}function pa(e){return function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=n.clientOffset;va(t);var i=t.slice(0),u=e.getMonitor(),o=e.getRegistry();Aa(i,u,o);var s=u.getItemType();return ma(i,o,s),ya(i,u,o),{type:$e,payload:{targetIds:i,clientOffset:a||null}}}}function va(e){H(Array.isArray(e),"Expected targetIds to be an array.")}function Aa(e,r,t){H(r.isDragging(),"Cannot call hover while not dragging."),H(!r.didDrop(),"Cannot call hover after drop.");for(var n=0;n<e.length;n++){var a=e[n];H(e.lastIndexOf(a)===n,"Expected targetIds to be unique in the passed array.");var i=t.getTarget(a);H(i,"Expected targetIds to be registered.")}}function ma(e,r,t){for(var n=e.length-1;n>=0;n--){var a=e[n],i=r.getTargetType(a);gt(i,t)||e.splice(n,1)}}function ya(e,r,t){e.forEach(function(n){var a=t.getTarget(n);a.hover(r,n)})}function Wt(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function qt(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Wt(Object(t),!0).forEach(function(n){ba(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Wt(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function ba(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function Ia(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.getMonitor(),a=e.getRegistry();wa(n);var i=Ca(n);i.forEach(function(u,o){var s=Ea(u,o,a,n),f={type:Ue,payload:{dropResult:qt(qt({},t),s)}};e.dispatch(f)})}}function wa(e){H(e.isDragging(),"Cannot call drop while not dragging."),H(!e.didDrop(),"Cannot call drop twice during one drag operation.")}function Ea(e,r,t,n){var a=t.getTarget(e),i=a?a.drop(n,e):void 0;return Da(i),typeof i>"u"&&(i=r===0?{}:n.getDropResult()),i}function Da(e){H(typeof e>"u"||Rr(e),"Drop result must either be an object or undefined.")}function Ca(e){var r=e.getTargetIds().filter(e.canDropOnTarget,e);return r.reverse(),r}function _a(e){return function(){var t=e.getMonitor(),n=e.getRegistry();Oa(t);var a=t.getSourceId();if(a!=null){var i=n.getSource(a,!0);i.endDrag(t,a),n.unpinSource()}return{type:Ge}}}function Oa(e){H(e.isDragging(),"Cannot call endDrag while not dragging.")}function Sa(e){return{beginDrag:ca(e),publishDragSource:ha(e),hover:pa(e),drop:Ia(e),endDrag:_a(e)}}function Ta(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Ba(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Pa(e,r,t){return r&&Ba(e.prototype,r),e}function me(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var xa=function(){function e(r,t){var n=this;Ta(this,e),me(this,"store",void 0),me(this,"monitor",void 0),me(this,"backend",void 0),me(this,"isSetUp",!1),me(this,"handleRefCountChange",function(){var a=n.store.getState().refCount>0;n.backend&&(a&&!n.isSetUp?(n.backend.setup(),n.isSetUp=!0):!a&&n.isSetUp&&(n.backend.teardown(),n.isSetUp=!1))}),this.store=r,this.monitor=t,r.subscribe(this.handleRefCountChange)}return Pa(e,[{key:"receiveBackend",value:function(t){this.backend=t}},{key:"getMonitor",value:function(){return this.monitor}},{key:"getBackend",value:function(){return this.backend}},{key:"getRegistry",value:function(){return this.monitor.registry}},{key:"getActions",value:function(){var t=this,n=this.store.dispatch;function a(u){return function(){for(var o=arguments.length,s=new Array(o),f=0;f<o;f++)s[f]=arguments[f];var d=u.apply(t,s);typeof d<"u"&&n(d)}}var i=Sa(this);return Object.keys(i).reduce(function(u,o){var s=i[o];return u[o]=a(s),u},{})}},{key:"dispatch",value:function(t){this.store.dispatch(t)}}]),e}(),ka=function(r,t){return r===t};function Qa(e,r){return!e&&!r?!0:!e||!r?!1:e.x===r.x&&e.y===r.y}function Ma(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ka;if(e.length!==r.length)return!1;for(var n=0;n<e.length;++n)if(!t(e[n],r[n]))return!1;return!0}function Yt(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function Xt(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Yt(Object(t),!0).forEach(function(n){Ra(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Yt(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Ra(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var Kt={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function Fa(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Kt,r=arguments.length>1?arguments[1]:void 0,t=r.payload;switch(r.type){case It:case He:return{initialSourceClientOffset:t.sourceClientOffset,initialClientOffset:t.clientOffset,clientOffset:t.clientOffset};case $e:return Qa(e.clientOffset,t.clientOffset)?e:Xt(Xt({},e),{},{clientOffset:t.clientOffset});case Ge:case Ue:return Kt;default:return e}}var Et="dnd-core/ADD_SOURCE",Dt="dnd-core/ADD_TARGET",Ct="dnd-core/REMOVE_SOURCE",ze="dnd-core/REMOVE_TARGET";function Na(e){return{type:Et,payload:{sourceId:e}}}function ja(e){return{type:Dt,payload:{targetId:e}}}function La(e){return{type:Ct,payload:{sourceId:e}}}function Ha(e){return{type:ze,payload:{targetId:e}}}function Jt(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function te(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Jt(Object(t),!0).forEach(function(n){$a(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Jt(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function $a(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var Ua={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function Ga(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ua,r=arguments.length>1?arguments[1]:void 0,t=r.payload;switch(r.type){case He:return te(te({},e),{},{itemType:t.itemType,item:t.item,sourceId:t.sourceId,isSourcePublic:t.isSourcePublic,dropResult:null,didDrop:!1});case wt:return te(te({},e),{},{isSourcePublic:!0});case $e:return te(te({},e),{},{targetIds:t.targetIds});case ze:return e.targetIds.indexOf(t.targetId)===-1?e:te(te({},e),{},{targetIds:ia(e.targetIds,t.targetId)});case Ue:return te(te({},e),{},{dropResult:t.dropResult,didDrop:!0,targetIds:[]});case Ge:return te(te({},e),{},{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}function za(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,r=arguments.length>1?arguments[1]:void 0;switch(r.type){case Et:case Dt:return e+1;case Ct:case ze:return e-1;default:return e}}var Le=[],_t=[];Le.__IS_NONE__=!0;_t.__IS_ALL__=!0;function Wa(e,r){if(e===Le)return!1;if(e===_t||typeof r>"u")return!0;var t=ua(r,e);return t.length>0}function qa(){var e=arguments.length>1?arguments[1]:void 0;switch(e.type){case $e:break;case Et:case Dt:case ze:case Ct:return Le;case He:case wt:case Ge:case Ue:default:return _t}var r=e.payload,t=r.targetIds,n=t===void 0?[]:t,a=r.prevTargetIds,i=a===void 0?[]:a,u=oa(n,i),o=u.length>0||!Ma(n,i);if(!o)return Le;var s=i[i.length-1],f=n[n.length-1];return s!==f&&(s&&u.push(s),f&&u.push(f)),u}function Ya(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return e+1}function Vt(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function Zt(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Vt(Object(t),!0).forEach(function(n){Xa(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Vt(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Xa(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function Ka(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;return{dirtyHandlerIds:qa(e.dirtyHandlerIds,{type:r.type,payload:Zt(Zt({},r.payload),{},{prevTargetIds:aa(e,"dragOperation.targetIds",[])})}),dragOffset:Fa(e.dragOffset,r),refCount:za(e.refCount,r),dragOperation:Ga(e.dragOperation,r),stateId:Ya(e.stateId)}}function Ja(e,r){return{x:e.x+r.x,y:e.y+r.y}}function Fr(e,r){return{x:e.x-r.x,y:e.y-r.y}}function Va(e){var r=e.clientOffset,t=e.initialClientOffset,n=e.initialSourceClientOffset;return!r||!t||!n?null:Fr(Ja(r,n),t)}function Za(e){var r=e.clientOffset,t=e.initialClientOffset;return!r||!t?null:Fr(r,t)}function ei(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ti(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ri(e,r,t){return r&&ti(e.prototype,r),e}function er(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var ni=function(){function e(r,t){ei(this,e),er(this,"store",void 0),er(this,"registry",void 0),this.store=r,this.registry=t}return ri(e,[{key:"subscribeToStateChange",value:function(t){var n=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{handlerIds:void 0},i=a.handlerIds;H(typeof t=="function","listener must be a function."),H(typeof i>"u"||Array.isArray(i),"handlerIds, when specified, must be an array of strings.");var u=this.store.getState().stateId,o=function(){var f=n.store.getState(),d=f.stateId;try{var c=d===u||d===u+1&&!Wa(f.dirtyHandlerIds,i);c||t()}finally{u=d}};return this.store.subscribe(o)}},{key:"subscribeToOffsetChange",value:function(t){var n=this;H(typeof t=="function","listener must be a function.");var a=this.store.getState().dragOffset,i=function(){var o=n.store.getState().dragOffset;o!==a&&(a=o,t())};return this.store.subscribe(i)}},{key:"canDragSource",value:function(t){if(!t)return!1;var n=this.registry.getSource(t);return H(n,"Expected to find a valid source. sourceId=".concat(t)),this.isDragging()?!1:n.canDrag(this,t)}},{key:"canDropOnTarget",value:function(t){if(!t)return!1;var n=this.registry.getTarget(t);if(H(n,"Expected to find a valid target. targetId=".concat(t)),!this.isDragging()||this.didDrop())return!1;var a=this.registry.getTargetType(t),i=this.getItemType();return gt(a,i)&&n.canDrop(this,t)}},{key:"isDragging",value:function(){return!!this.getItemType()}},{key:"isDraggingSource",value:function(t){if(!t)return!1;var n=this.registry.getSource(t,!0);if(H(n,"Expected to find a valid source. sourceId=".concat(t)),!this.isDragging()||!this.isSourcePublic())return!1;var a=this.registry.getSourceType(t),i=this.getItemType();return a!==i?!1:n.isDragging(this,t)}},{key:"isOverTarget",value:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{shallow:!1};if(!t)return!1;var a=n.shallow;if(!this.isDragging())return!1;var i=this.registry.getTargetType(t),u=this.getItemType();if(u&&!gt(i,u))return!1;var o=this.getTargetIds();if(!o.length)return!1;var s=o.indexOf(t);return a?s===o.length-1:s>-1}},{key:"getItemType",value:function(){return this.store.getState().dragOperation.itemType}},{key:"getItem",value:function(){return this.store.getState().dragOperation.item}},{key:"getSourceId",value:function(){return this.store.getState().dragOperation.sourceId}},{key:"getTargetIds",value:function(){return this.store.getState().dragOperation.targetIds}},{key:"getDropResult",value:function(){return this.store.getState().dragOperation.dropResult}},{key:"didDrop",value:function(){return this.store.getState().dragOperation.didDrop}},{key:"isSourcePublic",value:function(){return!!this.store.getState().dragOperation.isSourcePublic}},{key:"getInitialClientOffset",value:function(){return this.store.getState().dragOffset.initialClientOffset}},{key:"getInitialSourceClientOffset",value:function(){return this.store.getState().dragOffset.initialSourceClientOffset}},{key:"getClientOffset",value:function(){return this.store.getState().dragOffset.clientOffset}},{key:"getSourceClientOffset",value:function(){return Va(this.store.getState().dragOffset)}},{key:"getDifferenceFromInitialOffset",value:function(){return Za(this.store.getState().dragOffset)}}]),e}(),ai=0;function ii(){return ai++}function ke(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ke=function(t){return typeof t}:ke=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ke(e)}function oi(e){H(typeof e.canDrag=="function","Expected canDrag to be a function."),H(typeof e.beginDrag=="function","Expected beginDrag to be a function."),H(typeof e.endDrag=="function","Expected endDrag to be a function.")}function ui(e){H(typeof e.canDrop=="function","Expected canDrop to be a function."),H(typeof e.hover=="function","Expected hover to be a function."),H(typeof e.drop=="function","Expected beginDrag to be a function.")}function ht(e,r){if(r&&Array.isArray(e)){e.forEach(function(t){return ht(t,!1)});return}H(typeof e=="string"||ke(e)==="symbol",r?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}const tr=typeof global<"u"?global:self,Nr=tr.MutationObserver||tr.WebKitMutationObserver;function jr(e){return function(){const t=setTimeout(a,0),n=setInterval(a,50);function a(){clearTimeout(t),clearInterval(n),e()}}}function si(e){let r=1;const t=new Nr(e),n=document.createTextNode("");return t.observe(n,{characterData:!0}),function(){r=-r,n.data=r}}const ci=typeof Nr=="function"?si:jr;class fi{enqueueTask(r){const{queue:t,requestFlush:n}=this;t.length||(n(),this.flushing=!0),t[t.length]=r}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:r}=this;for(;this.index<r.length;){const t=this.index;if(this.index++,r[t].call(),this.index>this.capacity){for(let n=0,a=r.length-this.index;n<a;n++)r[n]=r[n+this.index];r.length-=this.index,this.index=0}}r.length=0,this.index=0,this.flushing=!1},this.registerPendingError=r=>{this.pendingErrors.push(r),this.requestErrorThrow()},this.requestFlush=ci(this.flush),this.requestErrorThrow=jr(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class li{call(){try{this.task&&this.task()}catch(r){this.onError(r)}finally{this.task=null,this.release(this)}}constructor(r,t){this.onError=r,this.release=t,this.task=null}}class di{create(r){const t=this.freeTasks,n=t.length?t.pop():new li(this.onError,a=>t[t.length]=a);return n.task=r,n}constructor(r){this.onError=r,this.freeTasks=[]}}const Lr=new fi,gi=new di(Lr.registerPendingError);function hi(e){Lr.enqueueTask(gi.create(e))}function pi(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function vi(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ai(e,r,t){return r&&vi(e.prototype,r),e}function de(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function mi(e,r){return wi(e)||Ii(e,r)||bi(e,r)||yi()}function yi(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bi(e,r){if(e){if(typeof e=="string")return rr(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return rr(e,r)}}function rr(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function Ii(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function wi(e){if(Array.isArray(e))return e}function Ei(e){var r=ii().toString();switch(e){case re.SOURCE:return"S".concat(r);case re.TARGET:return"T".concat(r);default:throw new Error("Unknown Handler Role: ".concat(e))}}function nr(e){switch(e[0]){case"S":return re.SOURCE;case"T":return re.TARGET;default:H(!1,"Cannot parse handler ID: ".concat(e))}}function ar(e,r){var t=e.entries(),n=!1;do{var a=t.next(),i=a.done,u=mi(a.value,2),o=u[1];if(o===r)return!0;n=!!i}while(!n);return!1}var Di=function(){function e(r){pi(this,e),de(this,"types",new Map),de(this,"dragSources",new Map),de(this,"dropTargets",new Map),de(this,"pinnedSourceId",null),de(this,"pinnedSource",null),de(this,"store",void 0),this.store=r}return Ai(e,[{key:"addSource",value:function(t,n){ht(t),oi(n);var a=this.addHandler(re.SOURCE,t,n);return this.store.dispatch(Na(a)),a}},{key:"addTarget",value:function(t,n){ht(t,!0),ui(n);var a=this.addHandler(re.TARGET,t,n);return this.store.dispatch(ja(a)),a}},{key:"containsHandler",value:function(t){return ar(this.dragSources,t)||ar(this.dropTargets,t)}},{key:"getSource",value:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;H(this.isSourceId(t),"Expected a valid source ID.");var a=n&&t===this.pinnedSourceId,i=a?this.pinnedSource:this.dragSources.get(t);return i}},{key:"getTarget",value:function(t){return H(this.isTargetId(t),"Expected a valid target ID."),this.dropTargets.get(t)}},{key:"getSourceType",value:function(t){return H(this.isSourceId(t),"Expected a valid source ID."),this.types.get(t)}},{key:"getTargetType",value:function(t){return H(this.isTargetId(t),"Expected a valid target ID."),this.types.get(t)}},{key:"isSourceId",value:function(t){var n=nr(t);return n===re.SOURCE}},{key:"isTargetId",value:function(t){var n=nr(t);return n===re.TARGET}},{key:"removeSource",value:function(t){var n=this;H(this.getSource(t),"Expected an existing source."),this.store.dispatch(La(t)),hi(function(){n.dragSources.delete(t),n.types.delete(t)})}},{key:"removeTarget",value:function(t){H(this.getTarget(t),"Expected an existing target."),this.store.dispatch(Ha(t)),this.dropTargets.delete(t),this.types.delete(t)}},{key:"pinSource",value:function(t){var n=this.getSource(t);H(n,"Expected an existing source."),this.pinnedSourceId=t,this.pinnedSource=n}},{key:"unpinSource",value:function(){H(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}},{key:"addHandler",value:function(t,n,a){var i=Ei(t);return this.types.set(i,n),t===re.SOURCE?this.dragSources.set(i,a):t===re.TARGET&&this.dropTargets.set(i,a),i}}]),e}();function Ci(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,a=_i(n),i=new ni(a,new Di(a)),u=new xa(a,i),o=e(u,r,t);return u.receiveBackend(o),u}function _i(e){var r=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return an(Ka,e&&r&&r({name:"dnd-core",instanceId:"dnd-core"}))}var Oi=["children"];function Si(e,r){return xi(e)||Pi(e,r)||Bi(e,r)||Ti()}function Ti(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Bi(e,r){if(e){if(typeof e=="string")return ir(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ir(e,r)}}function ir(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function Pi(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function xi(e){if(Array.isArray(e))return e}function ki(e,r){if(e==null)return{};var t=Qi(e,r),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(t[n]=e[n])}return t}function Qi(e,r){if(e==null)return{};var t={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(r.indexOf(a)>=0)&&(t[a]=e[a]);return t}var or=0,Qe=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__"),Mi=M.memo(function(r){var t=r.children,n=ki(r,Oi),a=Ri(n),i=Si(a,2),u=i[0],o=i[1];return M.useEffect(function(){if(o){var s=Hr();return++or,function(){--or===0&&(s[Qe]=null)}}},[]),W.jsx(Mr.Provider,Object.assign({value:u},{children:t}),void 0)});function Ri(e){if("manager"in e){var r={dragDropManager:e.manager};return[r,!1]}var t=Fi(e.backend,e.context,e.options,e.debugMode),n=!e.context;return[t,n]}function Fi(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Hr(),t=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,a=r;return a[Qe]||(a[Qe]={dragDropManager:Ci(e,r,t,n)}),a[Qe]}function Hr(){return typeof global<"u"?global:window}function Ni(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ji(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Li(e,r,t){return r&&ji(e.prototype,r),e}function ur(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var tt=!1,rt=!1,Hi=function(){function e(r){Ni(this,e),ur(this,"internalMonitor",void 0),ur(this,"sourceId",null),this.internalMonitor=r.getMonitor()}return Li(e,[{key:"receiveHandlerId",value:function(t){this.sourceId=t}},{key:"getHandlerId",value:function(){return this.sourceId}},{key:"canDrag",value:function(){H(!tt,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return tt=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{tt=!1}}},{key:"isDragging",value:function(){if(!this.sourceId)return!1;H(!rt,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return rt=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{rt=!1}}},{key:"subscribeToStateChange",value:function(t,n){return this.internalMonitor.subscribeToStateChange(t,n)}},{key:"isDraggingSource",value:function(t){return this.internalMonitor.isDraggingSource(t)}},{key:"isOverTarget",value:function(t,n){return this.internalMonitor.isOverTarget(t,n)}},{key:"getTargetIds",value:function(){return this.internalMonitor.getTargetIds()}},{key:"isSourcePublic",value:function(){return this.internalMonitor.isSourcePublic()}},{key:"getSourceId",value:function(){return this.internalMonitor.getSourceId()}},{key:"subscribeToOffsetChange",value:function(t){return this.internalMonitor.subscribeToOffsetChange(t)}},{key:"canDragSource",value:function(t){return this.internalMonitor.canDragSource(t)}},{key:"canDropOnTarget",value:function(t){return this.internalMonitor.canDropOnTarget(t)}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}]),e}();function $i(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Ui(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Gi(e,r,t){return r&&Ui(e.prototype,r),e}function sr(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var nt=!1,zi=function(){function e(r){$i(this,e),sr(this,"internalMonitor",void 0),sr(this,"targetId",null),this.internalMonitor=r.getMonitor()}return Gi(e,[{key:"receiveHandlerId",value:function(t){this.targetId=t}},{key:"getHandlerId",value:function(){return this.targetId}},{key:"subscribeToStateChange",value:function(t,n){return this.internalMonitor.subscribeToStateChange(t,n)}},{key:"canDrop",value:function(){if(!this.targetId)return!1;H(!nt,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return nt=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{nt=!1}}},{key:"isOver",value:function(t){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,t):!1}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}]),e}();function Wi(e){if(typeof e.type!="string"){var r=e.type.displayName||e.type.name||"the component";throw new Error("Only native element nodes can now be passed to React DnD connectors."+"You can either wrap ".concat(r," into a <div>, or turn it into a ")+"drag source or a drop target itself.")}}function qi(e){return function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(!M.isValidElement(r)){var n=r;return e(n,t),n}var a=r;Wi(a);var i=t?function(u){return e(u,t)}:e;return Yi(a,i)}}function $r(e){var r={};return Object.keys(e).forEach(function(t){var n=e[t];if(t.endsWith("Ref"))r[t]=e[t];else{var a=qi(n);r[t]=function(){return a}}}),r}function cr(e,r){typeof e=="function"?e(r):e.current=r}function Yi(e,r){var t=e.ref;return H(typeof t!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),t?M.cloneElement(e,{ref:function(a){cr(t,a),cr(r,a)}}):M.cloneElement(e,{ref:r})}function Me(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Me=function(t){return typeof t}:Me=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Me(e)}function pt(e){return e!==null&&Me(e)==="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function vt(e,r,t,n){var a=void 0;if(a!==void 0)return!!a;if(e===r)return!0;if(typeof e!="object"||!e||typeof r!="object"||!r)return!1;var i=Object.keys(e),u=Object.keys(r);if(i.length!==u.length)return!1;for(var o=Object.prototype.hasOwnProperty.bind(r),s=0;s<i.length;s++){var f=i[s];if(!o(f))return!1;var d=e[f],c=r[f];if(a=void 0,a===!1||a===void 0&&d!==c)return!1}return!0}function Xi(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Ki(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ji(e,r,t){return r&&Ki(e.prototype,r),e}function V(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var Vi=function(){function e(r){var t=this;Xi(this,e),V(this,"hooks",$r({dragSource:function(a,i){t.clearDragSource(),t.dragSourceOptions=i||null,pt(a)?t.dragSourceRef=a:t.dragSourceNode=a,t.reconnectDragSource()},dragPreview:function(a,i){t.clearDragPreview(),t.dragPreviewOptions=i||null,pt(a)?t.dragPreviewRef=a:t.dragPreviewNode=a,t.reconnectDragPreview()}})),V(this,"handlerId",null),V(this,"dragSourceRef",null),V(this,"dragSourceNode",void 0),V(this,"dragSourceOptionsInternal",null),V(this,"dragSourceUnsubscribe",void 0),V(this,"dragPreviewRef",null),V(this,"dragPreviewNode",void 0),V(this,"dragPreviewOptionsInternal",null),V(this,"dragPreviewUnsubscribe",void 0),V(this,"lastConnectedHandlerId",null),V(this,"lastConnectedDragSource",null),V(this,"lastConnectedDragSourceOptions",null),V(this,"lastConnectedDragPreview",null),V(this,"lastConnectedDragPreviewOptions",null),V(this,"backend",void 0),this.backend=r}return Ji(e,[{key:"receiveHandlerId",value:function(t){this.handlerId!==t&&(this.handlerId=t,this.reconnect())}},{key:"connectTarget",get:function(){return this.dragSource}},{key:"dragSourceOptions",get:function(){return this.dragSourceOptionsInternal},set:function(t){this.dragSourceOptionsInternal=t}},{key:"dragPreviewOptions",get:function(){return this.dragPreviewOptionsInternal},set:function(t){this.dragPreviewOptionsInternal=t}},{key:"reconnect",value:function(){this.reconnectDragSource(),this.reconnectDragPreview()}},{key:"reconnectDragSource",value:function(){var t=this.dragSource,n=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();if(n&&this.disconnectDragSource(),!!this.handlerId){if(!t){this.lastConnectedDragSource=t;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=t,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,t,this.dragSourceOptions))}}},{key:"reconnectDragPreview",value:function(){var t=this.dragPreview,n=this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(n&&this.disconnectDragPreview(),!!this.handlerId){if(!t){this.lastConnectedDragPreview=t;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions))}}},{key:"didHandlerIdChange",value:function(){return this.lastConnectedHandlerId!==this.handlerId}},{key:"didConnectedDragSourceChange",value:function(){return this.lastConnectedDragSource!==this.dragSource}},{key:"didConnectedDragPreviewChange",value:function(){return this.lastConnectedDragPreview!==this.dragPreview}},{key:"didDragSourceOptionsChange",value:function(){return!vt(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}},{key:"didDragPreviewOptionsChange",value:function(){return!vt(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}},{key:"disconnectDragSource",value:function(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}},{key:"disconnectDragPreview",value:function(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}},{key:"dragSource",get:function(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}},{key:"dragPreview",get:function(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}},{key:"clearDragSource",value:function(){this.dragSourceNode=null,this.dragSourceRef=null}},{key:"clearDragPreview",value:function(){this.dragPreviewNode=null,this.dragPreviewRef=null}}]),e}();function Zi(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function eo(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function to(e,r,t){return r&&eo(e.prototype,r),e}function ae(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var ro=function(){function e(r){var t=this;Zi(this,e),ae(this,"hooks",$r({dropTarget:function(a,i){t.clearDropTarget(),t.dropTargetOptions=i,pt(a)?t.dropTargetRef=a:t.dropTargetNode=a,t.reconnect()}})),ae(this,"handlerId",null),ae(this,"dropTargetRef",null),ae(this,"dropTargetNode",void 0),ae(this,"dropTargetOptionsInternal",null),ae(this,"unsubscribeDropTarget",void 0),ae(this,"lastConnectedHandlerId",null),ae(this,"lastConnectedDropTarget",null),ae(this,"lastConnectedDropTargetOptions",null),ae(this,"backend",void 0),this.backend=r}return to(e,[{key:"connectTarget",get:function(){return this.dropTarget}},{key:"reconnect",value:function(){var t=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();t&&this.disconnectDropTarget();var n=this.dropTarget;if(this.handlerId){if(!n){this.lastConnectedDropTarget=n;return}t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=n,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,n,this.dropTargetOptions))}}},{key:"receiveHandlerId",value:function(t){t!==this.handlerId&&(this.handlerId=t,this.reconnect())}},{key:"dropTargetOptions",get:function(){return this.dropTargetOptionsInternal},set:function(t){this.dropTargetOptionsInternal=t}},{key:"didHandlerIdChange",value:function(){return this.lastConnectedHandlerId!==this.handlerId}},{key:"didDropTargetChange",value:function(){return this.lastConnectedDropTarget!==this.dropTarget}},{key:"didOptionsChange",value:function(){return!vt(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}},{key:"disconnectDropTarget",value:function(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}},{key:"dropTarget",get:function(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}},{key:"clearDropTarget",value:function(){this.dropTargetRef=null,this.dropTargetNode=null}}]),e}();function no(e,r,t){var n=t.getRegistry(),a=n.addTarget(e,r);return[a,function(){return n.removeTarget(a)}]}function ao(e,r,t){var n=t.getRegistry(),a=n.addSource(e,r);return[a,function(){return n.removeSource(a)}]}var ue=typeof window<"u"?M.useLayoutEffect:M.useEffect;function Re(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Re=function(t){return typeof t}:Re=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Re(e)}function io(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function oo(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function uo(e,r,t){return r&&oo(e.prototype,r),e}function at(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var so=function(){function e(r,t,n){io(this,e),at(this,"spec",void 0),at(this,"monitor",void 0),at(this,"connector",void 0),this.spec=r,this.monitor=t,this.connector=n}return uo(e,[{key:"beginDrag",value:function(){var t,n=this.spec,a=this.monitor,i=null;return Re(n.item)==="object"?i=n.item:typeof n.item=="function"?i=n.item(a):i={},(t=i)!==null&&t!==void 0?t:null}},{key:"canDrag",value:function(){var t=this.spec,n=this.monitor;return typeof t.canDrag=="boolean"?t.canDrag:typeof t.canDrag=="function"?t.canDrag(n):!0}},{key:"isDragging",value:function(t,n){var a=this.spec,i=this.monitor,u=a.isDragging;return u?u(i):n===t.getSourceId()}},{key:"endDrag",value:function(){var t=this.spec,n=this.monitor,a=this.connector,i=t.end;i&&i(n.getItem(),n),a.reconnect()}}]),e}();function co(e,r,t){var n=M.useMemo(function(){return new so(e,r,t)},[r,t]);return M.useEffect(function(){n.spec=e},[e]),n}function ve(){var e=M.useContext(Mr),r=e.dragDropManager;return H(r!=null,"Expected drag drop context"),r}function fo(e){return M.useMemo(function(){var r=e.type;return H(r!=null,"spec.type must be defined"),r},[e])}function lo(e,r){return vo(e)||po(e,r)||ho(e,r)||go()}function go(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ho(e,r){if(e){if(typeof e=="string")return fr(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return fr(e,r)}}function fr(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function po(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function vo(e){if(Array.isArray(e))return e}function Ao(e,r,t){var n=ve(),a=co(e,r,t),i=fo(e);ue(function(){if(i!=null){var o=ao(i,a,n),s=lo(o,2),f=s[0],d=s[1];return r.receiveHandlerId(f),t.receiveHandlerId(f),d}},[n,r,t,a,i])}function mo(e){return wo(e)||Io(e)||bo(e)||yo()}function yo(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bo(e,r){if(e){if(typeof e=="string")return At(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return At(e,r)}}function Io(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function wo(e){if(Array.isArray(e))return At(e)}function At(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function Ur(e,r){var t=mo([]);return typeof e!="function"&&t.push(e),M.useMemo(function(){return typeof e=="function"?e():e},t)}function Eo(){var e=ve();return M.useMemo(function(){return new Hi(e)},[e])}function Do(e,r){var t=ve(),n=M.useMemo(function(){return new Vi(t.getBackend())},[t]);return ue(function(){return n.dragSourceOptions=e||null,n.reconnect(),function(){return n.disconnectDragSource()}},[n,e]),ue(function(){return n.dragPreviewOptions=r||null,n.reconnect(),function(){return n.disconnectDragPreview()}},[n,r]),n}function Co(e,r){return To(e)||So(e,r)||Oo(e,r)||_o()}function _o(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Oo(e,r){if(e){if(typeof e=="string")return lr(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return lr(e,r)}}function lr(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function So(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function To(e){if(Array.isArray(e))return e}function Bo(e,r,t){var n=M.useState(function(){return r(e)}),a=Co(n,2),i=a[0],u=a[1],o=M.useCallback(function(){var s=r(e);fn(i,s)||(u(s),t&&t())},[i,e,t]);return ue(o),[i,o]}function Po(e,r){return Mo(e)||Qo(e,r)||ko(e,r)||xo()}function xo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ko(e,r){if(e){if(typeof e=="string")return dr(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return dr(e,r)}}function dr(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function Qo(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function Mo(e){if(Array.isArray(e))return e}function Ro(e,r,t){var n=Bo(e,r,t),a=Po(n,2),i=a[0],u=a[1];return ue(function(){var s=e.getHandlerId();if(s!=null)return e.subscribeToStateChange(u,{handlerIds:[s]})},[e,u]),i}function Gr(e,r,t){return Ro(r,e||function(){return{}},function(){return t.reconnect()})}function Fo(e){return M.useMemo(function(){return e.hooks.dragSource()},[e])}function No(e){return M.useMemo(function(){return e.hooks.dragPreview()},[e])}function jo(e,r){var t=Ur(e);H(!t.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");var n=Eo(),a=Do(t.options,t.previewOptions);return Ao(t,n,a),[Gr(t.collect,n,a),Fo(a),No(a)]}function Lo(e){var r=e.accept;return M.useMemo(function(){return H(e.accept!=null,"accept must be defined"),Array.isArray(r)?r:[r]},[r])}function Ho(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function $o(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Uo(e,r,t){return r&&$o(e.prototype,r),e}function gr(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var Go=function(){function e(r,t){Ho(this,e),gr(this,"spec",void 0),gr(this,"monitor",void 0),this.spec=r,this.monitor=t}return Uo(e,[{key:"canDrop",value:function(){var t=this.spec,n=this.monitor;return t.canDrop?t.canDrop(n.getItem(),n):!0}},{key:"hover",value:function(){var t=this.spec,n=this.monitor;t.hover&&t.hover(n.getItem(),n)}},{key:"drop",value:function(){var t=this.spec,n=this.monitor;if(t.drop)return t.drop(n.getItem(),n)}}]),e}();function zo(e,r){var t=M.useMemo(function(){return new Go(e,r)},[r]);return M.useEffect(function(){t.spec=e},[e]),t}function Wo(e,r){return Ko(e)||Xo(e,r)||Yo(e,r)||qo()}function qo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yo(e,r){if(e){if(typeof e=="string")return hr(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return hr(e,r)}}function hr(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function Xo(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var n=[],a=!0,i=!1,u,o;try{for(t=t.call(e);!(a=(u=t.next()).done)&&(n.push(u.value),!(r&&n.length===r));a=!0);}catch(s){i=!0,o=s}finally{try{!a&&t.return!=null&&t.return()}finally{if(i)throw o}}return n}}function Ko(e){if(Array.isArray(e))return e}function Jo(e,r,t){var n=ve(),a=zo(e,r),i=Lo(e);ue(function(){var o=no(i,a,n),s=Wo(o,2),f=s[0],d=s[1];return r.receiveHandlerId(f),t.receiveHandlerId(f),d},[n,r,a,t,i.map(function(u){return u.toString()}).join("|")])}function Vo(){var e=ve();return M.useMemo(function(){return new zi(e)},[e])}function Zo(e){var r=ve(),t=M.useMemo(function(){return new ro(r.getBackend())},[r]);return ue(function(){return t.dropTargetOptions=e||null,t.reconnect(),function(){return t.disconnectDropTarget()}},[e]),t}function eu(e){return M.useMemo(function(){return e.hooks.dropTarget()},[e])}function tu(e,r){var t=Ur(e),n=Vo(),a=Zo(t.options);return Jo(t,n,a),[Gr(t.collect,n,a),eu(a)]}function zr(e){var r=null,t=function(){return r==null&&(r=e()),r};return t}function ru(e,r){return e.filter(function(t){return t!==r})}function nu(e,r){var t=new Set,n=function(u){return t.add(u)};e.forEach(n),r.forEach(n);var a=[];return t.forEach(function(i){return a.push(i)}),a}function au(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function iu(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ou(e,r,t){return r&&iu(e.prototype,r),e}function pr(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var uu=function(){function e(r){au(this,e),pr(this,"entered",[]),pr(this,"isNodeInDocument",void 0),this.isNodeInDocument=r}return ou(e,[{key:"enter",value:function(t){var n=this,a=this.entered.length,i=function(o){return n.isNodeInDocument(o)&&(!o.contains||o.contains(t))};return this.entered=nu(this.entered.filter(i),[t]),a===0&&this.entered.length>0}},{key:"leave",value:function(t){var n=this.entered.length;return this.entered=ru(this.entered.filter(this.isNodeInDocument),t),n>0&&this.entered.length===0}},{key:"reset",value:function(){this.entered=[]}}]),e}(),su=zr(function(){return/firefox/i.test(navigator.userAgent)}),Wr=zr(function(){return!!window.safari});function cu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function fu(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function lu(e,r,t){return r&&fu(e.prototype,r),e}function ye(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var vr=function(){function e(r,t){cu(this,e),ye(this,"xs",void 0),ye(this,"ys",void 0),ye(this,"c1s",void 0),ye(this,"c2s",void 0),ye(this,"c3s",void 0);for(var n=r.length,a=[],i=0;i<n;i++)a.push(i);a.sort(function(l,D){return r[l]<r[D]?-1:1});for(var u=[],o=[],s,f,d=0;d<n-1;d++)s=r[d+1]-r[d],f=t[d+1]-t[d],u.push(s),o.push(f/s);for(var c=[o[0]],g=0;g<u.length-1;g++){var A=o[g],p=o[g+1];if(A*p<=0)c.push(0);else{s=u[g];var m=u[g+1],y=s+m;c.push(3*y/((y+m)/A+(y+s)/p))}}c.push(o[o.length-1]);for(var I=[],E=[],_,b=0;b<c.length-1;b++){_=o[b];var P=c[b],x=1/u[b],h=P+c[b+1]-_-_;I.push((_-P-h)*x),E.push(h*x*x)}this.xs=r,this.ys=t,this.c1s=c,this.c2s=I,this.c3s=E}return lu(e,[{key:"interpolate",value:function(t){var n=this.xs,a=this.ys,i=this.c1s,u=this.c2s,o=this.c3s,s=n.length-1;if(t===n[s])return a[s];for(var f=0,d=o.length-1,c;f<=d;){c=Math.floor(.5*(f+d));var g=n[c];if(g<t)f=c+1;else if(g>t)d=c-1;else return a[c]}s=Math.max(0,d);var A=t-n[s],p=A*A;return a[s]+i[s]*A+u[s]*p+o[s]*A*p}}]),e}(),du=1;function qr(e){var r=e.nodeType===du?e:e.parentElement;if(!r)return null;var t=r.getBoundingClientRect(),n=t.top,a=t.left;return{x:a,y:n}}function Be(e){return{x:e.clientX,y:e.clientY}}function gu(e){var r;return e.nodeName==="IMG"&&(su()||!((r=document.documentElement)!==null&&r!==void 0&&r.contains(e)))}function hu(e,r,t,n){var a=e?r.width:t,i=e?r.height:n;return Wr()&&e&&(i/=window.devicePixelRatio,a/=window.devicePixelRatio),{dragPreviewWidth:a,dragPreviewHeight:i}}function pu(e,r,t,n,a){var i=gu(r),u=i?e:r,o=qr(u),s={x:t.x-o.x,y:t.y-o.y},f=e.offsetWidth,d=e.offsetHeight,c=n.anchorX,g=n.anchorY,A=hu(i,r,f,d),p=A.dragPreviewWidth,m=A.dragPreviewHeight,y=function(){var h=new vr([0,.5,1],[s.y,s.y/d*m,s.y+m-d]),l=h.interpolate(g);return Wr()&&i&&(l+=(window.devicePixelRatio-1)*m),l},I=function(){var h=new vr([0,.5,1],[s.x,s.x/f*p,s.x+p-f]);return h.interpolate(c)},E=a.offsetX,_=a.offsetY,b=E===0||E,P=_===0||_;return{x:b?E:I(),y:P?_:y()}}var Yr="__NATIVE_FILE__",Xr="__NATIVE_URL__",Kr="__NATIVE_TEXT__",Jr="__NATIVE_HTML__";const Ar=Object.freeze(Object.defineProperty({__proto__:null,FILE:Yr,HTML:Jr,TEXT:Kr,URL:Xr},Symbol.toStringTag,{value:"Module"}));function it(e,r,t){var n=r.reduce(function(a,i){return a||e.getData(i)},"");return n??t}var ge;function Pe(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var mt=(ge={},Pe(ge,Yr,{exposeProperties:{files:function(r){return Array.prototype.slice.call(r.files)},items:function(r){return r.items},dataTransfer:function(r){return r}},matchesTypes:["Files"]}),Pe(ge,Jr,{exposeProperties:{html:function(r,t){return it(r,t,"")},dataTransfer:function(r){return r}},matchesTypes:["Html","text/html"]}),Pe(ge,Xr,{exposeProperties:{urls:function(r,t){return it(r,t,"").split(`
`)},dataTransfer:function(r){return r}},matchesTypes:["Url","text/uri-list"]}),Pe(ge,Kr,{exposeProperties:{text:function(r,t){return it(r,t,"")},dataTransfer:function(r){return r}},matchesTypes:["Text","text/plain"]}),ge);function vu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Au(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function mu(e,r,t){return r&&Au(e.prototype,r),e}function mr(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var yu=function(){function e(r){vu(this,e),mr(this,"item",void 0),mr(this,"config",void 0),this.config=r,this.item={},this.initializeExposedProperties()}return mu(e,[{key:"initializeExposedProperties",value:function(){var t=this;Object.keys(this.config.exposeProperties).forEach(function(n){Object.defineProperty(t.item,n,{configurable:!0,enumerable:!0,get:function(){return console.warn(`Browser doesn't allow reading "`.concat(n,'" until the drop event.')),null}})})}},{key:"loadDataTransfer",value:function(t){var n=this;if(t){var a={};Object.keys(this.config.exposeProperties).forEach(function(i){a[i]={value:n.config.exposeProperties[i](t,n.config.matchesTypes),configurable:!0,enumerable:!0}}),Object.defineProperties(this.item,a)}}},{key:"canDrag",value:function(){return!0}},{key:"beginDrag",value:function(){return this.item}},{key:"isDragging",value:function(t,n){return n===t.getSourceId()}},{key:"endDrag",value:function(){}}]),e}();function bu(e,r){var t=new yu(mt[e]);return t.loadDataTransfer(r),t}function ot(e){if(!e)return null;var r=Array.prototype.slice.call(e.types||[]);return Object.keys(mt).filter(function(t){var n=mt[t].matchesTypes;return n.some(function(a){return r.indexOf(a)>-1})})[0]||null}function Iu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function wu(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Eu(e,r,t){return r&&wu(e.prototype,r),e}function ut(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var Du=function(){function e(r,t){Iu(this,e),ut(this,"ownerDocument",null),ut(this,"globalContext",void 0),ut(this,"optionsArgs",void 0),this.globalContext=r,this.optionsArgs=t}return Eu(e,[{key:"window",get:function(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}},{key:"document",get:function(){var t;return(t=this.globalContext)!==null&&t!==void 0&&t.document?this.globalContext.document:this.window?this.window.document:void 0}},{key:"rootElement",get:function(){var t;return((t=this.optionsArgs)===null||t===void 0?void 0:t.rootElement)||this.window}}]),e}();function yr(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,n)}return t}function br(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?yr(Object(t),!0).forEach(function(n){G(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):yr(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Cu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function _u(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ou(e,r,t){return r&&_u(e.prototype,r),e}function G(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var Su=function(){function e(r,t,n){var a=this;Cu(this,e),G(this,"options",void 0),G(this,"actions",void 0),G(this,"monitor",void 0),G(this,"registry",void 0),G(this,"enterLeaveCounter",void 0),G(this,"sourcePreviewNodes",new Map),G(this,"sourcePreviewNodeOptions",new Map),G(this,"sourceNodes",new Map),G(this,"sourceNodeOptions",new Map),G(this,"dragStartSourceIds",null),G(this,"dropTargetIds",[]),G(this,"dragEnterTargetIds",[]),G(this,"currentNativeSource",null),G(this,"currentNativeHandle",null),G(this,"currentDragSourceNode",null),G(this,"altKeyPressed",!1),G(this,"mouseMoveTimeoutTimer",null),G(this,"asyncEndDragFrameId",null),G(this,"dragOverTargetIds",null),G(this,"lastClientOffset",null),G(this,"hoverRafId",null),G(this,"getSourceClientOffset",function(i){var u=a.sourceNodes.get(i);return u&&qr(u)||null}),G(this,"endDragNativeItem",function(){a.isDraggingNativeItem()&&(a.actions.endDrag(),a.currentNativeHandle&&a.registry.removeSource(a.currentNativeHandle),a.currentNativeHandle=null,a.currentNativeSource=null)}),G(this,"isNodeInDocument",function(i){return!!(i&&a.document&&a.document.body&&a.document.body.contains(i))}),G(this,"endDragIfSourceWasRemovedFromDOM",function(){var i=a.currentDragSourceNode;i==null||a.isNodeInDocument(i)||a.clearCurrentDragSourceNode()&&a.monitor.isDragging()&&a.actions.endDrag()}),G(this,"handleTopDragStartCapture",function(){a.clearCurrentDragSourceNode(),a.dragStartSourceIds=[]}),G(this,"handleTopDragStart",function(i){if(!i.defaultPrevented){var u=a.dragStartSourceIds;a.dragStartSourceIds=null;var o=Be(i);a.monitor.isDragging()&&a.actions.endDrag(),a.actions.beginDrag(u||[],{publishSource:!1,getSourceClientOffset:a.getSourceClientOffset,clientOffset:o});var s=i.dataTransfer,f=ot(s);if(a.monitor.isDragging()){if(s&&typeof s.setDragImage=="function"){var d=a.monitor.getSourceId(),c=a.sourceNodes.get(d),g=a.sourcePreviewNodes.get(d)||c;if(g){var A=a.getCurrentSourcePreviewNodeOptions(),p=A.anchorX,m=A.anchorY,y=A.offsetX,I=A.offsetY,E={anchorX:p,anchorY:m},_={offsetX:y,offsetY:I},b=pu(c,g,o,E,_);s.setDragImage(g,b.x,b.y)}}try{s==null||s.setData("application/json",{})}catch{}a.setCurrentDragSourceNode(i.target);var P=a.getCurrentSourcePreviewNodeOptions(),x=P.captureDraggingState;x?a.actions.publishDragSource():setTimeout(function(){return a.actions.publishDragSource()},0)}else if(f)a.beginDragNativeItem(f);else{if(s&&!s.types&&(i.target&&!i.target.hasAttribute||!i.target.hasAttribute("draggable")))return;i.preventDefault()}}}),G(this,"handleTopDragEndCapture",function(){a.clearCurrentDragSourceNode()&&a.monitor.isDragging()&&a.actions.endDrag()}),G(this,"handleTopDragEnterCapture",function(i){a.dragEnterTargetIds=[];var u=a.enterLeaveCounter.enter(i.target);if(!(!u||a.monitor.isDragging())){var o=i.dataTransfer,s=ot(o);s&&a.beginDragNativeItem(s,o)}}),G(this,"handleTopDragEnter",function(i){var u=a.dragEnterTargetIds;if(a.dragEnterTargetIds=[],!!a.monitor.isDragging()){a.altKeyPressed=i.altKey,u.length>0&&a.actions.hover(u,{clientOffset:Be(i)});var o=u.some(function(s){return a.monitor.canDropOnTarget(s)});o&&(i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect=a.getCurrentDropEffect()))}}),G(this,"handleTopDragOverCapture",function(){a.dragOverTargetIds=[]}),G(this,"handleTopDragOver",function(i){var u=a.dragOverTargetIds;if(a.dragOverTargetIds=[],!a.monitor.isDragging()){i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect="none");return}a.altKeyPressed=i.altKey,a.lastClientOffset=Be(i),a.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(a.hoverRafId=requestAnimationFrame(function(){a.monitor.isDragging()&&a.actions.hover(u||[],{clientOffset:a.lastClientOffset}),a.hoverRafId=null}));var o=(u||[]).some(function(s){return a.monitor.canDropOnTarget(s)});o?(i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect=a.getCurrentDropEffect())):a.isDraggingNativeItem()?i.preventDefault():(i.preventDefault(),i.dataTransfer&&(i.dataTransfer.dropEffect="none"))}),G(this,"handleTopDragLeaveCapture",function(i){a.isDraggingNativeItem()&&i.preventDefault();var u=a.enterLeaveCounter.leave(i.target);u&&a.isDraggingNativeItem()&&setTimeout(function(){return a.endDragNativeItem()},0)}),G(this,"handleTopDropCapture",function(i){if(a.dropTargetIds=[],a.isDraggingNativeItem()){var u;i.preventDefault(),(u=a.currentNativeSource)===null||u===void 0||u.loadDataTransfer(i.dataTransfer)}else ot(i.dataTransfer)&&i.preventDefault();a.enterLeaveCounter.reset()}),G(this,"handleTopDrop",function(i){var u=a.dropTargetIds;a.dropTargetIds=[],a.actions.hover(u,{clientOffset:Be(i)}),a.actions.drop({dropEffect:a.getCurrentDropEffect()}),a.isDraggingNativeItem()?a.endDragNativeItem():a.monitor.isDragging()&&a.actions.endDrag()}),G(this,"handleSelectStart",function(i){var u=i.target;typeof u.dragDrop=="function"&&(u.tagName==="INPUT"||u.tagName==="SELECT"||u.tagName==="TEXTAREA"||u.isContentEditable||(i.preventDefault(),u.dragDrop()))}),this.options=new Du(t,n),this.actions=r.getActions(),this.monitor=r.getMonitor(),this.registry=r.getRegistry(),this.enterLeaveCounter=new uu(this.isNodeInDocument)}return Ou(e,[{key:"profile",value:function(){var t,n;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((t=this.dragStartSourceIds)===null||t===void 0?void 0:t.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((n=this.dragOverTargetIds)===null||n===void 0?void 0:n.length)||0}}},{key:"window",get:function(){return this.options.window}},{key:"document",get:function(){return this.options.document}},{key:"rootElement",get:function(){return this.options.rootElement}},{key:"setup",value:function(){var t=this.rootElement;if(t!==void 0){if(t.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");t.__isReactDndBackendSetUp=!0,this.addEventListeners(t)}}},{key:"teardown",value:function(){var t=this.rootElement;if(t!==void 0&&(t.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var n;(n=this.window)===null||n===void 0||n.cancelAnimationFrame(this.asyncEndDragFrameId)}}},{key:"connectDragPreview",value:function(t,n,a){var i=this;return this.sourcePreviewNodeOptions.set(t,a),this.sourcePreviewNodes.set(t,n),function(){i.sourcePreviewNodes.delete(t),i.sourcePreviewNodeOptions.delete(t)}}},{key:"connectDragSource",value:function(t,n,a){var i=this;this.sourceNodes.set(t,n),this.sourceNodeOptions.set(t,a);var u=function(f){return i.handleDragStart(f,t)},o=function(f){return i.handleSelectStart(f)};return n.setAttribute("draggable","true"),n.addEventListener("dragstart",u),n.addEventListener("selectstart",o),function(){i.sourceNodes.delete(t),i.sourceNodeOptions.delete(t),n.removeEventListener("dragstart",u),n.removeEventListener("selectstart",o),n.setAttribute("draggable","false")}}},{key:"connectDropTarget",value:function(t,n){var a=this,i=function(f){return a.handleDragEnter(f,t)},u=function(f){return a.handleDragOver(f,t)},o=function(f){return a.handleDrop(f,t)};return n.addEventListener("dragenter",i),n.addEventListener("dragover",u),n.addEventListener("drop",o),function(){n.removeEventListener("dragenter",i),n.removeEventListener("dragover",u),n.removeEventListener("drop",o)}}},{key:"addEventListeners",value:function(t){t.addEventListener&&(t.addEventListener("dragstart",this.handleTopDragStart),t.addEventListener("dragstart",this.handleTopDragStartCapture,!0),t.addEventListener("dragend",this.handleTopDragEndCapture,!0),t.addEventListener("dragenter",this.handleTopDragEnter),t.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),t.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),t.addEventListener("dragover",this.handleTopDragOver),t.addEventListener("dragover",this.handleTopDragOverCapture,!0),t.addEventListener("drop",this.handleTopDrop),t.addEventListener("drop",this.handleTopDropCapture,!0))}},{key:"removeEventListeners",value:function(t){t.removeEventListener&&(t.removeEventListener("dragstart",this.handleTopDragStart),t.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),t.removeEventListener("dragend",this.handleTopDragEndCapture,!0),t.removeEventListener("dragenter",this.handleTopDragEnter),t.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),t.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),t.removeEventListener("dragover",this.handleTopDragOver),t.removeEventListener("dragover",this.handleTopDragOverCapture,!0),t.removeEventListener("drop",this.handleTopDrop),t.removeEventListener("drop",this.handleTopDropCapture,!0))}},{key:"getCurrentSourceNodeOptions",value:function(){var t=this.monitor.getSourceId(),n=this.sourceNodeOptions.get(t);return br({dropEffect:this.altKeyPressed?"copy":"move"},n||{})}},{key:"getCurrentDropEffect",value:function(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}},{key:"getCurrentSourcePreviewNodeOptions",value:function(){var t=this.monitor.getSourceId(),n=this.sourcePreviewNodeOptions.get(t);return br({anchorX:.5,anchorY:.5,captureDraggingState:!1},n||{})}},{key:"isDraggingNativeItem",value:function(){var t=this.monitor.getItemType();return Object.keys(Ar).some(function(n){return Ar[n]===t})}},{key:"beginDragNativeItem",value:function(t,n){this.clearCurrentDragSourceNode(),this.currentNativeSource=bu(t,n),this.currentNativeHandle=this.registry.addSource(t,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}},{key:"setCurrentDragSourceNode",value:function(t){var n=this;this.clearCurrentDragSourceNode(),this.currentDragSourceNode=t;var a=1e3;this.mouseMoveTimeoutTimer=setTimeout(function(){var i;return(i=n.rootElement)===null||i===void 0?void 0:i.addEventListener("mousemove",n.endDragIfSourceWasRemovedFromDOM,!0)},a)}},{key:"clearCurrentDragSourceNode",value:function(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var t;(t=this.window)===null||t===void 0||t.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}},{key:"handleDragStart",value:function(t,n){t.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(n))}},{key:"handleDragEnter",value:function(t,n){this.dragEnterTargetIds.unshift(n)}},{key:"handleDragOver",value:function(t,n){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(n)}},{key:"handleDrop",value:function(t,n){this.dropTargetIds.unshift(n)}}]),e}(),Tu=function(r,t,n){return new Su(r,t,n)},Vr={exports:{}};/*!

pica
https://github.com/nodeca/pica

*/(function(e,r){(function(t){e.exports=t()})(function(){return function(){function t(n,a,i){function u(f,d){if(!a[f]){if(!n[f]){var c=typeof Se=="function"&&Se;if(!d&&c)return c(f,!0);if(o)return o(f,!0);var g=new Error("Cannot find module '"+f+"'");throw g.code="MODULE_NOT_FOUND",g}var A=a[f]={exports:{}};n[f][0].call(A.exports,function(p){var m=n[f][1][p];return u(m||p)},A,A.exports,t,n,a,i)}return a[f].exports}for(var o=typeof Se=="function"&&Se,s=0;s<i.length;s++)u(i[s]);return u}return t}()({1:[function(t,n,a){var i=t("multimath"),u=t("./mm_unsharp_mask"),o=t("./mm_resize");function s(f){var d=f||[],c={js:d.indexOf("js")>=0,wasm:d.indexOf("wasm")>=0};i.call(this,c),this.features={js:c.js,wasm:c.wasm&&this.has_wasm()},this.use(u),this.use(o)}s.prototype=Object.create(i.prototype),s.prototype.constructor=s,s.prototype.resizeAndUnsharp=function(d,c){var g=this.resize(d,c);return d.unsharpAmount&&this.unsharp_mask(g,d.toWidth,d.toHeight,d.unsharpAmount,d.unsharpRadius,d.unsharpThreshold),g},n.exports=s},{"./mm_resize":4,"./mm_unsharp_mask":9,multimath:19}],2:[function(t,n,a){function i(c){return c<0?0:c>255?255:c}function u(c){return c>=0?c:0}function o(c,g,A,p,m,y){var I,E,_,b,P,x,h,l,D,B,C,R=0,Q=0;for(D=0;D<p;D++){for(P=0,B=0;B<m;B++){for(x=y[P++],h=y[P++],l=R+x*4|0,I=E=_=b=0;h>0;h--)C=y[P++],b=b+C*c[l+3]|0,_=_+C*c[l+2]|0,E=E+C*c[l+1]|0,I=I+C*c[l]|0,l=l+4|0;g[Q+3]=u(b>>7),g[Q+2]=u(_>>7),g[Q+1]=u(E>>7),g[Q]=u(I>>7),Q=Q+p*4|0}Q=(D+1)*4|0,R=(D+1)*A*4|0}}function s(c,g,A,p,m,y){var I,E,_,b,P,x,h,l,D,B,C,R=0,Q=0;for(D=0;D<p;D++){for(P=0,B=0;B<m;B++){for(x=y[P++],h=y[P++],l=R+x*4|0,I=E=_=b=0;h>0;h--)C=y[P++],b=b+C*c[l+3]|0,_=_+C*c[l+2]|0,E=E+C*c[l+1]|0,I=I+C*c[l]|0,l=l+4|0;I>>=7,E>>=7,_>>=7,b>>=7,g[Q+3]=i(b+8192>>14),g[Q+2]=i(_+8192>>14),g[Q+1]=i(E+8192>>14),g[Q]=i(I+8192>>14),Q=Q+p*4|0}Q=(D+1)*4|0,R=(D+1)*A*4|0}}function f(c,g,A,p,m,y){var I,E,_,b,P,x,h,l,D,B,C,R,Q=0,$=0;for(B=0;B<p;B++){for(x=0,C=0;C<m;C++){for(h=y[x++],l=y[x++],D=Q+h*4|0,I=E=_=b=0;l>0;l--)R=y[x++],P=c[D+3],b=b+R*P|0,_=_+R*c[D+2]*P|0,E=E+R*c[D+1]*P|0,I=I+R*c[D]*P|0,D=D+4|0;_=_/255|0,E=E/255|0,I=I/255|0,g[$+3]=u(b>>7),g[$+2]=u(_>>7),g[$+1]=u(E>>7),g[$]=u(I>>7),$=$+p*4|0}$=(B+1)*4|0,Q=(B+1)*A*4|0}}function d(c,g,A,p,m,y){var I,E,_,b,P,x,h,l,D,B,C,R=0,Q=0;for(D=0;D<p;D++){for(P=0,B=0;B<m;B++){for(x=y[P++],h=y[P++],l=R+x*4|0,I=E=_=b=0;h>0;h--)C=y[P++],b=b+C*c[l+3]|0,_=_+C*c[l+2]|0,E=E+C*c[l+1]|0,I=I+C*c[l]|0,l=l+4|0;I>>=7,E>>=7,_>>=7,b>>=7,b=i(b+8192>>14),b>0&&(I=I*255/b|0,E=E*255/b|0,_=_*255/b|0),g[Q+3]=b,g[Q+2]=i(_+8192>>14),g[Q+1]=i(E+8192>>14),g[Q]=i(I+8192>>14),Q=Q+p*4|0}Q=(D+1)*4|0,R=(D+1)*A*4|0}}n.exports={convolveHor:o,convolveVert:s,convolveHorWithPre:f,convolveVertWithPre:d}},{}],3:[function(t,n,a){n.exports="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"},{}],4:[function(t,n,a){n.exports={name:"resize",fn:t("./resize"),wasm_fn:t("./resize_wasm"),wasm_src:t("./convolve_wasm_base64")}},{"./convolve_wasm_base64":3,"./resize":5,"./resize_wasm":8}],5:[function(t,n,a){var i=t("./resize_filter_gen"),u=t("./convolve"),o=u.convolveHor,s=u.convolveVert,f=u.convolveHorWithPre,d=u.convolveVertWithPre;function c(A,p,m){for(var y=3,I=p*m*4|0;y<I;){if(A[y]!==255)return!0;y=y+4|0}return!1}function g(A,p,m){for(var y=3,I=p*m*4|0;y<I;)A[y]=255,y=y+4|0}n.exports=function(p){var m=p.src,y=p.width,I=p.height,E=p.toWidth,_=p.toHeight,b=p.scaleX||p.toWidth/p.width,P=p.scaleY||p.toHeight/p.height,x=p.offsetX||0,h=p.offsetY||0,l=p.dest||new Uint8Array(E*_*4),D=typeof p.filter>"u"?"mks2013":p.filter,B=i(D,y,E,b,x),C=i(D,I,_,P,h),R=new Uint16Array(E*I*4);return c(m,y,I)?(f(m,R,y,I,E,B),d(R,l,I,E,_,C)):(o(m,R,y,I,E,B),s(R,l,I,E,_,C),g(l,E,_)),l}},{"./convolve":2,"./resize_filter_gen":6}],6:[function(t,n,a){var i=t("./resize_filter_info"),u=14;function o(s){return Math.round(s*((1<<u)-1))}n.exports=function(f,d,c,g,A){var p=i.filter[f].fn,m=1/g,y=Math.min(1,g),I=i.filter[f].win/y,E,_,b,P,x,h,l,D,B,C,R,Q,$,F,v,S,w,O=Math.floor((I+1)*2),T=new Int16Array((O+2)*c),U=0,N=!T.subarray||!T.set;for(E=0;E<c;E++){for(_=(E+.5)*m+A,b=Math.max(0,Math.floor(_-I)),P=Math.min(d-1,Math.ceil(_+I)),x=P-b+1,h=new Float32Array(x),l=new Int16Array(x),D=0,B=b,C=0;B<=P;B++,C++)R=p((B+.5-_)*y),D+=R,h[C]=R;for(Q=0,C=0;C<h.length;C++)$=h[C]/D,Q+=$,l[C]=o($);for(l[c>>1]+=o(1-Q),F=0;F<l.length&&l[F]===0;)F++;if(F<l.length){for(v=l.length-1;v>0&&l[v]===0;)v--;if(S=b+F,w=v-F+1,T[U++]=S,T[U++]=w,!N)T.set(l.subarray(F,v+1),U),U+=w;else for(C=F;C<=v;C++)T[U++]=l[C]}else T[U++]=0,T[U++]=0}return T}},{"./resize_filter_info":7}],7:[function(t,n,a){var i={box:{win:.5,fn:function(o){return o<0&&(o=-o),o<.5?1:0}},hamming:{win:1,fn:function(o){if(o<0&&(o=-o),o>=1)return 0;if(o<11920929e-14)return 1;var s=o*Math.PI;return Math.sin(s)/s*(.54+.46*Math.cos(s/1))}},lanczos2:{win:2,fn:function(o){if(o<0&&(o=-o),o>=2)return 0;if(o<11920929e-14)return 1;var s=o*Math.PI;return Math.sin(s)/s*Math.sin(s/2)/(s/2)}},lanczos3:{win:3,fn:function(o){if(o<0&&(o=-o),o>=3)return 0;if(o<11920929e-14)return 1;var s=o*Math.PI;return Math.sin(s)/s*Math.sin(s/3)/(s/3)}},mks2013:{win:2.5,fn:function(o){return o<0&&(o=-o),o>=2.5?0:o>=1.5?-.125*(o-2.5)*(o-2.5):o>=.5?.25*(4*o*o-11*o+7):1.0625-1.75*o*o}}};n.exports={filter:i,f2q:{box:0,hamming:1,lanczos2:2,lanczos3:3},q2f:["box","hamming","lanczos2","lanczos3"]}},{}],8:[function(t,n,a){var i=t("./resize_filter_gen");function u(c,g,A){for(var p=3,m=g*A*4|0;p<m;){if(c[p]!==255)return!0;p=p+4|0}return!1}function o(c,g,A){for(var p=3,m=g*A*4|0;p<m;)c[p]=255,p=p+4|0}function s(c){return new Uint8Array(c.buffer,0,c.byteLength)}var f=!0;try{f=new Uint32Array(new Uint8Array([1,0,0,0]).buffer)[0]===1}catch{}function d(c,g,A){if(f){g.set(s(c),A);return}for(var p=A,m=0;m<c.length;m++){var y=c[m];g[p++]=y&255,g[p++]=y>>8&255}}n.exports=function(g){var A=g.src,p=g.width,m=g.height,y=g.toWidth,I=g.toHeight,E=g.scaleX||g.toWidth/g.width,_=g.scaleY||g.toHeight/g.height,b=g.offsetX||0,P=g.offsetY||0,x=g.dest||new Uint8Array(y*I*4),h=typeof g.filter>"u"?"mks2013":g.filter,l=i(h,p,y,E,b),D=i(h,m,I,_,P),B=0,C=Math.max(A.byteLength,x.byteLength),R=this.__align(B+C),Q=m*y*4*2,$=this.__align(R+Q),F=this.__align($+l.byteLength),v=F+D.byteLength,S=this.__instance("resize",v),w=new Uint8Array(this.__memory.buffer),O=new Uint32Array(this.__memory.buffer),T=new Uint32Array(A.buffer);O.set(T),d(l,w,$),d(D,w,F);var U=S.exports.convolveHV||S.exports._convolveHV;u(A,p,m)?U($,F,R,p,m,y,I,1):(U($,F,R,p,m,y,I,0),o(x,y,I));var N=new Uint32Array(x.buffer);return N.set(new Uint32Array(this.__memory.buffer,0,I*y)),x}},{"./resize_filter_gen":6}],9:[function(t,n,a){n.exports={name:"unsharp_mask",fn:t("./unsharp_mask"),wasm_fn:t("./unsharp_mask_wasm"),wasm_src:t("./unsharp_mask_wasm_base64")}},{"./unsharp_mask":10,"./unsharp_mask_wasm":11,"./unsharp_mask_wasm_base64":12}],10:[function(t,n,a){var i=t("glur/mono16");function u(o,s,f){for(var d=s*f,c=new Uint16Array(d),g,A,p,m,y=0;y<d;y++)g=o[4*y],A=o[4*y+1],p=o[4*y+2],m=g>=A&&g>=p?g:A>=p&&A>=g?A:p,c[y]=m<<8;return c}n.exports=function(s,f,d,c,g,A){var p,m,y,I,E;if(!(c===0||g<.5)){g>2&&(g=2);var _=u(s,f,d),b=new Uint16Array(_);i(b,f,d,g);for(var P=c/100*4096+.5|0,x=A<<8,h=f*d,l=0;l<h;l++)p=_[l],I=p-b[l],Math.abs(I)>=x&&(m=p+(P*I+2048>>12),m=m>65280?65280:m,m=m<0?0:m,p=p!==0?p:1,y=(m<<12)/p|0,E=l*4,s[E]=s[E]*y+2048>>12,s[E+1]=s[E+1]*y+2048>>12,s[E+2]=s[E+2]*y+2048>>12)}}},{"glur/mono16":18}],11:[function(t,n,a){n.exports=function(u,o,s,f,d,c){if(!(f===0||d<.5)){d>2&&(d=2);var g=o*s,A=g*4,p=g*2,m=g*2,y=Math.max(o,s)*4,I=8*4,E=0,_=A,b=_+p,P=b+m,x=P+m,h=x+y,l=this.__instance("unsharp_mask",A+p+m*2+y+I,{exp:Math.exp}),D=new Uint32Array(u.buffer),B=new Uint32Array(this.__memory.buffer);B.set(D);var C=l.exports.hsv_v16||l.exports._hsv_v16;C(E,_,o,s),C=l.exports.blurMono16||l.exports._blurMono16,C(_,b,P,x,h,o,s,d),C=l.exports.unsharp||l.exports._unsharp,C(E,E,_,b,o,s,f,c),D.set(new Uint32Array(this.__memory.buffer,0,g))}}},{}],12:[function(t,n,a){n.exports="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"},{}],13:[function(t,n,a){var i=100;function u(o,s){this.create=o,this.available=[],this.acquired={},this.lastId=1,this.timeoutId=0,this.idle=s||2e3}u.prototype.acquire=function(){var o=this,s;return this.available.length!==0?s=this.available.pop():(s=this.create(),s.id=this.lastId++,s.release=function(){return o.release(s)}),this.acquired[s.id]=s,s},u.prototype.release=function(o){var s=this;delete this.acquired[o.id],o.lastUsed=Date.now(),this.available.push(o),this.timeoutId===0&&(this.timeoutId=setTimeout(function(){return s.gc()},i))},u.prototype.gc=function(){var o=this,s=Date.now();this.available=this.available.filter(function(f){return s-f.lastUsed>o.idle?(f.destroy(),!1):!0}),this.available.length!==0?this.timeoutId=setTimeout(function(){return o.gc()},i):this.timeoutId=0},n.exports=u},{}],14:[function(t,n,a){var i=2;n.exports=function(o,s,f,d,c,g){var A=f/o,p=d/s,m=(2*g+i+1)/c;if(m>.5)return[[f,d]];var y=Math.ceil(Math.log(Math.min(A,p))/Math.log(m));if(y<=1)return[[f,d]];for(var I=[],E=0;E<y;E++){var _=Math.round(Math.pow(Math.pow(o,y-E-1)*Math.pow(f,E+1),1/y)),b=Math.round(Math.pow(Math.pow(s,y-E-1)*Math.pow(d,E+1),1/y));I.push([_,b])}return I}},{}],15:[function(t,n,a){var i=1e-5;function u(s){var f=Math.round(s);return Math.abs(s-f)<i?f:Math.floor(s)}function o(s){var f=Math.round(s);return Math.abs(s-f)<i?f:Math.ceil(s)}n.exports=function(f){var d=f.toWidth/f.width,c=f.toHeight/f.height,g=u(f.srcTileSize*d)-2*f.destTileBorder,A=u(f.srcTileSize*c)-2*f.destTileBorder;if(g<1||A<1)throw new Error("Internal error in pica: target tile width/height is too small.");var p,m,y,I,E,_,b=[],P;for(I=0;I<f.toHeight;I+=A)for(y=0;y<f.toWidth;y+=g)p=y-f.destTileBorder,p<0&&(p=0),E=y+g+f.destTileBorder-p,p+E>=f.toWidth&&(E=f.toWidth-p),m=I-f.destTileBorder,m<0&&(m=0),_=I+A+f.destTileBorder-m,m+_>=f.toHeight&&(_=f.toHeight-m),P={toX:p,toY:m,toWidth:E,toHeight:_,toInnerX:y,toInnerY:I,toInnerWidth:g,toInnerHeight:A,offsetX:p/d-u(p/d),offsetY:m/c-u(m/c),scaleX:d,scaleY:c,x:u(p/d),y:u(m/c),width:o(E/d),height:o(_/c)},b.push(P);return b}},{}],16:[function(t,n,a){function i(u){return Object.prototype.toString.call(u)}n.exports.isCanvas=function(o){var s=i(o);return s==="[object HTMLCanvasElement]"||s==="[object OffscreenCanvas]"||s==="[object Canvas]"},n.exports.isImage=function(o){return i(o)==="[object HTMLImageElement]"},n.exports.isImageBitmap=function(o){return i(o)==="[object ImageBitmap]"},n.exports.limiter=function(o){var s=0,f=[];function d(){s<o&&f.length&&(s++,f.shift()())}return function(g){return new Promise(function(A,p){f.push(function(){g().then(function(m){A(m),s--,d()},function(m){p(m),s--,d()})}),d()})}},n.exports.cib_quality_name=function(o){switch(o){case 0:return"pixelated";case 1:return"low";case 2:return"medium"}return"high"},n.exports.cib_support=function(o){return Promise.resolve().then(function(){if(typeof createImageBitmap>"u")return!1;var s=o(100,100);return createImageBitmap(s,0,0,100,100,{resizeWidth:10,resizeHeight:10,resizeQuality:"high"}).then(function(f){var d=f.width===10;return f.close(),s=null,d})}).catch(function(){return!1})},n.exports.worker_offscreen_canvas_support=function(){return new Promise(function(o,s){if(typeof OffscreenCanvas>"u"){o(!1);return}function f(g){if(typeof createImageBitmap>"u"){g.postMessage(!1);return}Promise.resolve().then(function(){var A=new OffscreenCanvas(10,10),p=A.getContext("2d");return p.rect(0,0,1,1),createImageBitmap(A,0,0,1,1)}).then(function(){return g.postMessage(!0)},function(){return g.postMessage(!1)})}var d=btoa("(".concat(f.toString(),")(self);")),c=new Worker("data:text/javascript;base64,".concat(d));c.onmessage=function(g){return o(g.data)},c.onerror=s}).then(function(o){return o},function(){return!1})},n.exports.can_use_canvas=function(o){var s=!1;try{var f=o(2,1),d=f.getContext("2d"),c=d.createImageData(2,1);c.data[0]=12,c.data[1]=23,c.data[2]=34,c.data[3]=255,c.data[4]=45,c.data[5]=56,c.data[6]=67,c.data[7]=255,d.putImageData(c,0,0),c=null,c=d.getImageData(0,0,2,1),c.data[0]===12&&c.data[1]===23&&c.data[2]===34&&c.data[3]===255&&c.data[4]===45&&c.data[5]===56&&c.data[6]===67&&c.data[7]===255&&(s=!0)}catch{}return s},n.exports.cib_can_use_region=function(){return new Promise(function(o){if(typeof Image>"u"||typeof createImageBitmap>"u"){o(!1);return}var s=new Image;s.src="data:image/jpeg;base64,/9j/4QBiRXhpZgAATU0AKgAAAAgABQESAAMAAAABAAYAAAEaAAUAAAABAAAASgEbAAUAAAABAAAAUgEoAAMAAAABAAIAAAITAAMAAAABAAEAAAAAAAAAAABIAAAAAQAAAEgAAAAB/9sAQwAEAwMEAwMEBAMEBQQEBQYKBwYGBgYNCQoICg8NEBAPDQ8OERMYFBESFxIODxUcFRcZGRsbGxAUHR8dGh8YGhsa/9sAQwEEBQUGBQYMBwcMGhEPERoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoa/8IAEQgAAQACAwERAAIRAQMRAf/EABQAAQAAAAAAAAAAAAAAAAAAAAf/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIQAxAAAAF/P//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAQUCf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Bf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEABj8Cf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAT8hf//aAAwDAQACAAMAAAAQH//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Qf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Qf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAT8Qf//Z",s.onload=function(){createImageBitmap(s,0,0,s.width,s.height).then(function(f){f.width===s.width&&f.height===s.height?o(!0):o(!1)},function(){return o(!1)})},s.onerror=function(){return o(!1)}})}},{}],17:[function(t,n,a){n.exports=function(){var i=t("./mathlib"),u;onmessage=function(s){var f=s.data.opts;if(!f.src&&f.srcBitmap){var d=new OffscreenCanvas(f.width,f.height),c=d.getContext("2d");c.drawImage(f.srcBitmap,0,0),f.src=c.getImageData(0,0,f.width,f.height).data,d.width=d.height=0,d=null,f.srcBitmap.close(),f.srcBitmap=null}u||(u=new i(s.data.features));var g=u.resizeAndUnsharp(f);postMessage({data:g},[g.buffer])}}},{"./mathlib":1}],18:[function(t,n,a){var i,u,o,s,f,d,c,g;function A(y){y<.5&&(y=.5);var I=Math.exp(.726*.726)/y,E=Math.exp(-I),_=Math.exp(-2*I),b=(1-E)*(1-E)/(1+2*I*E-_);return i=b,u=b*(I-1)*E,o=b*(I+1)*E,s=-b*_,f=2*E,d=-_,c=(i+u)/(1-f-d),g=(o+s)/(1-f-d),new Float32Array([i,u,o,s,f,d,c,g])}function p(y,I,E,_,b,P){var x,h,l,D,B,C,R,Q,$,F,v,S,w,O;for($=0;$<P;$++){for(C=$*b,R=$,Q=0,x=y[C],B=x*_[6],D=B,v=_[0],S=_[1],w=_[4],O=_[5],F=0;F<b;F++)h=y[C],l=h*v+x*S+D*w+B*O,B=D,D=l,x=h,E[Q]=D,Q++,C++;for(C--,Q--,R+=P*(b-1),x=y[C],B=x*_[7],D=B,h=x,v=_[2],S=_[3],F=b-1;F>=0;F--)l=h*v+x*S+D*w+B*O,B=D,D=l,x=h,h=y[C],I[R]=E[Q]+D,C--,Q--,R-=P}}function m(y,I,E,_){if(_){var b=new Uint16Array(y.length),P=new Float32Array(Math.max(I,E)),x=A(_);p(y,b,P,x,I,E),p(b,y,P,x,E,I)}}n.exports=m},{}],19:[function(t,n,a){var i=t("object-assign"),u=t("./lib/base64decode"),o=t("./lib/wa_detect"),s={js:!0,wasm:!0};function f(d){if(!(this instanceof f))return new f(d);var c=i({},s,d||{});if(this.options=c,this.__cache={},this.__init_promise=null,this.__modules=c.modules||{},this.__memory=null,this.__wasm={},this.__isLE=new Uint32Array(new Uint8Array([1,0,0,0]).buffer)[0]===1,!this.options.js&&!this.options.wasm)throw new Error('mathlib: at least "js" or "wasm" should be enabled')}f.prototype.has_wasm=o,f.prototype.use=function(d){return this.__modules[d.name]=d,this.options.wasm&&this.has_wasm()&&d.wasm_fn?this[d.name]=d.wasm_fn:this[d.name]=d.fn,this},f.prototype.init=function(){if(this.__init_promise)return this.__init_promise;if(!this.options.js&&this.options.wasm&&!this.has_wasm())return Promise.reject(new Error(`mathlib: only "wasm" was enabled, but it's not supported`));var d=this;return this.__init_promise=Promise.all(Object.keys(d.__modules).map(function(c){var g=d.__modules[c];return!d.options.wasm||!d.has_wasm()||!g.wasm_fn||d.__wasm[c]?null:WebAssembly.compile(d.__base64decode(g.wasm_src)).then(function(A){d.__wasm[c]=A})})).then(function(){return d}),this.__init_promise},f.prototype.__base64decode=u,f.prototype.__reallocate=function(c){if(!this.__memory)return this.__memory=new WebAssembly.Memory({initial:Math.ceil(c/(64*1024))}),this.__memory;var g=this.__memory.buffer.byteLength;return g<c&&this.__memory.grow(Math.ceil((c-g)/(64*1024))),this.__memory},f.prototype.__instance=function(c,g,A){if(g&&this.__reallocate(g),!this.__wasm[c]){var p=this.__modules[c];this.__wasm[c]=new WebAssembly.Module(this.__base64decode(p.wasm_src))}if(!this.__cache[c]){var m={memoryBase:0,memory:this.__memory,tableBase:0,table:new WebAssembly.Table({initial:0,element:"anyfunc"})};this.__cache[c]=new WebAssembly.Instance(this.__wasm[c],{env:i(m,A||{})})}return this.__cache[c]},f.prototype.__align=function(c,g){g=g||8;var A=c%g;return c+(A?g-A:0)},n.exports=f},{"./lib/base64decode":20,"./lib/wa_detect":21,"object-assign":22}],20:[function(t,n,a){var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";n.exports=function(o){for(var s=o.replace(/[\r\n=]/g,""),f=s.length,d=new Uint8Array(f*3>>2),c=0,g=0,A=0;A<f;A++)A%4===0&&A&&(d[g++]=c>>16&255,d[g++]=c>>8&255,d[g++]=c&255),c=c<<6|i.indexOf(s.charAt(A));var p=f%4*6;return p===0?(d[g++]=c>>16&255,d[g++]=c>>8&255,d[g++]=c&255):p===18?(d[g++]=c>>10&255,d[g++]=c>>2&255):p===12&&(d[g++]=c>>4&255),d}},{}],21:[function(t,n,a){var i;n.exports=function(){if(typeof i<"u"||(i=!1,typeof WebAssembly>"u"))return i;try{var o=new Uint8Array([0,97,115,109,1,0,0,0,1,6,1,96,1,127,1,127,3,2,1,0,5,3,1,0,1,7,8,1,4,116,101,115,116,0,0,10,16,1,14,0,32,0,65,1,54,2,0,32,0,40,2,0,11]),s=new WebAssembly.Module(o),f=new WebAssembly.Instance(s,{});return f.exports.test(4)!==0&&(i=!0),i}catch{}return i}},{}],22:[function(t,n,a){var i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function s(d){if(d==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(d)}function f(){try{if(!Object.assign)return!1;var d=new String("abc");if(d[5]="de",Object.getOwnPropertyNames(d)[0]==="5")return!1;for(var c={},g=0;g<10;g++)c["_"+String.fromCharCode(g)]=g;var A=Object.getOwnPropertyNames(c).map(function(m){return c[m]});if(A.join("")!=="0123456789")return!1;var p={};return"abcdefghijklmnopqrst".split("").forEach(function(m){p[m]=m}),Object.keys(Object.assign({},p)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}n.exports=f()?Object.assign:function(d,c){for(var g,A=s(d),p,m=1;m<arguments.length;m++){g=Object(arguments[m]);for(var y in g)u.call(g,y)&&(A[y]=g[y]);if(i){p=i(g);for(var I=0;I<p.length;I++)o.call(g,p[I])&&(A[p[I]]=g[p[I]])}}return A}},{}],23:[function(t,n,a){var i=arguments[3],u=arguments[4],o=arguments[5],s=JSON.stringify;n.exports=function(f,d){for(var c,g=Object.keys(o),A=0,p=g.length;A<p;A++){var m=g[A],y=o[m].exports;if(y===f||y&&y.default===f){c=m;break}}if(!c){c=Math.floor(Math.pow(16,8)*Math.random()).toString(16);for(var I={},A=0,p=g.length;A<p;A++){var m=g[A];I[m]=m}u[c]=["function(require,module,exports){"+f+"(self); }",I]}var E=Math.floor(Math.pow(16,8)*Math.random()).toString(16),_={};_[c]=c,u[E]=["function(require,module,exports){var f = require("+s(c)+");(f.default ? f.default : f)(self);}",_];var b={};P(E);function P(C){b[C]=!0;for(var R in u[C][1]){var Q=u[C][1][R];b[Q]||P(Q)}}var x="("+i+")({"+Object.keys(b).map(function(C){return s(C)+":["+u[C][0]+","+s(u[C][1])+"]"}).join(",")+"},{},["+s(E)+"])",h=window.URL||window.webkitURL||window.mozURL||window.msURL,l=new Blob([x],{type:"text/javascript"});if(d&&d.bare)return l;var D=h.createObjectURL(l),B=new Worker(D);return B.objectURL=D,B}},{}],"/index.js":[function(t,n,a){function i(v,S){return d(v)||f(v,S)||o(v,S)||u()}function u(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function o(v,S){if(v){if(typeof v=="string")return s(v,S);var w=Object.prototype.toString.call(v).slice(8,-1);if(w==="Object"&&v.constructor&&(w=v.constructor.name),w==="Map"||w==="Set")return Array.from(v);if(w==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(w))return s(v,S)}}function s(v,S){(S==null||S>v.length)&&(S=v.length);for(var w=0,O=new Array(S);w<S;w++)O[w]=v[w];return O}function f(v,S){var w=v==null?null:typeof Symbol<"u"&&v[Symbol.iterator]||v["@@iterator"];if(w!=null){var O=[],T=!0,U=!1,N,L;try{for(w=w.call(v);!(T=(N=w.next()).done)&&(O.push(N.value),!(S&&O.length===S));T=!0);}catch(z){U=!0,L=z}finally{try{!T&&w.return!=null&&w.return()}finally{if(U)throw L}}return O}}function d(v){if(Array.isArray(v))return v}var c=t("object-assign"),g=t("webworkify"),A=t("./lib/mathlib"),p=t("./lib/pool"),m=t("./lib/utils"),y=t("./lib/worker"),I=t("./lib/stepper"),E=t("./lib/tiler"),_=t("./lib/mm_resize/resize_filter_info"),b={},P=!1;try{typeof navigator<"u"&&navigator.userAgent&&(P=navigator.userAgent.indexOf("Safari")>=0)}catch{}var x=1;typeof navigator<"u"&&(x=Math.min(navigator.hardwareConcurrency||1,4));var h={tile:1024,concurrency:x,features:["js","wasm","ww"],idle:2e3,createCanvas:function(S,w){var O=document.createElement("canvas");return O.width=S,O.height=w,O}},l={filter:"mks2013",unsharpAmount:0,unsharpRadius:0,unsharpThreshold:0},D=!1,B=!1,C=!1,R=!1,Q=!1;function $(){return{value:g(y),destroy:function(){if(this.value.terminate(),typeof window<"u"){var S=window.URL||window.webkitURL||window.mozURL||window.msURL;S&&S.revokeObjectURL&&this.value.objectURL&&S.revokeObjectURL(this.value.objectURL)}}}}function F(v){if(!(this instanceof F))return new F(v);this.options=c({},h,v||{});var S="lk_".concat(this.options.concurrency);this.__limit=b[S]||m.limiter(this.options.concurrency),b[S]||(b[S]=this.__limit),this.features={js:!1,wasm:!1,cib:!1,ww:!1},this.__workersPool=null,this.__requested_features=[],this.__mathlib=null}F.prototype.init=function(){var v=this;if(this.__initPromise)return this.__initPromise;if(typeof ImageData<"u"&&typeof Uint8ClampedArray<"u")try{new ImageData(new Uint8ClampedArray(400),10,10),D=!0}catch{}typeof ImageBitmap<"u"&&(ImageBitmap.prototype&&ImageBitmap.prototype.close?B=!0:this.debug("ImageBitmap does not support .close(), disabled"));var S=this.options.features.slice();if(S.indexOf("all")>=0&&(S=["cib","wasm","js","ww"]),this.__requested_features=S,this.__mathlib=new A(S),S.indexOf("ww")>=0&&typeof window<"u"&&"Worker"in window)try{var w=t("webworkify")(function(){});w.terminate(),this.features.ww=!0;var O="wp_".concat(JSON.stringify(this.options));b[O]?this.__workersPool=b[O]:(this.__workersPool=new p($,this.options.idle),b[O]=this.__workersPool)}catch{}var T=this.__mathlib.init().then(function(z){c(v.features,z.features)}),U;B?U=m.cib_support(this.options.createCanvas).then(function(z){if(v.features.cib&&S.indexOf("cib")<0){v.debug("createImageBitmap() resize supported, but disabled by config");return}S.indexOf("cib")>=0&&(v.features.cib=z)}):U=Promise.resolve(!1),C=m.can_use_canvas(this.options.createCanvas);var N;B&&D&&S.indexOf("ww")!==-1?N=m.worker_offscreen_canvas_support():N=Promise.resolve(!1),N=N.then(function(z){R=z});var L=m.cib_can_use_region().then(function(z){Q=z});return this.__initPromise=Promise.all([T,U,N,L]).then(function(){return v}),this.__initPromise},F.prototype.__invokeResize=function(v,S){var w=this;return S.__mathCache=S.__mathCache||{},Promise.resolve().then(function(){return w.features.ww?new Promise(function(O,T){var U=w.__workersPool.acquire();S.cancelToken&&S.cancelToken.catch(function(L){return T(L)}),U.value.onmessage=function(L){U.release(),L.data.err?T(L.data.err):O(L.data)};var N=[];v.src&&N.push(v.src.buffer),v.srcBitmap&&N.push(v.srcBitmap),U.value.postMessage({opts:v,features:w.__requested_features,preload:{wasm_nodule:w.__mathlib.__}},N)}):{data:w.__mathlib.resizeAndUnsharp(v,S.__mathCache)}})},F.prototype.__extractTileData=function(v,S,w,O,T){if(this.features.ww&&R&&(m.isCanvas(S)||Q))return this.debug("Create tile for OffscreenCanvas"),createImageBitmap(O.srcImageBitmap||S,v.x,v.y,v.width,v.height).then(function(L){return T.srcBitmap=L,T});if(m.isCanvas(S))return O.srcCtx||(O.srcCtx=S.getContext("2d")),this.debug("Get tile pixel data"),T.src=O.srcCtx.getImageData(v.x,v.y,v.width,v.height).data,T;this.debug("Draw tile imageBitmap/image to temporary canvas");var U=this.options.createCanvas(v.width,v.height),N=U.getContext("2d");return N.globalCompositeOperation="copy",N.drawImage(O.srcImageBitmap||S,v.x,v.y,v.width,v.height,0,0,v.width,v.height),this.debug("Get tile pixel data"),T.src=N.getImageData(0,0,v.width,v.height).data,U.width=U.height=0,T},F.prototype.__landTileData=function(v,S,w){var O;if(this.debug("Convert raw rgba tile result to ImageData"),S.bitmap)return w.toCtx.drawImage(S.bitmap,v.toX,v.toY),null;if(D)O=new ImageData(new Uint8ClampedArray(S.data),v.toWidth,v.toHeight);else if(O=w.toCtx.createImageData(v.toWidth,v.toHeight),O.data.set)O.data.set(S.data);else for(var T=O.data.length-1;T>=0;T--)O.data[T]=S.data[T];return this.debug("Draw tile"),P?w.toCtx.putImageData(O,v.toX,v.toY,v.toInnerX-v.toX,v.toInnerY-v.toY,v.toInnerWidth+1e-5,v.toInnerHeight+1e-5):w.toCtx.putImageData(O,v.toX,v.toY,v.toInnerX-v.toX,v.toInnerY-v.toY,v.toInnerWidth,v.toInnerHeight),null},F.prototype.__tileAndResize=function(v,S,w){var O=this,T={srcCtx:null,srcImageBitmap:null,isImageBitmapReused:!1,toCtx:null},U=function(L){return O.__limit(function(){if(w.canceled)return w.cancelToken;var z={width:L.width,height:L.height,toWidth:L.toWidth,toHeight:L.toHeight,scaleX:L.scaleX,scaleY:L.scaleY,offsetX:L.offsetX,offsetY:L.offsetY,filter:w.filter,unsharpAmount:w.unsharpAmount,unsharpRadius:w.unsharpRadius,unsharpThreshold:w.unsharpThreshold};return O.debug("Invoke resize math"),Promise.resolve(z).then(function(X){return O.__extractTileData(L,v,w,T,X)}).then(function(X){return O.debug("Invoke resize math"),O.__invokeResize(X,w)}).then(function(X){return w.canceled?w.cancelToken:(T.srcImageData=null,O.__landTileData(L,X,T))})})};return Promise.resolve().then(function(){if(T.toCtx=S.getContext("2d"),m.isCanvas(v))return null;if(m.isImageBitmap(v))return T.srcImageBitmap=v,T.isImageBitmapReused=!0,null;if(m.isImage(v))return B?(O.debug("Decode image via createImageBitmap"),createImageBitmap(v).then(function(N){T.srcImageBitmap=N}).catch(function(N){return null})):null;throw new Error('Pica: ".from" should be Image, Canvas or ImageBitmap')}).then(function(){if(w.canceled)return w.cancelToken;O.debug("Calculate tiles");var N=E({width:w.width,height:w.height,srcTileSize:O.options.tile,toWidth:w.toWidth,toHeight:w.toHeight,destTileBorder:w.__destTileBorder}),L=N.map(function(X){return U(X)});function z(X){X.srcImageBitmap&&(X.isImageBitmapReused||X.srcImageBitmap.close(),X.srcImageBitmap=null)}return O.debug("Process tiles"),Promise.all(L).then(function(){return O.debug("Finished!"),z(T),S},function(X){throw z(T),X})})},F.prototype.__processStages=function(v,S,w,O){var T=this;if(O.canceled)return O.cancelToken;var U=v.shift(),N=i(U,2),L=N[0],z=N[1],X=v.length===0,ee;X||_.q2f.indexOf(O.filter)<0?ee=O.filter:O.filter==="box"?ee="box":ee="hamming",O=c({},O,{toWidth:L,toHeight:z,filter:ee});var ie;return X||(ie=this.options.createCanvas(L,z)),this.__tileAndResize(S,X?w:ie,O).then(function(){return X?w:(O.width=L,O.height=z,T.__processStages(v,ie,w,O))}).then(function(we){return ie&&(ie.width=ie.height=0),we})},F.prototype.__resizeViaCreateImageBitmap=function(v,S,w){var O=this,T=S.getContext("2d");return this.debug("Resize via createImageBitmap()"),createImageBitmap(v,{resizeWidth:w.toWidth,resizeHeight:w.toHeight,resizeQuality:m.cib_quality_name(_.f2q[w.filter])}).then(function(U){if(w.canceled)return w.cancelToken;if(!w.unsharpAmount)return T.drawImage(U,0,0),U.close(),T=null,O.debug("Finished!"),S;O.debug("Unsharp result");var N=O.options.createCanvas(w.toWidth,w.toHeight),L=N.getContext("2d");L.drawImage(U,0,0),U.close();var z=L.getImageData(0,0,w.toWidth,w.toHeight);return O.__mathlib.unsharp_mask(z.data,w.toWidth,w.toHeight,w.unsharpAmount,w.unsharpRadius,w.unsharpThreshold),T.putImageData(z,0,0),N.width=N.height=0,z=L=N=T=null,O.debug("Finished!"),S})},F.prototype.resize=function(v,S,w){var O=this;this.debug("Start resize...");var T=c({},l);if(isNaN(w)?w&&(T=c(T,w)):T=c(T,{quality:w}),T.toWidth=S.width,T.toHeight=S.height,T.width=v.naturalWidth||v.width,T.height=v.naturalHeight||v.height,Object.prototype.hasOwnProperty.call(T,"quality")){if(T.quality<0||T.quality>3)throw new Error("Pica: .quality should be [0..3], got ".concat(T.quality));T.filter=_.q2f[T.quality]}if(S.width===0||S.height===0)return Promise.reject(new Error("Invalid output size: ".concat(S.width,"x").concat(S.height)));T.unsharpRadius>2&&(T.unsharpRadius=2),T.canceled=!1,T.cancelToken&&(T.cancelToken=T.cancelToken.then(function(N){throw T.canceled=!0,N},function(N){throw T.canceled=!0,N}));var U=3;return T.__destTileBorder=Math.ceil(Math.max(U,2.5*T.unsharpRadius|0)),this.init().then(function(){if(T.canceled)return T.cancelToken;if(O.features.cib){if(_.q2f.indexOf(T.filter)>=0)return O.__resizeViaCreateImageBitmap(v,S,T);O.debug("cib is enabled, but not supports provided filter, fallback to manual math")}if(!C){var N=new Error("Pica: cannot use getImageData on canvas, make sure fingerprinting protection isn't enabled");throw N.code="ERR_GET_IMAGE_DATA",N}var L=I(T.width,T.height,T.toWidth,T.toHeight,O.options.tile,T.__destTileBorder);return O.__processStages(L,v,S,T)})},F.prototype.resizeBuffer=function(v){var S=this,w=c({},l,v);if(Object.prototype.hasOwnProperty.call(w,"quality")){if(w.quality<0||w.quality>3)throw new Error("Pica: .quality should be [0..3], got ".concat(w.quality));w.filter=_.q2f[w.quality]}return this.init().then(function(){return S.__mathlib.resizeAndUnsharp(w)})},F.prototype.toBlob=function(v,S,w){return S=S||"image/png",new Promise(function(O){if(v.toBlob){v.toBlob(function(z){return O(z)},S,w);return}if(v.convertToBlob){O(v.convertToBlob({type:S,quality:w}));return}for(var T=atob(v.toDataURL(S,w).split(",")[1]),U=T.length,N=new Uint8Array(U),L=0;L<U;L++)N[L]=T.charCodeAt(L);O(new Blob([N],{type:S}))})},F.prototype.debug=function(){},n.exports=F},{"./lib/mathlib":1,"./lib/mm_resize/resize_filter_info":7,"./lib/pool":13,"./lib/stepper":14,"./lib/tiler":15,"./lib/utils":16,"./lib/worker":17,"object-assign":22,webworkify:23}]},{},[])("/index.js")})})(Vr);var Bu=Vr.exports;const Pu=wr(Bu);var yt={exports:{}};(function(e,r){Object.defineProperty(r,"__esModule",{value:!0});function t(h){return typeof h=="object"&&!("toString"in h)?Object.prototype.toString.call(h).slice(8,-1):h}var n=typeof process=="object"&&!0;function a(h,l){if(!h)throw n?new Error("Invariant failed"):new Error(l())}r.invariant=a;var i=Object.prototype.hasOwnProperty,u=Array.prototype.splice,o=Object.prototype.toString;function s(h){return o.call(h).slice(8,-1)}var f=Object.assign||function(h,l){return d(l).forEach(function(D){i.call(l,D)&&(h[D]=l[D])}),h},d=typeof Object.getOwnPropertySymbols=="function"?function(h){return Object.keys(h).concat(Object.getOwnPropertySymbols(h))}:function(h){return Object.keys(h)};function c(h){return Array.isArray(h)?f(h.constructor(h.length),h):s(h)==="Map"?new Map(h):s(h)==="Set"?new Set(h):h&&typeof h=="object"?f(Object.create(Object.getPrototypeOf(h)),h):h}var g=function(){function h(){this.commands=f({},A),this.update=this.update.bind(this),this.update.extend=this.extend=this.extend.bind(this),this.update.isEquals=function(l,D){return l===D},this.update.newContext=function(){return new h().update}}return Object.defineProperty(h.prototype,"isEquals",{get:function(){return this.update.isEquals},set:function(l){this.update.isEquals=l},enumerable:!0,configurable:!0}),h.prototype.extend=function(l,D){this.commands[l]=D},h.prototype.update=function(l,D){var B=this,C=typeof D=="function"?{$apply:D}:D;Array.isArray(l)&&Array.isArray(C)||a(!Array.isArray(C),function(){return"update(): You provided an invalid spec to update(). The spec may not contain an array except as the value of $set, $push, $unshift, $splice or any custom command allowing an array value."}),a(typeof C=="object"&&C!==null,function(){return"update(): You provided an invalid spec to update(). The spec and every included key path must be plain objects containing one of the "+("following commands: "+Object.keys(B.commands).join(", ")+".")});var R=l;return d(C).forEach(function(Q){if(i.call(B.commands,Q)){var $=l===R;R=B.commands[Q](C[Q],R,C,l),$&&B.isEquals(R,l)&&(R=l)}else{var F=s(l)==="Map"?B.update(l.get(Q),C[Q]):B.update(l[Q],C[Q]),v=s(R)==="Map"?R.get(Q):R[Q];(!B.isEquals(F,v)||typeof F>"u"&&!i.call(l,Q))&&(R===l&&(R=c(l)),s(R)==="Map"?R.set(Q,F):R[Q]=F)}}),R},h}();r.Context=g;var A={$push:function(h,l,D){return m(l,D,"$push"),h.length?l.concat(h):l},$unshift:function(h,l,D){return m(l,D,"$unshift"),h.length?h.concat(l):l},$splice:function(h,l,D,B){return I(l,D),h.forEach(function(C){E(C),l===B&&C.length&&(l=c(B)),u.apply(l,C)}),l},$set:function(h,l,D){return b(D),h},$toggle:function(h,l){y(h,"$toggle");var D=h.length?c(l):l;return h.forEach(function(B){D[B]=!l[B]}),D},$unset:function(h,l,D,B){return y(h,"$unset"),h.forEach(function(C){Object.hasOwnProperty.call(l,C)&&(l===B&&(l=c(B)),delete l[C])}),l},$add:function(h,l,D,B){return x(l,"$add"),y(h,"$add"),s(l)==="Map"?h.forEach(function(C){var R=C[0],Q=C[1];l===B&&l.get(R)!==Q&&(l=c(B)),l.set(R,Q)}):h.forEach(function(C){l===B&&!l.has(C)&&(l=c(B)),l.add(C)}),l},$remove:function(h,l,D,B){return x(l,"$remove"),y(h,"$remove"),h.forEach(function(C){l===B&&l.has(C)&&(l=c(B)),l.delete(C)}),l},$merge:function(h,l,D,B){return P(l,h),d(h).forEach(function(C){h[C]!==l[C]&&(l===B&&(l=c(B)),l[C]=h[C])}),l},$apply:function(h,l){return _(h),h(l)}},p=new g;r.isEquals=p.update.isEquals,r.extend=p.extend,r.default=p.update,r.default.default=e.exports=f(r.default,r);function m(h,l,D){a(Array.isArray(h),function(){return"update(): expected target of "+t(D)+" to be an array; got "+t(h)+"."}),y(l[D],D)}function y(h,l){a(Array.isArray(h),function(){return"update(): expected spec of "+t(l)+" to be an array; got "+t(h)+". Did you forget to wrap your parameter in an array?"})}function I(h,l){a(Array.isArray(h),function(){return"Expected $splice target to be an array; got "+t(h)}),E(l.$splice)}function E(h){a(Array.isArray(h),function(){return"update(): expected spec of $splice to be an array of arrays; got "+t(h)+". Did you forget to wrap your parameters in an array?"})}function _(h){a(typeof h=="function",function(){return"update(): expected spec of $apply to be a function; got "+t(h)+"."})}function b(h){a(Object.keys(h).length===1,function(){return"Cannot have more than one key in an object with $set"})}function P(h,l){a(l&&typeof l=="object",function(){return"update(): $merge expects a spec of type 'object'; got "+t(l)}),a(h&&typeof h=="object",function(){return"update(): $merge expects a target of type 'object'; got "+t(h)})}function x(h,l){var D=s(h);a(D==="Map"||D==="Set",function(){return"update(): "+t(l)+" expects a target of type Set or Map; got "+t(D)})}})(yt,yt.exports);var xu=yt.exports;const ku=wr(xu),Ir={CARD:"card"},Qu=({id:e,image:r,index:t,moveCard:n,handleRemoveImage:a})=>{const i=M.useRef(null),[{handlerId:u},o]=tu({accept:Ir.CARD,collect(f){return{handlerId:f.getHandlerId()}},hover(f,d){var I;if(!i.current)return;const c=f.index,g=t;if(c===g)return;const A=(I=i.current)==null?void 0:I.getBoundingClientRect(),p=(A.bottom-A.top)/2,y=d.getClientOffset().y-A.top;c<g&&y<p||c>g&&y>p||(n(c,g),f.index=g)}}),[{},s]=jo({type:Ir.CARD,item:()=>({id:e,index:t}),collect:f=>({isDragging:f.isDragging()})});return s(o(i)),W.jsx("div",{ref:i,"data-handler-id":u,children:W.jsxs("div",{className:"relative",children:[W.jsx("img",{className:"inline-flex border rounded-md border-gray-100 dark:border-gray-600 w-24 max-h-24 p-2 m-2",src:r,alt:"product"}),t===0&&W.jsx("p",{className:"text-xs absolute py-1 w-full bottom-0 inset-x-0 bg-blue-500 rounded-full text-white text-center ",children:"Default Image"}),W.jsx("button",{type:"button",className:"absolute top-0 right-0 text-red-500 focus:outline-none",onClick:()=>a(r),children:W.jsx(Er,{})})]})})},Mu=({setImageUrl:e,imageUrl:r,handleRemoveImage:t})=>{const n=M.useCallback((i,u)=>{e(o=>ku(o,{$splice:[[i,1],[u,0,o[i]]]}))},[e]),a=M.useCallback((i,u)=>W.jsx(Qu,{index:u,id:i.id,text:i.text,moveCard:n,image:i,handleRemoveImage:t},u+1),[n,t]);return W.jsx(W.Fragment,{children:r.map((i,u)=>a(i,u))})},Hu=({setImageUrl:e,imageUrl:r,product:t,folder:n,targetWidth:a=800,targetHeight:i=800})=>{const[u,o]=M.useState([]),[s,f]=M.useState(!1),[d,c]=M.useState(""),g=Pu(),{globalSetting:A}=un(),{getRootProps:p,getInputProps:m,fileRejections:y}=Qr({accept:{"image/*":[".jpeg",".jpg",".png",".webp"]},multiple:!!t,maxSize:5242880,maxFiles:(A==null?void 0:A.number_of_image_per_product)||2,onDrop:async b=>{const P=await Promise.all(b.map(x=>I(x,a,i)));o(P.map(x=>Object.assign(x,{preview:URL.createObjectURL(x)})))}}),I=async(b,P,x)=>{const h=new Image;h.src=URL.createObjectURL(b),await h.decode();const l=document.createElement("canvas");return l.width=P,l.height=x,new Promise(D=>{g.resize(h,l,{unsharpAmount:80,unsharpRadius:.6,unsharpThreshold:2}).then(B=>g.toBlob(B,b.type,.9)).then(B=>{const C=new File([B],b.name,{type:b.type});D(C)})})};M.useEffect(()=>{y&&y.map(({file:b,errors:P})=>W.jsxs("li",{children:[b.path," - ",b.size," bytes",W.jsx("ul",{children:P.map(x=>W.jsx("li",{children:x.code==="too-many-files"?le(`Maximum ${A==null?void 0:A.number_of_image_per_product} Image Can be Upload!`):le(x.message)},x.code))})]},b.path)),u&&u.forEach(b=>{if(t&&(r==null?void 0:r.length)+(u==null?void 0:u.length)>(A==null?void 0:A.number_of_image_per_product))return le(`Maximum ${A==null?void 0:A.number_of_image_per_product} Image Can be Upload!`);f(!0),c("Uploading....");const P=b.name.replaceAll(/\s/g,""),x=P==null?void 0:P.substring(0,P.lastIndexOf(".")),h=new FormData;h.append("file",b),h.append("upload_preset","your_cloudinary_upload_preset"),h.append("cloud_name","your_cloudinary_cloud_name"),h.append("folder",n),h.append("public_id",x),on({url:"https://api.cloudinary.com/v1_1/your_cloudinary_cloud_name/image/upload",method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:h}).then(l=>{cn("Image Uploaded successfully!"),f(!1),e(t?D=>[...D,l.data.secure_url]:l.data.secure_url)}).catch(l=>{console.error("err",l),le(l.Message),f(!1)})})},[u]);const E=u.map(b=>W.jsx("div",{children:W.jsx("div",{children:W.jsx("img",{className:"inline-flex border-2 border-gray-100 w-24 max-h-24",src:b.preview,alt:b.name})})},b.name));M.useEffect(()=>()=>{u.forEach(b=>URL.revokeObjectURL(b.preview))},[u]);const _=async b=>{try{if(f(!1),le("Image delete successfully!"),t){const P=r==null?void 0:r.filter(x=>x!==b);e(P)}else e("")}catch(P){console.error("err",P),le(P.Message),f(!1)}};return W.jsxs("div",{className:"w-full text-center",children:[W.jsxs("div",{className:"border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md cursor-pointer px-6 pt-5 pb-6",...p(),children:[W.jsx("input",{...m()}),W.jsx("span",{className:"mx-auto flex justify-center",children:W.jsx(sn,{className:"text-3xl text-emerald-500"})}),W.jsx("p",{className:"text-sm mt-2",children:xt("DragYourImage")}),W.jsx("em",{className:"text-xs text-gray-400",children:xt("imageFormat")})]}),W.jsx("div",{className:"text-emerald-500",children:s&&d}),W.jsx("aside",{className:"flex flex-row flex-wrap mt-4",children:t?W.jsx(Mi,{backend:Tu,children:W.jsx(Mu,{setImageUrl:e,imageUrl:r,handleRemoveImage:_})}):!t&&r?W.jsxs("div",{className:"relative",children:[W.jsx("img",{className:"inline-flex border rounded-md border-gray-100 dark:border-gray-600 w-24 max-h-24 p-2",src:r,alt:"product"}),W.jsx("button",{type:"button",className:"absolute top-0 right-0 text-red-500 focus:outline-none",onClick:()=>_(r),children:W.jsx(Er,{})})]}):E})]})};export{Hu as U};
