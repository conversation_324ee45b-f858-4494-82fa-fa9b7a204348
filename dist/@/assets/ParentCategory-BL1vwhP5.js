import{o as ve,a as Fr,g as Vr,H as w,r as p,v as $r,w as we,I as rt,J as qe,E as Xe,x as ge,K as M,M as V,F as mn,D as Ke,j as $e}from"./index-DD5OQCzb.js";import{c as gt,_ as ot,b as fe,w as be,d as He,e as ze,o as Br,K as We}from"./DrawerButton-C1kY46U5.js";import{u as zr}from"./useAsync-CdFiuEZy.js";import{n as Hr}from"./toast-C_V_NPJL.js";import{C as jr}from"./ProductServices-CGXRs0W4.js";import{u as Wr}from"./Layout-f_j_aP34.js";var Fe={wrapper:"react-tag-input",input:"react-tag-input__input",tag:"react-tag-input__tag",tagContent:"react-tag-input__tag__content",tagRemove:"react-tag-input__tag__remove",tagRemoveReadOnly:"react-tag-input__tag__remove-readonly"};function Ur(t){return t.replace(/(\r\n|\n|\r)/gm,"")}var Gr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function Yr(t){return String(t).replace(/[&<>"'`=\/]/g,function(r){return Gr[r]})}function yn(t){return Yr(Ur(t))}var qr=function(){var t=function(r,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var i in o)o.hasOwnProperty(i)&&(e[i]=o[i])},t(r,n)};return function(r,n){t(r,n);function e(){this.constructor=r}r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}}(),Xr=function(t){qr(r,t);function r(){var n=t!==null&&t.apply(this,arguments)||this;return n.focused=!1,n.removed=!1,n.preFocusedValue="",n.onPaste=function(e){e.preventDefault();var o=e.clipboardData.getData("text/plain");document.execCommand("insertHTML",!1,yn(o))},n.onFocus=function(){n.preFocusedValue=n.getValue(),n.focused=!0},n.onBlur=function(){n.focused=!1;var e=n.props.innerEditableRef.current,o=n.props,i=o.validator,s=o.change;if(!n.removed&&e){if(e.innerText===""){n.props.remove();return}if(i){var a=i(n.getValue());if(!a){e.innerText=n.preFocusedValue;return}}s(e.innerText)}},n.onKeyDown=function(e){if(e.keyCode===13){e.preventDefault(),n.focusInputRef();return}var o=n.props.removeOnBackspace,i=n.getValue();if(o&&e.keyCode===8&&i===""){n.removed=!0,n.props.remove(),n.focusInputRef();return}},n.getValue=function(){var e=n.getRef();return e?e.innerText:""},n.getRef=function(){return n.props.innerEditableRef.current},n.focusInputRef=function(){var e=n.props.inputRef;e&&e.current&&e.current.focus()},n}return r.prototype.componentDidMount=function(){this.preFocusedValue=this.getValue()},r.prototype.render=function(){var n=this.props,e=n.value,o=n.className,i=n.innerEditableRef;return ve.createElement("div",{ref:i,className:o,contentEditable:!0,onPaste:this.onPaste,onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,dangerouslySetInnerHTML:{__html:yn(e)}})},r}(ve.Component),Qr=function(){var t=function(r,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var i in o)o.hasOwnProperty(i)&&(e[i]=o[i])},t(r,n)};return function(r,n){t(r,n);function e(){this.constructor=r}r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}}(),Jr=function(t){Qr(r,t);function r(){var n=t!==null&&t.apply(this,arguments)||this;return n.innerEditableRef=ve.createRef(),n.remove=function(){return n.props.remove(n.props.index)},n}return r.prototype.render=function(){var n=this.props,e=n.value,o=n.index,i=n.editable,s=n.inputRef,a=n.validator,l=n.update,c=n.readOnly,d=n.removeOnBackspace,u=c?Fe.tagRemove+" "+Fe.tagRemoveReadOnly:Fe.tagRemove;return ve.createElement("div",{className:Fe.tag},!i&&ve.createElement("div",{className:Fe.tagContent},e),i&&ve.createElement(Xr,{value:e,inputRef:s,innerEditableRef:this.innerEditableRef,className:Fe.tagContent,change:function(v){return l(o,v)},remove:this.remove,validator:a,removeOnBackspace:d}),ve.createElement("div",{className:u,onClick:this.remove}))},r}(ve.Component),Zr=function(){var t=function(r,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var i in o)o.hasOwnProperty(i)&&(e[i]=o[i])},t(r,n)};return function(r,n){t(r,n);function e(){this.constructor=r}r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}}(),ii=function(t){Zr(r,t);function r(){var n=t!==null&&t.apply(this,arguments)||this;return n.state={input:""},n.inputRef=ve.createRef(),n.onInputChange=function(e){n.setState({input:e.target.value})},n.onInputKeyDown=function(e){var o=n.state.input,i=n.props,s=i.validator,a=i.removeOnBackspace;if(e.keyCode===13){if(e.preventDefault(),o==="")return;var l=s!==void 0?s(o):!0;if(!l)return;n.addTag(o)}else if(a&&(e.keyCode===8||e.keyCode===46)){if(o!=="")return;n.removeTag(n.props.tags.length-1)}},n.addTag=function(e){var o=n.props.tags.slice();o.push(e),n.props.onChange(o),n.setState({input:""})},n.removeTag=function(e){var o=n.props.tags.slice();o.splice(e,1),n.props.onChange(o)},n.updateTag=function(e,o){var i=n.props.tags.slice();i[e]=o,n.props.onChange(i)},n}return r.prototype.render=function(){var n=this,e=this.state.input,o=this.props,i=o.tags,s=o.placeholder,a=o.maxTags,l=o.editable,c=o.readOnly,d=o.validator,u=o.removeOnBackspace,v=a!==void 0?i.length>=a:!1,f=c?!1:l||!1,g=!c&&!v;return ve.createElement("div",{className:Fe.wrapper},i.map(function(h,y){return ve.createElement(Jr,{key:y,value:h,index:y,editable:f,readOnly:c||!1,inputRef:n.inputRef,update:n.updateTag,remove:n.removeTag,validator:d,removeOnBackspace:u})}),g&&ve.createElement("input",{ref:this.inputRef,value:e,className:Fe.input,placeholder:s||"Type and press enter",onChange:this.onInputChange,onKeyDown:this.onInputKeyDown}))},r}(ve.Component),Hn={exports:{}},ut={};Object.defineProperty(ut,"__esModule",{value:!0});var Ze,Bt=Fr(),W=(Ze=Bt)&&typeof Ze=="object"&&"default"in Ze?Ze.default:Ze;function jn(t,r){return(jn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,e){return n.__proto__=e,n})(t,r)}function X(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(function(t,r){r===void 0&&(r={});var n=r.insertAt;if(typeof document<"u"){var e=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",n==="top"&&e.firstChild?e.insertBefore(o,e.firstChild):e.appendChild(o),o.styleSheet?o.styleSheet.cssText=t:o.appendChild(document.createTextNode(t))}})('.multiSelectContainer,.multiSelectContainer *,.multiSelectContainer :after,.multiSelectContainer :before{box-sizing:border-box}.multiSelectContainer{position:relative;text-align:left;width:100%}.disable_ms{opacity:.5;pointer-events:none}.display-none{display:none}.searchWrapper{border:1px solid #ccc;border-radius:4px;min-height:22px;padding:5px;position:relative}.multiSelectContainer input{background:transparent;border:none;margin-top:3px}.multiSelectContainer input:focus{outline:none}.chip{align-items:center;background:#0096fb;border-radius:11px;color:#fff;display:inline-flex;font-size:13px;line-height:19px;margin-bottom:5px;margin-right:5px;padding:4px 10px}.chip,.singleChip{white-space:nowrap}.singleChip{background:none;border-radius:none;color:inherit}.singleChip i{display:none}.closeIcon{cursor:pointer;float:right;height:13px;margin-left:5px;width:13px}.optionListContainer{background:#fff;border-radius:4px;margin-top:1px;position:absolute;width:100%;z-index:2}.multiSelectContainer ul{border:1px solid #ccc;border-radius:4px;display:block;margin:0;max-height:250px;overflow-y:auto;padding:0}.multiSelectContainer li{padding:10px}.multiSelectContainer li:hover{background:#0096fb;color:#fff;cursor:pointer}.checkbox{margin-right:10px}.disableSelection{opacity:.5;pointer-events:none}.highlightOption{background:#0096fb;color:#fff}.displayBlock{display:block}.displayNone{display:none}.notFound{display:block;padding:10px}.singleSelect{padding-right:20px}li.groupHeading{color:#908e8e;padding:5px 15px;pointer-events:none}li.groupChildEle{padding-left:30px}.icon_down_dir{position:absolute;right:10px;top:50%;transform:translateY(-50%);width:14px}.icon_down_dir:before{content:"\\e803"}.custom-close{display:flex}');var Cn={circle:"data:image/svg+xml,%3Csvg%20height%3D%22512px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%20512%20512%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%20512%20512%22%20width%3D%22512px%22%20xml%3Aspace%3D%22preserve%22%20%20%20%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20%20%20%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%20%20%20%20%3Cstyle%20type%3D%22text%2Fcss%22%3E%20%20%20%20%20%20%20%20.st0%7B%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%23fff%3B%20%20%20%20%20%20%20%20%7D%20%3C%2Fstyle%3E%20%20%20%20%3Cg%3E%20%20%20%20%20%20%20%20%3Cpath%20class%3D%22st0%22%20d%3D%22M256%2C33C132.3%2C33%2C32%2C133.3%2C32%2C257c0%2C123.7%2C100.3%2C224%2C224%2C224c123.7%2C0%2C224-100.3%2C224-224C480%2C133.3%2C379.7%2C33%2C256%2C33z%20%20%20%20M364.3%2C332.5c1.5%2C1.5%2C2.3%2C3.5%2C2.3%2C5.6c0%2C2.1-0.8%2C4.2-2.3%2C5.6l-21.6%2C21.7c-1.6%2C1.6-3.6%2C2.3-5.6%2C2.3c-2%2C0-4.1-0.8-5.6-2.3L256%2C289.8%20%20%20l-75.4%2C75.7c-1.5%2C1.6-3.6%2C2.3-5.6%2C2.3c-2%2C0-4.1-0.8-5.6-2.3l-21.6-21.7c-1.5-1.5-2.3-3.5-2.3-5.6c0-2.1%2C0.8-4.2%2C2.3-5.6l75.7-76%20%20%20l-75.9-75c-3.1-3.1-3.1-8.2%2C0-11.3l21.6-21.7c1.5-1.5%2C3.5-2.3%2C5.6-2.3c2.1%2C0%2C4.1%2C0.8%2C5.6%2C2.3l75.7%2C74.7l75.7-74.7%20%20%20c1.5-1.5%2C3.5-2.3%2C5.6-2.3c2.1%2C0%2C4.1%2C0.8%2C5.6%2C2.3l21.6%2C21.7c3.1%2C3.1%2C3.1%2C8.2%2C0%2C11.3l-75.9%2C75L364.3%2C332.5z%22%2F%3E%20%20%20%20%3C%2Fg%3E%3C%2Fsvg%3E",circle2:"data:image/svg+xml,%3Csvg%20viewBox%3D%220%200%2096%2096%22%20%20%20%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%20%20%20%20%3Cstyle%20type%3D%22text%2Fcss%22%3E%20%20%20%20%20%20%20%20.st0%7B%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%23fff%3B%20%20%20%20%20%20%20%20%7D%20%3C%2Fstyle%3E%20%20%20%20%3Cg%3E%20%20%20%20%20%20%20%20%3Cpath%20class%3D%22st0%22%20d%3D%22M48%2C0A48%2C48%2C0%2C1%2C0%2C96%2C48%2C48.0512%2C48.0512%2C0%2C0%2C0%2C48%2C0Zm0%2C84A36%2C36%2C0%2C1%2C1%2C84%2C48%2C36.0393%2C36.0393%2C0%2C0%2C1%2C48%2C84Z%22%2F%3E%20%20%20%20%20%20%20%20%3Cpath%20class%3D%22st0%22%20d%3D%22M64.2422%2C31.7578a5.9979%2C5.9979%2C0%2C0%2C0-8.4844%2C0L48%2C39.5156l-7.7578-7.7578a5.9994%2C5.9994%2C0%2C0%2C0-8.4844%2C8.4844L39.5156%2C48l-7.7578%2C7.7578a5.9994%2C5.9994%2C0%2C1%2C0%2C8.4844%2C8.4844L48%2C56.4844l7.7578%2C7.7578a5.9994%2C5.9994%2C0%2C0%2C0%2C8.4844-8.4844L56.4844%2C48l7.7578-7.7578A5.9979%2C5.9979%2C0%2C0%2C0%2C64.2422%2C31.7578Z%22%2F%3E%20%20%20%20%3C%2Fg%3E%3C%2Fsvg%3E",close:"data:image/svg+xml,%3Csvg%20height%3D%22135.467mm%22%20style%3D%22shape-rendering%3AgeometricPrecision%3B%20text-rendering%3AgeometricPrecision%3B%20image-rendering%3AoptimizeQuality%3B%20fill-rule%3Aevenodd%3B%20clip-rule%3Aevenodd%22%20viewBox%3D%220%200%2013547%2013547%22%20width%3D%22135.467mm%22%20xml%3Aspace%3D%22preserve%22%20%20%20%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20%20%20%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%20%20%20%20%3Cdefs%3E%20%20%20%20%20%20%20%20%3Cstyle%20type%3D%22text%2Fcss%22%3E%20%20%20%20%20%20%20%20%20%20%20%20.fil0%20%7Bfill%3Anone%7D%20%20%20%20%20%20%20%20%20%20%20%20.fil1%20%7Bfill%3A%23fff%7D%20%20%20%20%20%20%20%20%3C%2Fstyle%3E%20%20%20%20%3C%2Fdefs%3E%20%20%20%20%3Cg%20id%3D%22Ebene_x0020_1%22%3E%20%20%20%20%20%20%20%20%3Cpolygon%20class%3D%22fil0%22%20points%3D%220%2C0%2013547%2C0%2013547%2C13547%200%2C13547%20%22%2F%3E%20%20%20%20%20%20%20%20%3Cpath%20class%3D%22fil1%22%20d%3D%22M714%2012832l12118%200%200%20-12117%20-12118%200%200%2012117zm4188%20-2990l1871%20-1871%201871%201871%201197%20-1197%20-1871%20-1871%201871%20-1871%20-1197%20-1197%20-1871%201871%20-1871%20-1871%20-1197%201197%201871%201871%20-1871%201871%201197%201197z%22%2F%3E%20%20%20%20%3C%2Fg%3E%3C%2Fsvg%3E",cancel:"data:image/svg+xml,%3Csvg%20height%3D%22512px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%20512%20512%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%20512%20512%22%20width%3D%22512px%22%20xml%3Aspace%3D%22preserve%22%20%20%20%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20%20%20%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%20%20%20%20%3Cstyle%20type%3D%22text%2Fcss%22%3E%20%20%20%20%20%20%20%20.st0%7B%20%20%20%20%20%20%20%20%20%20%20%20fill%3A%23fff%3B%20%20%20%20%20%20%20%20%7D%20%3C%2Fstyle%3E%20%20%20%20%3Cpath%20class%3D%22st0%22%20d%3D%22M443.6%2C387.1L312.4%2C255.4l131.5-130c5.4-5.4%2C5.4-14.2%2C0-19.6l-37.4-37.6c-2.6-2.6-6.1-4-9.8-4c-3.7%2C0-7.2%2C1.5-9.8%2C4%20%20L256%2C197.8L124.9%2C68.3c-2.6-2.6-6.1-4-9.8-4c-3.7%2C0-7.2%2C1.5-9.8%2C4L68%2C105.9c-5.4%2C5.4-5.4%2C14.2%2C0%2C19.6l131.5%2C130L68.4%2C387.1%20%20c-2.6%2C2.6-4.1%2C6.1-4.1%2C9.8c0%2C3.7%2C1.4%2C7.2%2C4.1%2C9.8l37.4%2C37.6c2.7%2C2.7%2C6.2%2C4.1%2C9.8%2C4.1c3.5%2C0%2C7.1-1.3%2C9.8-4.1L256%2C313.1l130.7%2C131.1%20%20c2.7%2C2.7%2C6.2%2C4.1%2C9.8%2C4.1c3.5%2C0%2C7.1-1.3%2C9.8-4.1l37.4-37.6c2.6-2.6%2C4.1-6.1%2C4.1-9.8C447.7%2C393.2%2C446.2%2C389.7%2C443.6%2C387.1z%22%2F%3E%3C%2Fsvg%3E"};function eo(t){var r,n,e=Bt.useRef(null);return n=t.outsideClick,Bt.useEffect(function(){function o(i){r.current&&!r.current.contains(i.target)&&n()}return document.addEventListener("mousedown",o),function(){document.removeEventListener("mousedown",o)}},[r=e]),W.createElement("div",{ref:e},t.children)}var At=function(t){var r,n;function e(i){var s;return(s=t.call(this,i)||this).state={inputValue:"",options:i.options,filteredOptions:i.options,unfilteredOptions:i.options,selectedValues:Object.assign([],i.selectedValues),preSelectedValues:Object.assign([],i.selectedValues),toggleOptionsList:!1,highlightOption:i.avoidHighlightFirstOption?-1:0,showCheckbox:i.showCheckbox,keepSearchTerm:i.keepSearchTerm,groupedObject:[],closeIconType:Cn[i.closeIcon]||Cn.circle},s.optionTimeout=null,s.searchWrapper=W.createRef(),s.searchBox=W.createRef(),s.onChange=s.onChange.bind(X(s)),s.onKeyPress=s.onKeyPress.bind(X(s)),s.onFocus=s.onFocus.bind(X(s)),s.onBlur=s.onBlur.bind(X(s)),s.renderMultiselectContainer=s.renderMultiselectContainer.bind(X(s)),s.renderSelectedList=s.renderSelectedList.bind(X(s)),s.onRemoveSelectedItem=s.onRemoveSelectedItem.bind(X(s)),s.toggelOptionList=s.toggelOptionList.bind(X(s)),s.onArrowKeyNavigation=s.onArrowKeyNavigation.bind(X(s)),s.onSelectItem=s.onSelectItem.bind(X(s)),s.filterOptionsByInput=s.filterOptionsByInput.bind(X(s)),s.removeSelectedValuesFromOptions=s.removeSelectedValuesFromOptions.bind(X(s)),s.isSelectedValue=s.isSelectedValue.bind(X(s)),s.fadeOutSelection=s.fadeOutSelection.bind(X(s)),s.isDisablePreSelectedValues=s.isDisablePreSelectedValues.bind(X(s)),s.renderGroupByOptions=s.renderGroupByOptions.bind(X(s)),s.renderNormalOption=s.renderNormalOption.bind(X(s)),s.listenerCallback=s.listenerCallback.bind(X(s)),s.resetSelectedValues=s.resetSelectedValues.bind(X(s)),s.getSelectedItems=s.getSelectedItems.bind(X(s)),s.getSelectedItemsCount=s.getSelectedItemsCount.bind(X(s)),s.hideOnClickOutside=s.hideOnClickOutside.bind(X(s)),s.onCloseOptionList=s.onCloseOptionList.bind(X(s)),s.isVisible=s.isVisible.bind(X(s)),s}n=t,(r=e).prototype=Object.create(n.prototype),r.prototype.constructor=r,jn(r,n);var o=e.prototype;return o.initialSetValue=function(){var i=this.props,s=i.groupBy,a=this.state.options;i.showCheckbox||i.singleSelect||this.removeSelectedValuesFromOptions(!1),s&&this.groupByOptions(a)},o.resetSelectedValues=function(){var i=this,s=this.state.unfilteredOptions;return new Promise(function(a){i.setState({selectedValues:[],preSelectedValues:[],options:s,filteredOptions:s},function(){a(),i.initialSetValue()})})},o.getSelectedItems=function(){return this.state.selectedValues},o.getSelectedItemsCount=function(){return this.state.selectedValues.length},o.componentDidMount=function(){this.initialSetValue(),this.searchWrapper.current.addEventListener("click",this.listenerCallback)},o.componentDidUpdate=function(i){var s=this.props,a=s.options,l=s.selectedValues,c=i.selectedValues;JSON.stringify(i.options)!==JSON.stringify(a)&&this.setState({options:a,filteredOptions:a,unfilteredOptions:a},this.initialSetValue),JSON.stringify(c)!==JSON.stringify(l)&&this.setState({selectedValues:Object.assign([],l),preSelectedValues:Object.assign([],l)},this.initialSetValue)},o.listenerCallback=function(){this.searchBox.current.focus()},o.componentWillUnmount=function(){this.optionTimeout&&clearTimeout(this.optionTimeout),this.searchWrapper.current.removeEventListener("click",this.listenerCallback)},o.removeSelectedValuesFromOptions=function(i){var s=this.props,a=s.isObject,l=s.displayValue,c=s.groupBy,d=this.state,u=d.selectedValues,v=u===void 0?[]:u,f=d.unfilteredOptions;if(!i&&c&&this.groupByOptions(d.options),v.length||i){if(a){var g=f.filter(function(y){return v.findIndex(function(m){return m[l]===y[l]})===-1});return c&&this.groupByOptions(g),void this.setState({options:g,filteredOptions:g},this.filterOptionsByInput)}var h=f.filter(function(y){return v.indexOf(y)===-1});this.setState({options:h,filteredOptions:h},this.filterOptionsByInput)}},o.groupByOptions=function(i){var s=this.props.groupBy,a=i.reduce(function(l,c){var d=c[s]||"Others";return l[d]=l[d]||[],l[d].push(c),l},Object.create({}));this.setState({groupedObject:a})},o.onChange=function(i){var s=this.props.onSearch;this.setState({inputValue:i.target.value},this.filterOptionsByInput),s&&s(i.target.value)},o.onKeyPress=function(i){var s=this.props.onKeyPressFn;s&&s(i,i.target.value)},o.filterOptionsByInput=function(){var i,s=this,a=this.state,l=a.inputValue,c=this.props,d=c.displayValue;i=a.filteredOptions.filter(c.isObject?function(u){return s.matchValues(u[d],l)}:function(u){return s.matchValues(u,l)}),this.groupByOptions(i),this.setState({options:i})},o.matchValues=function(i,s){return this.props.caseSensitiveSearch?i.indexOf(s)>-1:i.toLowerCase?i.toLowerCase().indexOf(s.toLowerCase())>-1:i.toString().indexOf(s)>-1},o.onArrowKeyNavigation=function(i){var s=this.state,a=s.options,l=s.highlightOption,c=s.toggleOptionsList,d=s.selectedValues;if(i.keyCode!==8||s.inputValue||this.props.disablePreSelectedValues||!d.length||this.onRemoveSelectedItem(d.length-1),a.length){if(i.keyCode===38)this.setState(l>0?function(u){return{highlightOption:u.highlightOption-1}}:{highlightOption:a.length-1});else if(i.keyCode===40)this.setState(l<a.length-1?function(u){return{highlightOption:u.highlightOption+1}}:{highlightOption:0});else if(i.key==="Enter"&&a.length&&c){if(l===-1)return;this.onSelectItem(a[l])}}},o.onRemoveSelectedItem=function(i){var s,a=this,l=this.state.selectedValues,c=this.props,d=c.onRemove,u=c.showCheckbox,v=c.displayValue;s=c.isObject?l.findIndex(function(f){return f[v]===i[v]}):l.indexOf(i),l.splice(s,1),d(l,i),this.setState({selectedValues:l},function(){u||a.removeSelectedValuesFromOptions(!0)}),this.props.closeOnSelect||this.searchBox.current.focus()},o.onSelectItem=function(i){var s=this,a=this.state.selectedValues,l=this.props,c=l.selectionLimit,d=l.onSelect,u=l.singleSelect,v=l.showCheckbox;if(this.state.keepSearchTerm||this.setState({inputValue:""}),u)return this.onSingleSelect(i),void d([i],i);this.isSelectedValue(i)?this.onRemoveSelectedItem(i):c!=a.length&&(a.push(i),d(a,i),this.setState({selectedValues:a},function(){v?s.filterOptionsByInput():s.removeSelectedValuesFromOptions(!0)}),this.props.closeOnSelect||this.searchBox.current.focus())},o.onSingleSelect=function(i){this.setState({selectedValues:[i],toggleOptionsList:!1})},o.isSelectedValue=function(i){var s=this.props,a=s.displayValue,l=this.state.selectedValues;return s.isObject?l.filter(function(c){return c[a]===i[a]}).length>0:l.filter(function(c){return c===i}).length>0},o.renderOptionList=function(){var i=this.props,s=i.groupBy,a=i.style,l=i.emptyRecordMsg,c=i.loadingMessage,d=c===void 0?"loading...":c,u=this.state.options;return i.loading?W.createElement("ul",{className:"optionContainer",style:a.optionContainer},typeof d=="string"&&W.createElement("span",{style:a.loadingMessage,className:"notFound"},d),typeof d!="string"&&d):W.createElement("ul",{className:"optionContainer",style:a.optionContainer},u.length===0&&W.createElement("span",{style:a.notFound,className:"notFound"},l),s?this.renderGroupByOptions():this.renderNormalOption())},o.renderGroupByOptions=function(){var i=this,s=this.props,a=s.isObject,l=a!==void 0&&a,c=s.displayValue,d=s.showCheckbox,u=s.style,v=s.singleSelect,f=this.state.groupedObject;return Object.keys(f).map(function(g){return W.createElement(W.Fragment,{key:g},W.createElement("li",{className:"groupHeading",style:u.groupHeading},g),f[g].map(function(h,y){var m=i.isSelectedValue(h);return W.createElement("li",{key:"option"+y,style:u.option,className:"groupChildEle option "+(m?"selected":"")+" "+(i.fadeOutSelection(h)?"disableSelection":"")+" "+(i.isDisablePreSelectedValues(h)?"disableSelection":""),onClick:function(){return i.onSelectItem(h)}},d&&!v&&W.createElement("input",{type:"checkbox",className:"checkbox",readOnly:!0,checked:m}),i.props.optionValueDecorator(l?h[c]:(h||"").toString(),h))}))})},o.renderNormalOption=function(){var i=this,s=this.props,a=s.isObject,l=a!==void 0&&a,c=s.displayValue,d=s.showCheckbox,u=s.style,v=s.singleSelect,f=this.state.highlightOption;return this.state.options.map(function(g,h){var y=i.isSelectedValue(g);return W.createElement("li",{key:"option"+h,style:u.option,className:"option "+(y?"selected":"")+" "+(f===h?"highlightOption highlight":"")+" "+(i.fadeOutSelection(g)?"disableSelection":"")+" "+(i.isDisablePreSelectedValues(g)?"disableSelection":""),onClick:function(){return i.onSelectItem(g)}},d&&!v&&W.createElement("input",{type:"checkbox",readOnly:!0,className:"checkbox",checked:y}),i.props.optionValueDecorator(l?g[c]:(g||"").toString(),g))})},o.renderSelectedList=function(){var i=this,s=this.props,a=s.isObject,l=a!==void 0&&a,c=s.displayValue,d=s.style,u=s.singleSelect,v=s.customCloseIcon,f=this.state,g=f.closeIconType;return f.selectedValues.map(function(h,y){return W.createElement("span",{className:"chip  "+(u&&"singleChip")+" "+(i.isDisablePreSelectedValues(h)&&"disableSelection"),key:y,style:d.chips},i.props.selectedValueDecorator(l?h[c]:(h||"").toString(),h),!i.isDisablePreSelectedValues(h)&&(v?W.createElement("i",{className:"custom-close",onClick:function(){return i.onRemoveSelectedItem(h)}},v):W.createElement("img",{className:"icon_cancel closeIcon",src:g,onClick:function(){return i.onRemoveSelectedItem(h)}})))})},o.isDisablePreSelectedValues=function(i){var s=this.props,a=s.displayValue,l=this.state.preSelectedValues;return!(!s.disablePreSelectedValues||!l.length)&&(s.isObject?l.filter(function(c){return c[a]===i[a]}).length>0:l.filter(function(c){return c===i}).length>0)},o.fadeOutSelection=function(i){var s=this.props,a=s.selectionLimit;if(!s.singleSelect){var l=this.state.selectedValues;return a!=-1&&a==l.length&&(a==l.length?!s.showCheckbox||!this.isSelectedValue(i):void 0)}},o.toggelOptionList=function(){this.setState({toggleOptionsList:!this.state.toggleOptionsList,highlightOption:this.props.avoidHighlightFirstOption?-1:0})},o.onCloseOptionList=function(){this.setState({toggleOptionsList:!1,highlightOption:this.props.avoidHighlightFirstOption?-1:0,inputValue:""})},o.onFocus=function(){this.state.toggleOptionsList?clearTimeout(this.optionTimeout):this.toggelOptionList()},o.onBlur=function(){this.setState({inputValue:""},this.filterOptionsByInput),this.optionTimeout=setTimeout(this.onCloseOptionList,250)},o.isVisible=function(i){return!!i&&!!(i.offsetWidth||i.offsetHeight||i.getClientRects().length)},o.hideOnClickOutside=function(){var i=this,s=document.getElementsByClassName("multiselect-container")[0];document.addEventListener("click",function(a){s&&!s.contains(a.target)&&i.isVisible(s)&&i.toggelOptionList()})},o.renderMultiselectContainer=function(){var i=this.state,s=i.inputValue,a=i.toggleOptionsList,l=i.selectedValues,c=this.props,d=c.placeholder,u=c.style,v=c.singleSelect,f=c.id,g=c.name,h=c.hidePlaceholder,y=c.disable,m=c.showArrow,C=c.customArrow;return W.createElement("div",{className:"multiselect-container multiSelectContainer "+(y?"disable_ms":"")+" "+(c.className||""),id:f||"multiselectContainerReact",style:u.multiselectContainer},W.createElement("div",{className:"search-wrapper searchWrapper "+(v?"singleSelect":""),ref:this.searchWrapper,style:u.searchBox,onClick:v?this.toggelOptionList:function(){}},!c.hideSelectedList&&this.renderSelectedList(),W.createElement("input",{type:"text",ref:this.searchBox,className:"searchBox "+(v&&l.length?"display-none":""),id:(f||"search")+"_input",name:(g||"search_name")+"_input",onChange:this.onChange,onKeyPress:this.onKeyPress,value:s,onFocus:this.onFocus,onBlur:this.onBlur,placeholder:v&&l.length||h&&l.length?"":d,onKeyDown:this.onArrowKeyNavigation,style:u.inputField,autoComplete:"off",disabled:v||y}),(v||m)&&W.createElement(W.Fragment,null,C?W.createElement("span",{className:"icon_down_dir"},C):W.createElement("img",{src:"data:image/svg+xml,%3Csvg%20height%3D%2232%22%20viewBox%3D%220%200%2032%2032%22%20width%3D%2232%22%20%20%20%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%20%20%20%20%3Cg%20id%3D%22background%22%3E%20%20%20%20%20%20%20%20%3Crect%20fill%3D%22none%22%20height%3D%2232%22%20width%3D%2232%22%2F%3E%20%20%20%20%3C%2Fg%3E%20%20%20%20%3Cg%20id%3D%22arrow_x5F_down%22%3E%20%20%20%20%20%20%20%20%3Cpolygon%20points%3D%222.002%2C10%2016.001%2C24%2030.002%2C10%20%20%22%2F%3E%20%20%20%20%3C%2Fg%3E%3C%2Fsvg%3E",className:"icon_cancel icon_down_dir"}))),W.createElement("div",{className:"optionListContainer "+(a?"displayBlock":"displayNone"),onMouseDown:function(S){S.preventDefault()}},this.renderOptionList()))},o.render=function(){return W.createElement(eo,{outsideClick:this.onCloseOptionList},this.renderMultiselectContainer())},e}(W.Component);At.defaultProps={options:[],disablePreSelectedValues:!1,selectedValues:[],isObject:!0,displayValue:"model",showCheckbox:!1,selectionLimit:-1,placeholder:"Select",groupBy:"",style:{},emptyRecordMsg:"No Options Available",onSelect:function(){},onRemove:function(){},onKeyPressFn:function(){},closeIcon:"circle2",singleSelect:!1,caseSensitiveSearch:!1,id:"",name:"",closeOnSelect:!0,avoidHighlightFirstOption:!1,hidePlaceholder:!1,showArrow:!1,keepSearchTerm:!1,customCloseIcon:"",className:"",customArrow:void 0,selectedValueDecorator:function(t){return t},optionValueDecorator:function(t){return t}},ut.Multiselect=At,ut.default=At;Hn.exports=ut;var to=Hn.exports;const no=Vr(to);var ro=`accept acceptCharset accessKey action allowFullScreen allowTransparency
    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge
    charSet checked classID className colSpan cols content contentEditable contextMenu
    controls coords crossOrigin data dateTime default defer dir disabled download draggable
    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder
    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity
    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media
    mediaGroup method min minLength multiple muted name noValidate nonce open
    optimum pattern placeholder poster preload radioGroup readOnly rel required
    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected
    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style
    summary tabIndex target title type useMap value width wmode wrap`,oo=`onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown
    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick
    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown
    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel
    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough
    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata
    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError`,ao="".concat(ro," ").concat(oo).split(/[\s\n]+/),io="aria-",so="data-";function Sn(t,r){return t.indexOf(r)===0}function Wn(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n;r===!1?n={aria:!0,data:!0,attr:!0}:r===!0?n={aria:!0}:n=w({},r);var e={};return Object.keys(t).forEach(function(o){(n.aria&&(o==="role"||Sn(o,io))||n.data&&Sn(o,so)||n.attr&&ao.includes(o))&&(e[o]=t[o])}),e}var zt={},lo=function(r){};function co(t,r){}function uo(t,r){}function fo(){zt={}}function Un(t,r,n){!r&&!zt[n]&&(t(!1,n),zt[n]=!0)}function Re(t,r){Un(co,t,r)}function vo(t,r){Un(uo,t,r)}Re.preMessage=lo;Re.resetWarned=fo;Re.noteOnce=vo;var Xt=p.createContext(null);function po(t){var r=t.dropPosition,n=t.dropLevelOffset,e=t.indent,o={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:o.top=0,o.left=-n*e;break;case 1:o.bottom=0,o.left=-n*e;break;case 0:o.bottom=0,o.left=e;break}return p.createElement("div",{style:o})}function Gn(t){if(t==null)throw new TypeError("Cannot destructure "+t)}var En=gt()?p.useLayoutEffect:p.useEffect,Te=function(r,n){var e=p.useRef(!0);En(function(){return r(e.current)},n),En(function(){return e.current=!1,function(){e.current=!0}},[])},Yn={exports:{}},H={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qt=Symbol.for("react.element"),Jt=Symbol.for("react.portal"),mt=Symbol.for("react.fragment"),yt=Symbol.for("react.strict_mode"),Ct=Symbol.for("react.profiler"),St=Symbol.for("react.provider"),Et=Symbol.for("react.context"),ho=Symbol.for("react.server_context"),bt=Symbol.for("react.forward_ref"),xt=Symbol.for("react.suspense"),kt=Symbol.for("react.suspense_list"),Ot=Symbol.for("react.memo"),Dt=Symbol.for("react.lazy"),go=Symbol.for("react.offscreen"),qn;qn=Symbol.for("react.module.reference");function xe(t){if(typeof t=="object"&&t!==null){var r=t.$$typeof;switch(r){case Qt:switch(t=t.type,t){case mt:case Ct:case yt:case xt:case kt:return t;default:switch(t=t&&t.$$typeof,t){case ho:case Et:case bt:case Dt:case Ot:case St:return t;default:return r}}case Jt:return r}}}H.ContextConsumer=Et;H.ContextProvider=St;H.Element=Qt;H.ForwardRef=bt;H.Fragment=mt;H.Lazy=Dt;H.Memo=Ot;H.Portal=Jt;H.Profiler=Ct;H.StrictMode=yt;H.Suspense=xt;H.SuspenseList=kt;H.isAsyncMode=function(){return!1};H.isConcurrentMode=function(){return!1};H.isContextConsumer=function(t){return xe(t)===Et};H.isContextProvider=function(t){return xe(t)===St};H.isElement=function(t){return typeof t=="object"&&t!==null&&t.$$typeof===Qt};H.isForwardRef=function(t){return xe(t)===bt};H.isFragment=function(t){return xe(t)===mt};H.isLazy=function(t){return xe(t)===Dt};H.isMemo=function(t){return xe(t)===Ot};H.isPortal=function(t){return xe(t)===Jt};H.isProfiler=function(t){return xe(t)===Ct};H.isStrictMode=function(t){return xe(t)===yt};H.isSuspense=function(t){return xe(t)===xt};H.isSuspenseList=function(t){return xe(t)===kt};H.isValidElementType=function(t){return typeof t=="string"||typeof t=="function"||t===mt||t===Ct||t===yt||t===xt||t===kt||t===go||typeof t=="object"&&t!==null&&(t.$$typeof===Dt||t.$$typeof===Ot||t.$$typeof===St||t.$$typeof===Et||t.$$typeof===bt||t.$$typeof===qn||t.getModuleId!==void 0)};H.typeOf=xe;Yn.exports=H;var Xn=Yn.exports;function ft(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=[];return ve.Children.forEach(t,function(e){e==null&&!r.keepEmpty||(Array.isArray(e)?n=n.concat(ft(e)):Xn.isFragment(e)&&e.props?n=n.concat(ft(e.props.children,r)):n.push(e))}),n}function mo(t){return t instanceof HTMLElement||t instanceof SVGElement}function et(t){return mo(t)?t:t instanceof ve.Component?$r.findDOMNode(t):null}function yo(t,r,n){var e=p.useRef({});return(!("value"in e.current)||n(e.current.condition,r))&&(e.current.value=t(),e.current.condition=r),e.current.value}function Qn(t,r){typeof t=="function"?t(r):we(t)==="object"&&t&&"current"in t&&(t.current=r)}function Co(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var e=r.filter(function(o){return o});return e.length<=1?e[0]:function(o){r.forEach(function(i){Qn(i,o)})}}function So(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return yo(function(){return Co.apply(void 0,r)},r,function(e,o){return e.length!==o.length||e.every(function(i,s){return i!==o[s]})})}function Jn(t){var r,n,e=Xn.isMemo(t)?t.type.type:t.type;return!(typeof e=="function"&&!((r=e.prototype)!==null&&r!==void 0&&r.render)||typeof t=="function"&&!((n=t.prototype)!==null&&n!==void 0&&n.render))}var Ht=p.createContext(null);function Eo(t){var r=t.children,n=t.onBatchResize,e=p.useRef(0),o=p.useRef([]),i=p.useContext(Ht),s=p.useCallback(function(a,l,c){e.current+=1;var d=e.current;o.current.push({size:a,element:l,data:c}),Promise.resolve().then(function(){d===e.current&&(n==null||n(o.current),o.current=[])}),i==null||i(a,l,c)},[n,i]);return p.createElement(Ht.Provider,{value:s},r)}var Zn=function(){if(typeof Map<"u")return Map;function t(r,n){var e=-1;return r.some(function(o,i){return o[0]===n?(e=i,!0):!1}),e}return function(){function r(){this.__entries__=[]}return Object.defineProperty(r.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),r.prototype.get=function(n){var e=t(this.__entries__,n),o=this.__entries__[e];return o&&o[1]},r.prototype.set=function(n,e){var o=t(this.__entries__,n);~o?this.__entries__[o][1]=e:this.__entries__.push([n,e])},r.prototype.delete=function(n){var e=this.__entries__,o=t(e,n);~o&&e.splice(o,1)},r.prototype.has=function(n){return!!~t(this.__entries__,n)},r.prototype.clear=function(){this.__entries__.splice(0)},r.prototype.forEach=function(n,e){e===void 0&&(e=null);for(var o=0,i=this.__entries__;o<i.length;o++){var s=i[o];n.call(e,s[1],s[0])}},r}()}(),jt=typeof window<"u"&&typeof document<"u"&&window.document===document,vt=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),bo=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(vt):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)}}(),xo=2;function ko(t,r){var n=!1,e=!1,o=0;function i(){n&&(n=!1,t()),e&&a()}function s(){bo(i)}function a(){var l=Date.now();if(n){if(l-o<xo)return;e=!0}else n=!0,e=!1,setTimeout(s,r);o=l}return a}var Oo=20,Do=["top","right","bottom","left","width","height","size","weight"],Ro=typeof MutationObserver<"u",wo=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=ko(this.refresh.bind(this),Oo)}return t.prototype.addObserver=function(r){~this.observers_.indexOf(r)||this.observers_.push(r),this.connected_||this.connect_()},t.prototype.removeObserver=function(r){var n=this.observers_,e=n.indexOf(r);~e&&n.splice(e,1),!n.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){var r=this.updateObservers_();r&&this.refresh()},t.prototype.updateObservers_=function(){var r=this.observers_.filter(function(n){return n.gatherActive(),n.hasActive()});return r.forEach(function(n){return n.broadcastActive()}),r.length>0},t.prototype.connect_=function(){!jt||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Ro?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){!jt||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(r){var n=r.propertyName,e=n===void 0?"":n,o=Do.some(function(i){return!!~e.indexOf(i)});o&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),er=function(t,r){for(var n=0,e=Object.keys(r);n<e.length;n++){var o=e[n];Object.defineProperty(t,o,{value:r[o],enumerable:!1,writable:!1,configurable:!0})}return t},Ye=function(t){var r=t&&t.ownerDocument&&t.ownerDocument.defaultView;return r||vt},tr=Rt(0,0,0,0);function pt(t){return parseFloat(t)||0}function bn(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return r.reduce(function(e,o){var i=t["border-"+o+"-width"];return e+pt(i)},0)}function No(t){for(var r=["top","right","bottom","left"],n={},e=0,o=r;e<o.length;e++){var i=o[e],s=t["padding-"+i];n[i]=pt(s)}return n}function Ko(t){var r=t.getBBox();return Rt(0,0,r.width,r.height)}function _o(t){var r=t.clientWidth,n=t.clientHeight;if(!r&&!n)return tr;var e=Ye(t).getComputedStyle(t),o=No(e),i=o.left+o.right,s=o.top+o.bottom,a=pt(e.width),l=pt(e.height);if(e.boxSizing==="border-box"&&(Math.round(a+i)!==r&&(a-=bn(e,"left","right")+i),Math.round(l+s)!==n&&(l-=bn(e,"top","bottom")+s)),!To(t)){var c=Math.round(a+i)-r,d=Math.round(l+s)-n;Math.abs(c)!==1&&(a-=c),Math.abs(d)!==1&&(l-=d)}return Rt(o.left,o.top,a,l)}var Mo=function(){return typeof SVGGraphicsElement<"u"?function(t){return t instanceof Ye(t).SVGGraphicsElement}:function(t){return t instanceof Ye(t).SVGElement&&typeof t.getBBox=="function"}}();function To(t){return t===Ye(t).document.documentElement}function Lo(t){return jt?Mo(t)?Ko(t):_o(t):tr}function Po(t){var r=t.x,n=t.y,e=t.width,o=t.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,s=Object.create(i.prototype);return er(s,{x:r,y:n,width:e,height:o,top:n,right:r+e,bottom:o+n,left:r}),s}function Rt(t,r,n,e){return{x:t,y:r,width:n,height:e}}var Ao=function(){function t(r){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Rt(0,0,0,0),this.target=r}return t.prototype.isActive=function(){var r=Lo(this.target);return this.contentRect_=r,r.width!==this.broadcastWidth||r.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var r=this.contentRect_;return this.broadcastWidth=r.width,this.broadcastHeight=r.height,r},t}(),Io=function(){function t(r,n){var e=Po(n);er(this,{target:r,contentRect:e})}return t}(),Fo=function(){function t(r,n,e){if(this.activeObservations_=[],this.observations_=new Zn,typeof r!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=r,this.controller_=n,this.callbackCtx_=e}return t.prototype.observe=function(r){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(r instanceof Ye(r).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(r)||(n.set(r,new Ao(r)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(r){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(r instanceof Ye(r).Element))throw new TypeError('parameter 1 is not of type "Element".');var n=this.observations_;n.has(r)&&(n.delete(r),n.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var r=this;this.clearActive(),this.observations_.forEach(function(n){n.isActive()&&r.activeObservations_.push(n)})},t.prototype.broadcastActive=function(){if(this.hasActive()){var r=this.callbackCtx_,n=this.activeObservations_.map(function(e){return new Io(e.target,e.broadcastRect())});this.callback_.call(r,n,r),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),nr=typeof WeakMap<"u"?new WeakMap:new Zn,rr=function(){function t(r){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=wo.getInstance(),e=new Fo(r,n,this);nr.set(this,e)}return t}();["observe","unobserve","disconnect"].forEach(function(t){rr.prototype[t]=function(){var r;return(r=nr.get(this))[t].apply(r,arguments)}});var Vo=function(){return typeof vt.ResizeObserver<"u"?vt.ResizeObserver:rr}(),Ve=new Map;function $o(t){t.forEach(function(r){var n,e=r.target;(n=Ve.get(e))===null||n===void 0||n.forEach(function(o){return o(e)})})}var or=new Vo($o);function Bo(t,r){Ve.has(t)||(Ve.set(t,new Set),or.observe(t)),Ve.get(t).add(r)}function zo(t,r){Ve.has(t)&&(Ve.get(t).delete(r),Ve.get(t).size||(or.unobserve(t),Ve.delete(t)))}var Ho=function(t){rt(n,t);var r=ot(n);function n(){return qe(this,n),r.apply(this,arguments)}return Xe(n,[{key:"render",value:function(){return this.props.children}}]),n}(p.Component);function jo(t,r){var n=t.children,e=t.disabled,o=p.useRef(null),i=p.useRef(null),s=p.useContext(Ht),a=typeof n=="function",l=a?n(o):n,c=p.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),d=!a&&p.isValidElement(l)&&Jn(l),u=d?l.ref:null,v=So(u,o),f=function(){var m;return et(o.current)||(o.current&&we(o.current)==="object"?et((m=o.current)===null||m===void 0?void 0:m.nativeElement):null)||et(i.current)};p.useImperativeHandle(r,function(){return f()});var g=p.useRef(t);g.current=t;var h=p.useCallback(function(y){var m=g.current,C=m.onResize,S=m.data,E=y.getBoundingClientRect(),b=E.width,k=E.height,x=y.offsetWidth,R=y.offsetHeight,O=Math.floor(b),K=Math.floor(k);if(c.current.width!==O||c.current.height!==K||c.current.offsetWidth!==x||c.current.offsetHeight!==R){var N={width:O,height:K,offsetWidth:x,offsetHeight:R};c.current=N;var _=x===Math.round(b)?b:x,D=R===Math.round(k)?k:R,P=w(w({},N),{},{offsetWidth:_,offsetHeight:D});s==null||s(P,y,S),C&&Promise.resolve().then(function(){C(P,y)})}},[]);return p.useEffect(function(){var y=f();return y&&!e&&Bo(y,h),function(){return zo(y,h)}},[o.current,e]),p.createElement(Ho,{ref:i},d?p.cloneElement(l,{ref:v}):l)}var Wo=p.forwardRef(jo),Uo="rc-observer-key";function Go(t,r){var n=t.children,e=typeof n=="function"?[n]:ft(n);return e.map(function(o,i){var s=(o==null?void 0:o.key)||"".concat(Uo,"-").concat(i);return p.createElement(Wo,ge({},t,{key:s,ref:i===0?r:void 0}),o)})}var Zt=p.forwardRef(Go);Zt.Collection=Eo;var ar=p.forwardRef(function(t,r){var n=t.height,e=t.offsetY,o=t.offsetX,i=t.children,s=t.prefixCls,a=t.onInnerResize,l=t.innerProps,c=t.rtl,d=t.extra,u={},v={display:"flex",flexDirection:"column"};if(e!==void 0){var f;u={height:n,position:"relative",overflow:"hidden"},v=w(w({},v),{},(f={transform:"translateY(".concat(e,"px)")},M(f,c?"marginRight":"marginLeft",-o),M(f,"position","absolute"),M(f,"left",0),M(f,"right",0),M(f,"top",0),f))}return p.createElement("div",{style:u},p.createElement(Zt,{onResize:function(h){var y=h.offsetHeight;y&&a&&a()}},p.createElement("div",ge({style:v,className:fe(M({},"".concat(s,"-holder-inner"),s)),ref:r},l),i,d)))});ar.displayName="Filler";function xn(t,r){var n="touches"in t?t.touches[0]:t;return n[r?"pageX":"pageY"]}var kn=p.forwardRef(function(t,r){var n,e=t.prefixCls,o=t.rtl,i=t.scrollOffset,s=t.scrollRange,a=t.onStartMove,l=t.onStopMove,c=t.onScroll,d=t.horizontal,u=t.spinSize,v=t.containerSize,f=t.style,g=t.thumbStyle,h=p.useState(!1),y=V(h,2),m=y[0],C=y[1],S=p.useState(null),E=V(S,2),b=E[0],k=E[1],x=p.useState(null),R=V(x,2),O=R[0],K=R[1],N=!o,_=p.useRef(),D=p.useRef(),P=p.useState(!1),I=V(P,2),F=I[0],j=I[1],z=p.useRef(),te=function(){clearTimeout(z.current),j(!0),z.current=setTimeout(function(){j(!1)},3e3)},ne=s-v||0,Q=v-u||0,ee=ne>0,J=p.useMemo(function(){if(i===0||ne===0)return 0;var re=i/ne;return re*Q},[i,ne,Q]),Se=function(Z){Z.stopPropagation(),Z.preventDefault()},pe=p.useRef({top:J,dragging:m,pageY:b,startTop:O});pe.current={top:J,dragging:m,pageY:b,startTop:O};var Ee=function(Z){C(!0),k(xn(Z,d)),K(pe.current.top),a(),Z.stopPropagation(),Z.preventDefault()};p.useEffect(function(){var re=function(Ne){Ne.preventDefault()},Z=_.current,me=D.current;return Z.addEventListener("touchstart",re),me.addEventListener("touchstart",Ee),function(){Z.removeEventListener("touchstart",re),me.removeEventListener("touchstart",Ee)}},[]);var he=p.useRef();he.current=ne;var ke=p.useRef();ke.current=Q,p.useEffect(function(){if(m){var re,Z=function(Ne){var ye=pe.current,_e=ye.dragging,q=ye.pageY,A=ye.startTop;if(be.cancel(re),_e){var $=xn(Ne,d)-q,U=A;!N&&d?U-=$:U+=$;var ae=he.current,ue=ke.current,ce=ue?U/ue:0,de=Math.ceil(ce*ae);de=Math.max(de,0),de=Math.min(de,ae),re=be(function(){c(de,d)})}},me=function(){C(!1),l()};return window.addEventListener("mousemove",Z),window.addEventListener("touchmove",Z),window.addEventListener("mouseup",me),window.addEventListener("touchend",me),function(){window.removeEventListener("mousemove",Z),window.removeEventListener("touchmove",Z),window.removeEventListener("mouseup",me),window.removeEventListener("touchend",me),be.cancel(re)}}},[m]),p.useEffect(function(){te()},[i]),p.useImperativeHandle(r,function(){return{delayHidden:te}});var se="".concat(e,"-scrollbar"),Y={position:"absolute",visibility:F&&ee?null:"hidden"},G={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return d?(Y.height=8,Y.left=0,Y.right=0,Y.bottom=0,G.height="100%",G.width=u,N?G.left=J:G.right=J):(Y.width=8,Y.top=0,Y.bottom=0,N?Y.right=0:Y.left=0,G.width="100%",G.height=u,G.top=J),p.createElement("div",{ref:_,className:fe(se,(n={},M(n,"".concat(se,"-horizontal"),d),M(n,"".concat(se,"-vertical"),!d),M(n,"".concat(se,"-visible"),F),n)),style:w(w({},Y),f),onMouseDown:Se,onMouseMove:te},p.createElement("div",{ref:D,className:fe("".concat(se,"-thumb"),M({},"".concat(se,"-thumb-moving"),m)),style:w(w({},G),g),onMouseDown:Ee}))});function Yo(t){var r=t.children,n=t.setRef,e=p.useCallback(function(o){n(o)},[]);return p.cloneElement(r,{ref:e})}function qo(t,r,n,e,o,i,s){var a=s.getKey;return t.slice(r,n+1).map(function(l,c){var d=r+c,u=i(l,d,{style:{width:e}}),v=a(l);return p.createElement(Yo,{key:v,setRef:function(g){return o(l,g)}},u)})}var Xo=function(){function t(){qe(this,t),this.maps=void 0,this.id=0,this.maps=Object.create(null)}return Xe(t,[{key:"set",value:function(n,e){this.maps[n]=e,this.id+=1}},{key:"get",value:function(n){return this.maps[n]}}]),t}();function Qo(t,r,n){var e=p.useState(0),o=V(e,2),i=o[0],s=o[1],a=p.useRef(new Map),l=p.useRef(new Xo),c=p.useRef();function d(){be.cancel(c.current)}function u(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;d();var g=function(){a.current.forEach(function(y,m){if(y&&y.offsetParent){var C=et(y),S=C.offsetHeight;l.current.get(m)!==S&&l.current.set(m,C.offsetHeight)}}),s(function(y){return y+1})};f?g():c.current=be(g)}function v(f,g){var h=t(f);a.current.get(h),g?(a.current.set(h,g),u()):a.current.delete(h)}return p.useEffect(function(){return d},[]),[v,u,l.current,i]}function On(t){var r=p.useRef();r.current=t;var n=p.useCallback(function(){for(var e,o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return(e=r.current)===null||e===void 0?void 0:e.call.apply(e,[r].concat(i))},[]);return n}function dt(t){var r=p.useRef(!1),n=p.useState(t),e=V(n,2),o=e[0],i=e[1];p.useEffect(function(){return r.current=!1,function(){r.current=!0}},[]);function s(a,l){l&&r.current||i(a)}return[o,s]}var Jo=10;function Zo(t,r,n,e,o,i,s,a){var l=p.useRef(),c=p.useState(null),d=V(c,2),u=d[0],v=d[1];return Te(function(){if(u&&u.times<Jo){if(!t.current){v(function(te){return w({},te)});return}i();var f=u.targetAlign,g=u.originAlign,h=u.index,y=u.offset,m=t.current.clientHeight,C=!1,S=f,E=null;if(m){for(var b=f||g,k=0,x=0,R=0,O=Math.min(r.length-1,h),K=0;K<=O;K+=1){var N=o(r[K]);x=k;var _=n.get(N);R=x+(_===void 0?e:_),k=R}for(var D=b==="top"?y:m-y,P=O;P>=0;P-=1){var I=o(r[P]),F=n.get(I);if(F===void 0){C=!0;break}if(D-=F,D<=0)break}switch(b){case"top":E=x-y;break;case"bottom":E=R-m+y;break;default:{var j=t.current.scrollTop,z=j+m;x<j?S="top":R>z&&(S="bottom")}}E!==null&&s(E),E!==u.lastTop&&(C=!0)}C&&v(function(te){return w(w({},te),{},{times:te.times+1,targetAlign:S,lastTop:E})})}},[u,t.current]),function(f){if(f==null){a();return}if(be.cancel(l.current),typeof f=="number")s(f);else if(f&&we(f)==="object"){var g,h=f.align;"index"in f?g=f.index:g=r.findIndex(function(C){return o(C)===f.key});var y=f.offset,m=y===void 0?0:y;v({times:0,index:g,offset:m,originAlign:h})}}}function ea(t,r,n){var e=t.length,o=r.length,i,s;if(e===0&&o===0)return null;e<o?(i=t,s=r):(i=r,s=t);var a={__EMPTY_ITEM__:!0};function l(g){return g!==void 0?n(g):a}for(var c=null,d=Math.abs(e-o)!==1,u=0;u<s.length;u+=1){var v=l(i[u]),f=l(s[u]);if(v!==f){c=u,d=d||v!==l(s[u+1]);break}}return c===null?null:{index:c,multiple:d}}function ta(t,r,n){var e=p.useState(t),o=V(e,2),i=o[0],s=o[1],a=p.useState(null),l=V(a,2),c=l[0],d=l[1];return p.useEffect(function(){var u=ea(i||[],t||[],r);(u==null?void 0:u.index)!==void 0&&d(t[u.index]),s(t)},[t]),[c]}var Dn=(typeof navigator>"u"?"undefined":we(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const ir=function(t,r){var n=p.useRef(!1),e=p.useRef(null);function o(){clearTimeout(e.current),n.current=!0,e.current=setTimeout(function(){n.current=!1},50)}var i=p.useRef({top:t,bottom:r});return i.current.top=t,i.current.bottom=r,function(s){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=s<0&&i.current.top||s>0&&i.current.bottom;return a&&l?(clearTimeout(e.current),n.current=!1):(!l||n.current)&&o(),!n.current&&l}};function na(t,r,n,e,o){var i=p.useRef(0),s=p.useRef(null),a=p.useRef(null),l=p.useRef(!1),c=ir(r,n);function d(y,m){be.cancel(s.current),i.current+=m,a.current=m,!c(m)&&(Dn||y.preventDefault(),s.current=be(function(){var C=l.current?10:1;o(i.current*C),i.current=0}))}function u(y,m){o(m,!0),Dn||y.preventDefault()}var v=p.useRef(null),f=p.useRef(null);function g(y){if(t){be.cancel(f.current),f.current=be(function(){v.current=null},2);var m=y.deltaX,C=y.deltaY,S=y.shiftKey,E=m,b=C;(v.current==="sx"||!v.current&&S&&C&&!m)&&(E=C,b=0,v.current="sx");var k=Math.abs(E),x=Math.abs(b);v.current===null&&(v.current=e&&k>x?"x":"y"),v.current==="y"?d(y,b):u(y,E)}}function h(y){t&&(l.current=y.detail===a.current)}return[g,h]}var ra=14/15;function oa(t,r,n){var e=p.useRef(!1),o=p.useRef(0),i=p.useRef(null),s=p.useRef(null),a,l=function(v){if(e.current){var f=Math.ceil(v.touches[0].pageY),g=o.current-f;o.current=f,n(g)&&v.preventDefault(),clearInterval(s.current),s.current=setInterval(function(){g*=ra,(!n(g,!0)||Math.abs(g)<=.1)&&clearInterval(s.current)},16)}},c=function(){e.current=!1,a()},d=function(v){a(),v.touches.length===1&&!e.current&&(e.current=!0,o.current=Math.ceil(v.touches[0].pageY),i.current=v.target,i.current.addEventListener("touchmove",l),i.current.addEventListener("touchend",c))};a=function(){i.current&&(i.current.removeEventListener("touchmove",l),i.current.removeEventListener("touchend",c))},Te(function(){return t&&r.current.addEventListener("touchstart",d),function(){var u;(u=r.current)===null||u===void 0||u.removeEventListener("touchstart",d),a(),clearInterval(s.current)}},[t])}var aa=20;function Rn(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=t/r*100;return isNaN(n)&&(n=0),n=Math.max(n,aa),n=Math.min(n,t/2),Math.floor(n)}function ia(t,r,n,e){var o=p.useMemo(function(){return[new Map,[]]},[t,n.id,e]),i=V(o,2),s=i[0],a=i[1],l=function(d){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:d,v=s.get(d),f=s.get(u);if(v===void 0||f===void 0)for(var g=t.length,h=a.length;h<g;h+=1){var y,m=t[h],C=r(m);s.set(C,h);var S=(y=n.get(C))!==null&&y!==void 0?y:e;if(a[h]=(a[h-1]||0)+S,C===d&&(v=h),C===u&&(f=h),v!==void 0&&f!==void 0)break}return{top:a[v-1]||0,bottom:a[f]}};return l}var sa=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles"],la=[],ca={overflowY:"auto",overflowAnchor:"none"};function da(t,r){var n=t.prefixCls,e=n===void 0?"rc-virtual-list":n,o=t.className,i=t.height,s=t.itemHeight,a=t.fullHeight,l=a===void 0?!0:a,c=t.style,d=t.data,u=t.children,v=t.itemKey,f=t.virtual,g=t.direction,h=t.scrollWidth,y=t.component,m=y===void 0?"div":y,C=t.onScroll,S=t.onVirtualScroll,E=t.onVisibleChange,b=t.innerProps,k=t.extraRender,x=t.styles,R=He(t,sa),O=!!(f!==!1&&i&&s),K=O&&d&&(s*d.length>i||!!h),N=g==="rtl",_=fe(e,M({},"".concat(e,"-rtl"),N),o),D=d||la,P=p.useRef(),I=p.useRef(),F=p.useState(0),j=V(F,2),z=j[0],te=j[1],ne=p.useState(0),Q=V(ne,2),ee=Q[0],J=Q[1],Se=p.useState(!1),pe=V(Se,2),Ee=pe[0],he=pe[1],ke=function(){he(!0)},se=function(){he(!1)},Y=p.useCallback(function(T){return typeof v=="function"?v(T):T==null?void 0:T[v]},[v]),G={getKey:Y};function re(T){te(function(L){var B;typeof T=="function"?B=T(L):B=T;var ie=Rr(B);return P.current.scrollTop=ie,ie})}var Z=p.useRef({start:0,end:D.length}),me=p.useRef(),le=ta(D,Y),Ne=V(le,1),ye=Ne[0];me.current=ye;var _e=Qo(Y),q=V(_e,4),A=q[0],$=q[1],U=q[2],ae=q[3],ue=p.useMemo(function(){if(!O)return{scrollHeight:void 0,start:0,end:D.length-1,offset:void 0};if(!K){var T;return{scrollHeight:((T=I.current)===null||T===void 0?void 0:T.offsetHeight)||0,start:0,end:D.length-1,offset:void 0}}for(var L=0,B,ie,Ae,Pr=D.length,Je=0;Je<Pr;Je+=1){var Ar=D[Je],Ir=Y(Ar),gn=U.get(Ir),Pt=L+(gn===void 0?s:gn);Pt>=z&&B===void 0&&(B=Je,ie=L),Pt>z+i&&Ae===void 0&&(Ae=Je),L=Pt}return B===void 0&&(B=0,ie=0,Ae=Math.ceil(i/s)),Ae===void 0&&(Ae=D.length-1),Ae=Math.min(Ae+1,D.length-1),{scrollHeight:L,start:B,end:Ae,offset:ie}},[K,O,z,D,ae,i]),ce=ue.scrollHeight,de=ue.start,Oe=ue.end,Le=ue.offset;Z.current.start=de,Z.current.end=Oe;var rn=p.useState({width:0,height:i}),on=V(rn,2),Pe=on[0],xr=on[1],kr=function(L){xr({width:L.width||L.offsetWidth,height:L.height||L.offsetHeight})},an=p.useRef(),sn=p.useRef(),Or=p.useMemo(function(){return Rn(Pe.width,h)},[Pe.width,h]),Dr=p.useMemo(function(){return Rn(Pe.height,ce)},[Pe.height,ce]),wt=ce-i,Nt=p.useRef(wt);Nt.current=wt;function Rr(T){var L=T;return Number.isNaN(Nt.current)||(L=Math.min(L,Nt.current)),L=Math.max(L,0),L}var ln=z<=0,cn=z>=wt,wr=ir(ln,cn),Kt=function(){return{x:N?-ee:ee,y:z}},_t=p.useRef(Kt()),Mt=On(function(){if(S){var T=Kt();(_t.current.x!==T.x||_t.current.y!==T.y)&&(S(T),_t.current=T)}});function dn(T,L){var B=T;L?(mn.flushSync(function(){J(B)}),Mt()):re(B)}function Nr(T){var L=T.currentTarget.scrollTop;L!==z&&re(L),C==null||C(T),Mt()}var Tt=function(L){var B=L,ie=h-Pe.width;return B=Math.max(B,0),B=Math.min(B,ie),B},Kr=On(function(T,L){L?(mn.flushSync(function(){J(function(B){var ie=B+(N?-T:T);return Tt(ie)})}),Mt()):re(function(B){var ie=B+T;return ie})}),_r=na(O,ln,cn,!!h,Kr),un=V(_r,2),Lt=un[0],fn=un[1];oa(O,P,function(T,L){return wr(T,L)?!1:(Lt({preventDefault:function(){},deltaY:T}),!0)}),Te(function(){function T(B){O&&B.preventDefault()}var L=P.current;return L.addEventListener("wheel",Lt),L.addEventListener("DOMMouseScroll",fn),L.addEventListener("MozMousePixelScroll",T),function(){L.removeEventListener("wheel",Lt),L.removeEventListener("DOMMouseScroll",fn),L.removeEventListener("MozMousePixelScroll",T)}},[O]),Te(function(){h&&J(function(T){return Tt(T)})},[Pe.width,h]);var vn=function(){var L,B;(L=an.current)===null||L===void 0||L.delayHidden(),(B=sn.current)===null||B===void 0||B.delayHidden()},pn=Zo(P,D,U,s,Y,function(){return $(!0)},re,vn);p.useImperativeHandle(r,function(){return{getScrollInfo:Kt,scrollTo:function(L){function B(ie){return ie&&we(ie)==="object"&&("left"in ie||"top"in ie)}B(L)?(L.left!==void 0&&J(Tt(L.left)),pn(L.top)):pn(L)}}}),Te(function(){if(E){var T=D.slice(de,Oe+1);E(T,D)}},[de,Oe,D]);var Mr=ia(D,Y,U,s),Tr=k==null?void 0:k({start:de,end:Oe,virtual:K,offsetX:ee,offsetY:Le,rtl:N,getSize:Mr}),Lr=qo(D,de,Oe,h,A,u,G),Qe=null;i&&(Qe=w(M({},l?"height":"maxHeight",i),ca),O&&(Qe.overflowY="hidden",h&&(Qe.overflowX="hidden"),Ee&&(Qe.pointerEvents="none")));var hn={};return N&&(hn.dir="rtl"),p.createElement("div",ge({style:w(w({},c),{},{position:"relative"}),className:_},hn,R),p.createElement(Zt,{onResize:kr},p.createElement(m,{className:"".concat(e,"-holder"),style:Qe,ref:P,onScroll:Nr,onMouseEnter:vn},p.createElement(ar,{prefixCls:e,height:ce,offsetX:ee,offsetY:Le,scrollWidth:h,onInnerResize:$,ref:I,innerProps:b,rtl:N,extra:Tr},Lr))),K&&ce>i&&p.createElement(kn,{ref:an,prefixCls:e,scrollOffset:z,scrollRange:ce,rtl:N,onScroll:dn,onStartMove:ke,onStopMove:se,spinSize:Dr,containerSize:Pe.height,style:x==null?void 0:x.verticalScrollBar,thumbStyle:x==null?void 0:x.verticalScrollBarThumb}),K&&h&&p.createElement(kn,{ref:sn,prefixCls:e,scrollOffset:ee,scrollRange:h,rtl:N,onScroll:dn,onStartMove:ke,onStopMove:se,spinSize:Or,containerSize:Pe.width,horizontal:!0,style:x==null?void 0:x.horizontalScrollBar,thumbStyle:x==null?void 0:x.horizontalScrollBarThumb}))}var sr=p.forwardRef(da);sr.displayName="List";var ua=p.createContext({}),fa=function(t){rt(n,t);var r=ot(n);function n(){return qe(this,n),r.apply(this,arguments)}return Xe(n,[{key:"render",value:function(){return this.props.children}}]),n}(p.Component),Be="none",it="appear",st="enter",lt="leave",wn="none",De="prepare",Ue="start",Ge="active",en="end",lr="prepared";function Nn(t,r){var n={};return n[t.toLowerCase()]=r.toLowerCase(),n["Webkit".concat(t)]="webkit".concat(r),n["Moz".concat(t)]="moz".concat(r),n["ms".concat(t)]="MS".concat(r),n["O".concat(t)]="o".concat(r.toLowerCase()),n}function va(t,r){var n={animationend:Nn("Animation","AnimationEnd"),transitionend:Nn("Transition","TransitionEnd")};return t&&("AnimationEvent"in r||delete n.animationend.animation,"TransitionEvent"in r||delete n.transitionend.transition),n}var pa=va(gt(),typeof window<"u"?window:{}),cr={};if(gt()){var ha=document.createElement("div");cr=ha.style}var ct={};function dr(t){if(ct[t])return ct[t];var r=pa[t];if(r)for(var n=Object.keys(r),e=n.length,o=0;o<e;o+=1){var i=n[o];if(Object.prototype.hasOwnProperty.call(r,i)&&i in cr)return ct[t]=r[i],ct[t]}return""}var ur=dr("animationend"),fr=dr("transitionend"),vr=!!(ur&&fr),Kn=ur||"animationend",_n=fr||"transitionend";function Mn(t,r){if(!t)return null;if(we(t)==="object"){var n=r.replace(/-\w/g,function(e){return e[1].toUpperCase()});return t[n]}return"".concat(t,"-").concat(r)}const ga=function(t){var r=p.useRef(),n=p.useRef(t);n.current=t;var e=p.useCallback(function(s){n.current(s)},[]);function o(s){s&&(s.removeEventListener(_n,e),s.removeEventListener(Kn,e))}function i(s){r.current&&r.current!==s&&o(r.current),s&&s!==r.current&&(s.addEventListener(_n,e),s.addEventListener(Kn,e),r.current=s)}return p.useEffect(function(){return function(){o(r.current)}},[]),[i,o]};var pr=gt()?p.useLayoutEffect:p.useEffect;const ma=function(){var t=p.useRef(null);function r(){be.cancel(t.current)}function n(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:2;r();var i=be(function(){o<=1?e({isCanceled:function(){return i!==t.current}}):n(e,o-1)});t.current=i}return p.useEffect(function(){return function(){r()}},[]),[n,r]};var ya=[De,Ue,Ge,en],Ca=[De,lr],hr=!1,Sa=!0;function gr(t){return t===Ge||t===en}const Ea=function(t,r,n){var e=dt(wn),o=V(e,2),i=o[0],s=o[1],a=ma(),l=V(a,2),c=l[0],d=l[1];function u(){s(De,!0)}var v=r?Ca:ya;return pr(function(){if(i!==wn&&i!==en){var f=v.indexOf(i),g=v[f+1],h=n(i);h===hr?s(g,!0):g&&c(function(y){function m(){y.isCanceled()||s(g,!0)}h===!0?m():Promise.resolve(h).then(m)})}},[t,i]),p.useEffect(function(){return function(){d()}},[]),[u,i]};function ba(t,r,n,e){var o=e.motionEnter,i=o===void 0?!0:o,s=e.motionAppear,a=s===void 0?!0:s,l=e.motionLeave,c=l===void 0?!0:l,d=e.motionDeadline,u=e.motionLeaveImmediately,v=e.onAppearPrepare,f=e.onEnterPrepare,g=e.onLeavePrepare,h=e.onAppearStart,y=e.onEnterStart,m=e.onLeaveStart,C=e.onAppearActive,S=e.onEnterActive,E=e.onLeaveActive,b=e.onAppearEnd,k=e.onEnterEnd,x=e.onLeaveEnd,R=e.onVisibleChanged,O=dt(),K=V(O,2),N=K[0],_=K[1],D=dt(Be),P=V(D,2),I=P[0],F=P[1],j=dt(null),z=V(j,2),te=z[0],ne=z[1],Q=p.useRef(!1),ee=p.useRef(null);function J(){return n()}var Se=p.useRef(!1);function pe(){F(Be,!0),ne(null,!0)}function Ee(q){var A=J();if(!(q&&!q.deadline&&q.target!==A)){var $=Se.current,U;I===it&&$?U=b==null?void 0:b(A,q):I===st&&$?U=k==null?void 0:k(A,q):I===lt&&$&&(U=x==null?void 0:x(A,q)),I!==Be&&$&&U!==!1&&pe()}}var he=ga(Ee),ke=V(he,1),se=ke[0],Y=function(A){var $,U,ae;switch(A){case it:return $={},M($,De,v),M($,Ue,h),M($,Ge,C),$;case st:return U={},M(U,De,f),M(U,Ue,y),M(U,Ge,S),U;case lt:return ae={},M(ae,De,g),M(ae,Ue,m),M(ae,Ge,E),ae;default:return{}}},G=p.useMemo(function(){return Y(I)},[I]),re=Ea(I,!t,function(q){if(q===De){var A=G[De];return A?A(J()):hr}if(le in G){var $;ne((($=G[le])===null||$===void 0?void 0:$.call(G,J(),null))||null)}return le===Ge&&(se(J()),d>0&&(clearTimeout(ee.current),ee.current=setTimeout(function(){Ee({deadline:!0})},d))),le===lr&&pe(),Sa}),Z=V(re,2),me=Z[0],le=Z[1],Ne=gr(le);Se.current=Ne,pr(function(){_(r);var q=Q.current;Q.current=!0;var A;!q&&r&&a&&(A=it),q&&r&&i&&(A=st),(q&&!r&&c||!q&&u&&!r&&c)&&(A=lt);var $=Y(A);A&&(t||$[De])?(F(A),me()):F(Be)},[r]),p.useEffect(function(){(I===it&&!a||I===st&&!i||I===lt&&!c)&&F(Be)},[a,i,c]),p.useEffect(function(){return function(){Q.current=!1,clearTimeout(ee.current)}},[]);var ye=p.useRef(!1);p.useEffect(function(){N&&(ye.current=!0),N!==void 0&&I===Be&&((ye.current||N)&&(R==null||R(N)),ye.current=!0)},[N,I]);var _e=te;return G[De]&&le===Ue&&(_e=w({transition:"none"},_e)),[I,le,_e,N??r]}function xa(t){var r=t;we(t)==="object"&&(r=t.transitionSupport);function n(o,i){return!!(o.motionName&&r&&i!==!1)}var e=p.forwardRef(function(o,i){var s=o.visible,a=s===void 0?!0:s,l=o.removeOnLeave,c=l===void 0?!0:l,d=o.forceRender,u=o.children,v=o.motionName,f=o.leavedClassName,g=o.eventProps,h=p.useContext(ua),y=h.motion,m=n(o,y),C=p.useRef(),S=p.useRef();function E(){try{return C.current instanceof HTMLElement?C.current:et(S.current)}catch{return null}}var b=ba(m,a,E,o),k=V(b,4),x=k[0],R=k[1],O=k[2],K=k[3],N=p.useRef(K);K&&(N.current=!0);var _=p.useCallback(function(ne){C.current=ne,Qn(i,ne)},[i]),D,P=w(w({},g),{},{visible:a});if(!u)D=null;else if(x===Be)K?D=u(w({},P),_):!c&&N.current&&f?D=u(w(w({},P),{},{className:f}),_):d||!c&&!f?D=u(w(w({},P),{},{style:{display:"none"}}),_):D=null;else{var I,F;R===De?F="prepare":gr(R)?F="active":R===Ue&&(F="start");var j=Mn(v,"".concat(x,"-").concat(F));D=u(w(w({},P),{},{className:fe(Mn(v,x),(I={},M(I,j,j&&F),M(I,v,typeof v=="string"),I)),style:O}),_)}if(p.isValidElement(D)&&Jn(D)){var z=D,te=z.ref;te||(D=p.cloneElement(D,{ref:_}))}return p.createElement(fa,{ref:S},D)});return e.displayName="CSSMotion",e}const mr=xa(vr);var Wt="add",Ut="keep",Gt="remove",It="removed";function ka(t){var r;return t&&we(t)==="object"&&"key"in t?r=t:r={key:t},w(w({},r),{},{key:String(r.key)})}function Yt(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return t.map(ka)}function Oa(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],n=[],e=0,o=r.length,i=Yt(t),s=Yt(r);i.forEach(function(c){for(var d=!1,u=e;u<o;u+=1){var v=s[u];if(v.key===c.key){e<u&&(n=n.concat(s.slice(e,u).map(function(f){return w(w({},f),{},{status:Wt})})),e=u),n.push(w(w({},v),{},{status:Ut})),e+=1,d=!0;break}}d||n.push(w(w({},c),{},{status:Gt}))}),e<o&&(n=n.concat(s.slice(e).map(function(c){return w(w({},c),{},{status:Wt})})));var a={};n.forEach(function(c){var d=c.key;a[d]=(a[d]||0)+1});var l=Object.keys(a).filter(function(c){return a[c]>1});return l.forEach(function(c){n=n.filter(function(d){var u=d.key,v=d.status;return u!==c||v!==Gt}),n.forEach(function(d){d.key===c&&(d.status=Ut)})}),n}var Da=["component","children","onVisibleChanged","onAllRemoved"],Ra=["status"],wa=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];function Na(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:mr,n=function(e){rt(i,e);var o=ot(i);function i(){var s;qe(this,i);for(var a=arguments.length,l=new Array(a),c=0;c<a;c++)l[c]=arguments[c];return s=o.call.apply(o,[this].concat(l)),M(Ke(s),"state",{keyEntities:[]}),M(Ke(s),"removeKey",function(d){var u=s.state.keyEntities,v=u.map(function(f){return f.key!==d?f:w(w({},f),{},{status:It})});return s.setState({keyEntities:v}),v.filter(function(f){var g=f.status;return g!==It}).length}),s}return Xe(i,[{key:"render",value:function(){var a=this,l=this.state.keyEntities,c=this.props,d=c.component,u=c.children,v=c.onVisibleChanged,f=c.onAllRemoved,g=He(c,Da),h=d||p.Fragment,y={};return wa.forEach(function(m){y[m]=g[m],delete g[m]}),delete g.keys,p.createElement(h,g,l.map(function(m,C){var S=m.status,E=He(m,Ra),b=S===Wt||S===Ut;return p.createElement(r,ge({},y,{key:E.key,visible:b,eventProps:E,onVisibleChanged:function(x){if(v==null||v(x,{key:E.key}),!x){var R=a.removeKey(E.key);R===0&&f&&f()}}}),function(k,x){return u(w(w({},k),{},{index:C}),x)})}))}}],[{key:"getDerivedStateFromProps",value:function(a,l){var c=a.keys,d=l.keyEntities,u=Yt(c),v=Oa(d,u);return{keyEntities:v.filter(function(f){var g=d.find(function(h){var y=h.key;return f.key===y});return!(g&&g.status===It&&f.status===Gt)})}}}]),i}(p.Component);return M(n,"defaultProps",{component:"div"}),n}Na(vr);var Ka=function(r){for(var n=r.prefixCls,e=r.level,o=r.isStart,i=r.isEnd,s="".concat(n,"-indent-unit"),a=[],l=0;l<e;l+=1){var c;a.push(p.createElement("span",{key:l,className:fe(s,(c={},M(c,"".concat(s,"-start"),o[l]),M(c,"".concat(s,"-end"),i[l]),c))}))}return p.createElement("span",{"aria-hidden":"true",className:"".concat(n,"-indent")},a)};const _a=p.memo(Ka);function Ce(t,r){return t[r]}var Ma=["children"];function yr(t,r){return"".concat(t,"-").concat(r)}function Ta(t){return t&&t.type&&t.type.isTreeNode}function at(t,r){return t??r}function ht(t){var r=t||{},n=r.title,e=r._title,o=r.key,i=r.children,s=n||"title";return{title:s,_title:e||[s],key:o||"key",children:i||"children"}}function La(t){function r(n){var e=ft(n);return e.map(function(o){if(!Ta(o))return Re(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var i=o.key,s=o.props,a=s.children,l=He(s,Ma),c=w({key:i},l),d=r(a);return d.length&&(c.children=d),c}).filter(function(o){return o})}return r(t)}function Ft(t,r,n){var e=ht(n),o=e._title,i=e.key,s=e.children,a=new Set(r===!0?[]:r),l=[];function c(d){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return d.map(function(v,f){for(var g=yr(u?u.pos:"0",f),h=at(v[i],g),y,m=0;m<o.length;m+=1){var C=o[m];if(v[C]!==void 0){y=v[C];break}}var S=w(w({},Br(v,[].concat(ze(o),[i,s]))),{},{title:y,key:h,parent:u,pos:g,children:null,data:v,isStart:[].concat(ze(u?u.isStart:[]),[f===0]),isEnd:[].concat(ze(u?u.isEnd:[]),[f===d.length-1])});return l.push(S),r===!0||a.has(h)?S.children=c(v[s]||[],S):S.children=[],S})}return c(t),l}function Pa(t,r,n){var e={};we(n)==="object"?e=n:e={externalGetKey:n},e=e||{};var o=e,i=o.childrenPropName,s=o.externalGetKey,a=o.fieldNames,l=ht(a),c=l.key,d=l.children,u=i||d,v;s?typeof s=="string"?v=function(h){return h[s]}:typeof s=="function"&&(v=function(h){return s(h)}):v=function(h,y){return at(h[c],y)};function f(g,h,y,m){var C=g?g[u]:t,S=g?yr(y.pos,h):"0",E=g?[].concat(ze(m),[g]):[];if(g){var b=v(g,S),k={node:g,index:h,pos:S,key:b,parentPos:y.node?y.pos:null,level:y.level+1,nodes:E};r(k)}C&&C.forEach(function(x,R){f(x,R,{node:g,pos:S,level:y?y.level+1:-1},E)})}f(null)}function Aa(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=r.initWrapper,e=r.processEntity,o=r.onProcessFinished,i=r.externalGetKey,s=r.childrenPropName,a=r.fieldNames,l=arguments.length>2?arguments[2]:void 0,c=i||l,d={},u={},v={posEntities:d,keyEntities:u};return n&&(v=n(v)||v),Pa(t,function(f){var g=f.node,h=f.index,y=f.pos,m=f.key,C=f.parentPos,S=f.level,E=f.nodes,b={node:g,nodes:E,index:h,key:m,pos:y,level:S},k=at(m,y);d[y]=b,u[k]=b,b.parent=d[C],b.parent&&(b.parent.children=b.parent.children||[],b.parent.children.push(b)),e&&e(b,v)},{externalGetKey:c,childrenPropName:s,fieldNames:a}),o&&o(v),v}function tt(t,r){var n=r.expandedKeys,e=r.selectedKeys,o=r.loadedKeys,i=r.loadingKeys,s=r.checkedKeys,a=r.halfCheckedKeys,l=r.dragOverNodeKey,c=r.dropPosition,d=r.keyEntities,u=Ce(d,t),v={eventKey:t,expanded:n.indexOf(t)!==-1,selected:e.indexOf(t)!==-1,loaded:o.indexOf(t)!==-1,loading:i.indexOf(t)!==-1,checked:s.indexOf(t)!==-1,halfChecked:a.indexOf(t)!==-1,pos:String(u?u.pos:""),dragOver:l===t&&c===0,dragOverGapTop:l===t&&c===-1,dragOverGapBottom:l===t&&c===1};return v}function oe(t){var r=t.data,n=t.expanded,e=t.selected,o=t.checked,i=t.loaded,s=t.loading,a=t.halfChecked,l=t.dragOver,c=t.dragOverGapTop,d=t.dragOverGapBottom,u=t.pos,v=t.active,f=t.eventKey,g=w(w({},r),{},{expanded:n,selected:e,checked:o,loaded:i,loading:s,halfChecked:a,dragOver:l,dragOverGapTop:c,dragOverGapBottom:d,pos:u,active:v,key:f});return"props"in g||Object.defineProperty(g,"props",{get:function(){return Re(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),t}}),g}var Ia=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],Tn="open",Ln="close",Fa="---",Va=function(t){rt(n,t);var r=ot(n);function n(){var e;qe(this,n);for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return e=r.call.apply(r,[this].concat(i)),e.state={dragNodeHighlight:!1},e.selectHandle=void 0,e.cacheIndent=void 0,e.onSelectorClick=function(a){var l=e.props.context.onNodeClick;l(a,oe(e.props)),e.isSelectable()?e.onSelect(a):e.onCheck(a)},e.onSelectorDoubleClick=function(a){var l=e.props.context.onNodeDoubleClick;l(a,oe(e.props))},e.onSelect=function(a){if(!e.isDisabled()){var l=e.props.context.onNodeSelect;l(a,oe(e.props))}},e.onCheck=function(a){if(!e.isDisabled()){var l=e.props,c=l.disableCheckbox,d=l.checked,u=e.props.context.onNodeCheck;if(!(!e.isCheckable()||c)){var v=!d;u(a,oe(e.props),v)}}},e.onMouseEnter=function(a){var l=e.props.context.onNodeMouseEnter;l(a,oe(e.props))},e.onMouseLeave=function(a){var l=e.props.context.onNodeMouseLeave;l(a,oe(e.props))},e.onContextMenu=function(a){var l=e.props.context.onNodeContextMenu;l(a,oe(e.props))},e.onDragStart=function(a){var l=e.props.context.onNodeDragStart;a.stopPropagation(),e.setState({dragNodeHighlight:!0}),l(a,Ke(e));try{a.dataTransfer.setData("text/plain","")}catch{}},e.onDragEnter=function(a){var l=e.props.context.onNodeDragEnter;a.preventDefault(),a.stopPropagation(),l(a,Ke(e))},e.onDragOver=function(a){var l=e.props.context.onNodeDragOver;a.preventDefault(),a.stopPropagation(),l(a,Ke(e))},e.onDragLeave=function(a){var l=e.props.context.onNodeDragLeave;a.stopPropagation(),l(a,Ke(e))},e.onDragEnd=function(a){var l=e.props.context.onNodeDragEnd;a.stopPropagation(),e.setState({dragNodeHighlight:!1}),l(a,Ke(e))},e.onDrop=function(a){var l=e.props.context.onNodeDrop;a.preventDefault(),a.stopPropagation(),e.setState({dragNodeHighlight:!1}),l(a,Ke(e))},e.onExpand=function(a){var l=e.props,c=l.loading,d=l.context.onNodeExpand;c||d(a,oe(e.props))},e.setSelectHandle=function(a){e.selectHandle=a},e.getNodeState=function(){var a=e.props.expanded;return e.isLeaf()?null:a?Tn:Ln},e.hasChildren=function(){var a=e.props.eventKey,l=e.props.context.keyEntities,c=Ce(l,a)||{},d=c.children;return!!(d||[]).length},e.isLeaf=function(){var a=e.props,l=a.isLeaf,c=a.loaded,d=e.props.context.loadData,u=e.hasChildren();return l===!1?!1:l||!d&&!u||d&&c&&!u},e.isDisabled=function(){var a=e.props.disabled,l=e.props.context.disabled;return!!(l||a)},e.isCheckable=function(){var a=e.props.checkable,l=e.props.context.checkable;return!l||a===!1?!1:l},e.syncLoadData=function(a){var l=a.expanded,c=a.loading,d=a.loaded,u=e.props.context,v=u.loadData,f=u.onNodeLoad;c||v&&l&&!e.isLeaf()&&!e.hasChildren()&&!d&&f(oe(e.props))},e.isDraggable=function(){var a=e.props,l=a.data,c=a.context.draggable;return!!(c&&(!c.nodeDraggable||c.nodeDraggable(l)))},e.renderDragHandler=function(){var a=e.props.context,l=a.draggable,c=a.prefixCls;return l!=null&&l.icon?p.createElement("span",{className:"".concat(c,"-draggable-icon")},l.icon):null},e.renderSwitcherIconDom=function(a){var l=e.props.switcherIcon,c=e.props.context.switcherIcon,d=l||c;return typeof d=="function"?d(w(w({},e.props),{},{isLeaf:a})):d},e.renderSwitcher=function(){var a=e.props.expanded,l=e.props.context.prefixCls;if(e.isLeaf()){var c=e.renderSwitcherIconDom(!0);return c!==!1?p.createElement("span",{className:fe("".concat(l,"-switcher"),"".concat(l,"-switcher-noop"))},c):null}var d=fe("".concat(l,"-switcher"),"".concat(l,"-switcher_").concat(a?Tn:Ln)),u=e.renderSwitcherIconDom(!1);return u!==!1?p.createElement("span",{onClick:e.onExpand,className:d},u):null},e.renderCheckbox=function(){var a=e.props,l=a.checked,c=a.halfChecked,d=a.disableCheckbox,u=e.props.context.prefixCls,v=e.isDisabled(),f=e.isCheckable();if(!f)return null;var g=typeof f!="boolean"?f:null;return p.createElement("span",{className:fe("".concat(u,"-checkbox"),l&&"".concat(u,"-checkbox-checked"),!l&&c&&"".concat(u,"-checkbox-indeterminate"),(v||d)&&"".concat(u,"-checkbox-disabled")),onClick:e.onCheck},g)},e.renderIcon=function(){var a=e.props.loading,l=e.props.context.prefixCls;return p.createElement("span",{className:fe("".concat(l,"-iconEle"),"".concat(l,"-icon__").concat(e.getNodeState()||"docu"),a&&"".concat(l,"-icon_loading"))})},e.renderSelector=function(){var a=e.state.dragNodeHighlight,l=e.props,c=l.title,d=c===void 0?Fa:c,u=l.selected,v=l.icon,f=l.loading,g=l.data,h=e.props.context,y=h.prefixCls,m=h.showIcon,C=h.icon,S=h.loadData,E=h.titleRender,b=e.isDisabled(),k="".concat(y,"-node-content-wrapper"),x;if(m){var R=v||C;x=R?p.createElement("span",{className:fe("".concat(y,"-iconEle"),"".concat(y,"-icon__customize"))},typeof R=="function"?R(e.props):R):e.renderIcon()}else S&&f&&(x=e.renderIcon());var O;typeof d=="function"?O=d(g):E?O=E(g):O=d;var K=p.createElement("span",{className:"".concat(y,"-title")},O);return p.createElement("span",{ref:e.setSelectHandle,title:typeof d=="string"?d:"",className:fe("".concat(k),"".concat(k,"-").concat(e.getNodeState()||"normal"),!b&&(u||a)&&"".concat(y,"-node-selected")),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onContextMenu:e.onContextMenu,onClick:e.onSelectorClick,onDoubleClick:e.onSelectorDoubleClick},x,K,e.renderDropIndicator())},e.renderDropIndicator=function(){var a=e.props,l=a.disabled,c=a.eventKey,d=e.props.context,u=d.draggable,v=d.dropLevelOffset,f=d.dropPosition,g=d.prefixCls,h=d.indent,y=d.dropIndicatorRender,m=d.dragOverNodeKey,C=d.direction,S=!!u,E=!l&&S&&m===c,b=h??e.cacheIndent;return e.cacheIndent=h,E?y({dropPosition:f,dropLevelOffset:v,indent:b,prefixCls:g,direction:C}):null},e}return Xe(n,[{key:"componentDidMount",value:function(){this.syncLoadData(this.props)}},{key:"componentDidUpdate",value:function(){this.syncLoadData(this.props)}},{key:"isSelectable",value:function(){var o=this.props.selectable,i=this.props.context.selectable;return typeof o=="boolean"?o:i}},{key:"render",value:function(){var o,i=this.props,s=i.eventKey,a=i.className,l=i.style,c=i.dragOver,d=i.dragOverGapTop,u=i.dragOverGapBottom,v=i.isLeaf,f=i.isStart,g=i.isEnd,h=i.expanded,y=i.selected,m=i.checked,C=i.halfChecked,S=i.loading,E=i.domRef,b=i.active;i.data;var k=i.onMouseMove,x=i.selectable,R=He(i,Ia),O=this.props.context,K=O.prefixCls,N=O.filterTreeNode,_=O.keyEntities,D=O.dropContainerKey,P=O.dropTargetKey,I=O.draggingNodeKey,F=this.isDisabled(),j=Wn(R,{aria:!0,data:!0}),z=Ce(_,s)||{},te=z.level,ne=g[g.length-1],Q=this.isDraggable(),ee=!F&&Q,J=I===s,Se=x!==void 0?{"aria-selected":!!x}:void 0;return p.createElement("div",ge({ref:E,className:fe(a,"".concat(K,"-treenode"),(o={},M(o,"".concat(K,"-treenode-disabled"),F),M(o,"".concat(K,"-treenode-switcher-").concat(h?"open":"close"),!v),M(o,"".concat(K,"-treenode-checkbox-checked"),m),M(o,"".concat(K,"-treenode-checkbox-indeterminate"),C),M(o,"".concat(K,"-treenode-selected"),y),M(o,"".concat(K,"-treenode-loading"),S),M(o,"".concat(K,"-treenode-active"),b),M(o,"".concat(K,"-treenode-leaf-last"),ne),M(o,"".concat(K,"-treenode-draggable"),Q),M(o,"dragging",J),M(o,"drop-target",P===s),M(o,"drop-container",D===s),M(o,"drag-over",!F&&c),M(o,"drag-over-gap-top",!F&&d),M(o,"drag-over-gap-bottom",!F&&u),M(o,"filter-node",N&&N(oe(this.props))),o)),style:l,draggable:ee,"aria-grabbed":J,onDragStart:ee?this.onDragStart:void 0,onDragEnter:Q?this.onDragEnter:void 0,onDragOver:Q?this.onDragOver:void 0,onDragLeave:Q?this.onDragLeave:void 0,onDrop:Q?this.onDrop:void 0,onDragEnd:Q?this.onDragEnd:void 0,onMouseMove:k},Se,j),p.createElement(_a,{prefixCls:K,level:te,isStart:f,isEnd:g}),this.renderDragHandler(),this.renderSwitcher(),this.renderCheckbox(),this.renderSelector())}}]),n}(p.Component),nt=function(r){return p.createElement(Xt.Consumer,null,function(n){return p.createElement(Va,ge({},r,{context:n}))})};nt.displayName="TreeNode";nt.isTreeNode=1;function $a(t,r){var n=p.useState(!1),e=V(n,2),o=e[0],i=e[1];Te(function(){if(o)return t(),function(){r()}},[o]),Te(function(){return i(!0),function(){i(!1)}},[])}var Ba=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Cr=function(r,n){var e=r.className,o=r.style,i=r.motion,s=r.motionNodes,a=r.motionType,l=r.onMotionStart,c=r.onMotionEnd,d=r.active,u=r.treeNodeRequiredProps,v=He(r,Ba),f=p.useState(!0),g=V(f,2),h=g[0],y=g[1],m=p.useContext(Xt),C=m.prefixCls,S=s&&a!=="hide";Te(function(){s&&S!==h&&y(S)},[s]);var E=function(){s&&l()},b=p.useRef(!1),k=function(){s&&!b.current&&(b.current=!0,c())};$a(E,k);var x=function(O){S===O&&k()};return s?p.createElement(mr,ge({ref:n,visible:h},i,{motionAppear:a==="show",onVisibleChanged:x}),function(R,O){var K=R.className,N=R.style;return p.createElement("div",{ref:O,className:fe("".concat(C,"-treenode-motion"),K),style:N},s.map(function(_){var D=ge({},(Gn(_.data),_.data)),P=_.title,I=_.key,F=_.isStart,j=_.isEnd;delete D.children;var z=tt(I,u);return p.createElement(nt,ge({},D,z,{title:P,active:d,data:_.data,key:I,isStart:F,isEnd:j}))}))}):p.createElement(nt,ge({domRef:n,className:e,style:o},v,{active:d}))};Cr.displayName="MotionTreeNode";var za=p.forwardRef(Cr);function Ha(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],n=t.length,e=r.length;if(Math.abs(n-e)!==1)return{add:!1,key:null};function o(i,s){var a=new Map;i.forEach(function(c){a.set(c,!0)});var l=s.filter(function(c){return!a.has(c)});return l.length===1?l[0]:null}return n<e?{add:!0,key:o(t,r)}:{add:!1,key:o(r,t)}}function Pn(t,r,n){var e=t.findIndex(function(a){return a.key===n}),o=t[e+1],i=r.findIndex(function(a){return a.key===n});if(o){var s=r.findIndex(function(a){return a.key===o.key});return r.slice(i+1,s)}return r.slice(i+1)}var ja=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],An={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Wa=function(){},je="RC_TREE_MOTION_".concat(Math.random()),qt={key:je},Sr={key:je,level:0,index:0,pos:"0",node:qt,nodes:[qt]},In={parent:null,children:[],pos:Sr.pos,data:qt,title:null,key:je,isStart:[],isEnd:[]};function Fn(t,r,n,e){return r===!1||!n?t:t.slice(0,Math.ceil(n/e)+1)}function Vn(t){var r=t.key,n=t.pos;return at(r,n)}function Ua(t){for(var r=String(t.data.key),n=t;n.parent;)n=n.parent,r="".concat(n.data.key," > ").concat(r);return r}var Er=p.forwardRef(function(t,r){var n=t.prefixCls,e=t.data;t.selectable,t.checkable;var o=t.expandedKeys,i=t.selectedKeys,s=t.checkedKeys,a=t.loadedKeys,l=t.loadingKeys,c=t.halfCheckedKeys,d=t.keyEntities,u=t.disabled,v=t.dragging,f=t.dragOverNodeKey,g=t.dropPosition,h=t.motion,y=t.height,m=t.itemHeight,C=t.virtual,S=t.focusable,E=t.activeItem,b=t.focused,k=t.tabIndex,x=t.onKeyDown,R=t.onFocus,O=t.onBlur,K=t.onActiveChange,N=t.onListChangeStart,_=t.onListChangeEnd,D=He(t,ja),P=p.useRef(null),I=p.useRef(null);p.useImperativeHandle(r,function(){return{scrollTo:function($){P.current.scrollTo($)},getIndentWidth:function(){return I.current.offsetWidth}}});var F=p.useState(o),j=V(F,2),z=j[0],te=j[1],ne=p.useState(e),Q=V(ne,2),ee=Q[0],J=Q[1],Se=p.useState(e),pe=V(Se,2),Ee=pe[0],he=pe[1],ke=p.useState([]),se=V(ke,2),Y=se[0],G=se[1],re=p.useState(null),Z=V(re,2),me=Z[0],le=Z[1],Ne=p.useRef(e);Ne.current=e;function ye(){var A=Ne.current;J(A),he(A),G([]),le(null),_()}Te(function(){te(o);var A=Ha(z,o);if(A.key!==null)if(A.add){var $=ee.findIndex(function(Oe){var Le=Oe.key;return Le===A.key}),U=Fn(Pn(ee,e,A.key),C,y,m),ae=ee.slice();ae.splice($+1,0,In),he(ae),G(U),le("show")}else{var ue=e.findIndex(function(Oe){var Le=Oe.key;return Le===A.key}),ce=Fn(Pn(e,ee,A.key),C,y,m),de=e.slice();de.splice(ue+1,0,In),he(de),G(ce),le("hide")}else ee!==e&&(J(e),he(e))},[o,e]),p.useEffect(function(){v||ye()},[v]);var _e=h?Ee:e,q={expandedKeys:o,selectedKeys:i,loadedKeys:a,loadingKeys:l,checkedKeys:s,halfCheckedKeys:c,dragOverNodeKey:f,dropPosition:g,keyEntities:d};return p.createElement(p.Fragment,null,b&&E&&p.createElement("span",{style:An,"aria-live":"assertive"},Ua(E)),p.createElement("div",null,p.createElement("input",{style:An,disabled:S===!1||u,tabIndex:S!==!1?k:null,onKeyDown:x,onFocus:R,onBlur:O,value:"",onChange:Wa,"aria-label":"for screen reader"})),p.createElement("div",{className:"".concat(n,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},p.createElement("div",{className:"".concat(n,"-indent")},p.createElement("div",{ref:I,className:"".concat(n,"-indent-unit")}))),p.createElement(sr,ge({},D,{data:_e,itemKey:Vn,height:y,fullHeight:!1,virtual:C,itemHeight:m,prefixCls:"".concat(n,"-list"),ref:P,onVisibleChange:function($,U){var ae=new Set($),ue=U.filter(function(ce){return!ae.has(ce)});ue.some(function(ce){return Vn(ce)===je})&&ye()}}),function(A){var $=A.pos,U=ge({},(Gn(A.data),A.data)),ae=A.title,ue=A.key,ce=A.isStart,de=A.isEnd,Oe=at(ue,$);delete U.key,delete U.children;var Le=tt(Oe,q);return p.createElement(za,ge({},U,Le,{title:ae,active:!!E&&ue===E.key,pos:$,data:A.data,isStart:ce,isEnd:de,motion:h,motionNodes:ue===je?Y:null,motionType:me,onMotionStart:N,onMotionEnd:ye,treeNodeRequiredProps:q,onMouseMove:function(){K(null)}}))}))});Er.displayName="NodeList";function Me(t,r){if(!t)return[];var n=t.slice(),e=n.indexOf(r);return e>=0&&n.splice(e,1),n}function Ie(t,r){var n=(t||[]).slice();return n.indexOf(r)===-1&&n.push(r),n}function tn(t){return t.split("-")}function Ga(t,r){var n=[],e=Ce(r,t);function o(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];i.forEach(function(s){var a=s.key,l=s.children;n.push(a),o(l)})}return o(e.children),n}function Ya(t){if(t.parent){var r=tn(t.pos);return Number(r[r.length-1])===t.parent.children.length-1}return!1}function qa(t){var r=tn(t.pos);return Number(r[r.length-1])===0}function $n(t,r,n,e,o,i,s,a,l,c){var d,u=t.clientX,v=t.clientY,f=t.target.getBoundingClientRect(),g=f.top,h=f.height,y=(c==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),m=(y-12)/e,C=Ce(a,n.props.eventKey);if(v<g+h/2){var S=s.findIndex(function(I){return I.key===C.key}),E=S<=0?0:S-1,b=s[E].key;C=Ce(a,b)}var k=C.key,x=C,R=C.key,O=0,K=0;if(!l.includes(k))for(var N=0;N<m&&Ya(C);N+=1)C=C.parent,K+=1;var _=r.props.data,D=C.node,P=!0;return qa(C)&&C.level===0&&v<g+h/2&&i({dragNode:_,dropNode:D,dropPosition:-1})&&C.key===n.props.eventKey?O=-1:(x.children||[]).length&&l.includes(R)?i({dragNode:_,dropNode:D,dropPosition:0})?O=0:P=!1:K===0?m>-1.5?i({dragNode:_,dropNode:D,dropPosition:1})?O=1:P=!1:i({dragNode:_,dropNode:D,dropPosition:0})?O=0:i({dragNode:_,dropNode:D,dropPosition:1})?O=1:P=!1:i({dragNode:_,dropNode:D,dropPosition:1})?O=1:P=!1,{dropPosition:O,dropLevelOffset:K,dropTargetKey:C.key,dropTargetPos:C.pos,dragOverNodeKey:R,dropContainerKey:O===0?null:((d=C.parent)===null||d===void 0?void 0:d.key)||null,dropAllowed:P}}function Bn(t,r){if(t){var n=r.multiple;return n?t.slice():t.length?[t[0]]:t}}function Vt(t){if(!t)return null;var r;if(Array.isArray(t))r={checkedKeys:t,halfCheckedKeys:void 0};else if(we(t)==="object")r={checkedKeys:t.checked||void 0,halfCheckedKeys:t.halfChecked||void 0};else return Re(!1,"`checkedKeys` is not an array or an object"),null;return r}function zn(t,r){var n=new Set;function e(o){if(!n.has(o)){var i=Ce(r,o);if(i){n.add(o);var s=i.parent,a=i.node;a.disabled||s&&e(s.key)}}}return(t||[]).forEach(function(o){e(o)}),ze(n)}function br(t,r){var n=new Set;return t.forEach(function(e){r.has(e)||n.add(e)}),n}function Xa(t){var r=t||{},n=r.disabled,e=r.disableCheckbox,o=r.checkable;return!!(n||e)||o===!1}function Qa(t,r,n,e){for(var o=new Set(t),i=new Set,s=0;s<=n;s+=1){var a=r.get(s)||new Set;a.forEach(function(u){var v=u.key,f=u.node,g=u.children,h=g===void 0?[]:g;o.has(v)&&!e(f)&&h.filter(function(y){return!e(y.node)}).forEach(function(y){o.add(y.key)})})}for(var l=new Set,c=n;c>=0;c-=1){var d=r.get(c)||new Set;d.forEach(function(u){var v=u.parent,f=u.node;if(!(e(f)||!u.parent||l.has(u.parent.key))){if(e(u.parent.node)){l.add(v.key);return}var g=!0,h=!1;(v.children||[]).filter(function(y){return!e(y.node)}).forEach(function(y){var m=y.key,C=o.has(m);g&&!C&&(g=!1),!h&&(C||i.has(m))&&(h=!0)}),g&&o.add(v.key),h&&i.add(v.key),l.add(v.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(br(i,o))}}function Ja(t,r,n,e,o){for(var i=new Set(t),s=new Set(r),a=0;a<=e;a+=1){var l=n.get(a)||new Set;l.forEach(function(v){var f=v.key,g=v.node,h=v.children,y=h===void 0?[]:h;!i.has(f)&&!s.has(f)&&!o(g)&&y.filter(function(m){return!o(m.node)}).forEach(function(m){i.delete(m.key)})})}s=new Set;for(var c=new Set,d=e;d>=0;d-=1){var u=n.get(d)||new Set;u.forEach(function(v){var f=v.parent,g=v.node;if(!(o(g)||!v.parent||c.has(v.parent.key))){if(o(v.parent.node)){c.add(f.key);return}var h=!0,y=!1;(f.children||[]).filter(function(m){return!o(m.node)}).forEach(function(m){var C=m.key,S=i.has(C);h&&!S&&(h=!1),!y&&(S||s.has(C))&&(y=!0)}),h||i.delete(f.key),y&&s.add(f.key),c.add(f.key)}})}return{checkedKeys:Array.from(i),halfCheckedKeys:Array.from(br(s,i))}}function $t(t,r,n,e){var o=[],i;i=Xa;var s=new Set(t.filter(function(d){var u=!!Ce(n,d);return u||o.push(d),u})),a=new Map,l=0;Object.keys(n).forEach(function(d){var u=n[d],v=u.level,f=a.get(v);f||(f=new Set,a.set(v,f)),f.add(u),l=Math.max(l,v)}),Re(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(d){return"'".concat(d,"'")}).join(", ")));var c;return r===!0?c=Qa(s,a,l,i):c=Ja(s,r.halfCheckedKeys,a,l,i),c}var Za=10,nn=function(t){rt(n,t);var r=ot(n);function n(){var e;qe(this,n);for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return e=r.call.apply(r,[this].concat(i)),e.destroyed=!1,e.delayedDragEnterLogic=void 0,e.loadingRetryTimes={},e.state={keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:ht()},e.dragStartMousePosition=null,e.dragNode=void 0,e.currentMouseOverDroppableNodeKey=null,e.listRef=p.createRef(),e.onNodeDragStart=function(a,l){var c=e.state,d=c.expandedKeys,u=c.keyEntities,v=e.props.onDragStart,f=l.props.eventKey;e.dragNode=l,e.dragStartMousePosition={x:a.clientX,y:a.clientY};var g=Me(d,f);e.setState({draggingNodeKey:f,dragChildrenKeys:Ga(f,u),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(g),window.addEventListener("dragend",e.onWindowDragEnd),v==null||v({event:a,node:oe(l.props)})},e.onNodeDragEnter=function(a,l){var c=e.state,d=c.expandedKeys,u=c.keyEntities,v=c.dragChildrenKeys,f=c.flattenNodes,g=c.indent,h=e.props,y=h.onDragEnter,m=h.onExpand,C=h.allowDrop,S=h.direction,E=l.props,b=E.pos,k=E.eventKey,x=Ke(e),R=x.dragNode;if(e.currentMouseOverDroppableNodeKey!==k&&(e.currentMouseOverDroppableNodeKey=k),!R){e.resetDragState();return}var O=$n(a,R,l,g,e.dragStartMousePosition,C,f,u,d,S),K=O.dropPosition,N=O.dropLevelOffset,_=O.dropTargetKey,D=O.dropContainerKey,P=O.dropTargetPos,I=O.dropAllowed,F=O.dragOverNodeKey;if(v.indexOf(_)!==-1||!I){e.resetDragState();return}if(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach(function(j){clearTimeout(e.delayedDragEnterLogic[j])}),R.props.eventKey!==l.props.eventKey&&(a.persist(),e.delayedDragEnterLogic[b]=window.setTimeout(function(){if(e.state.draggingNodeKey!==null){var j=ze(d),z=Ce(u,l.props.eventKey);z&&(z.children||[]).length&&(j=Ie(d,l.props.eventKey)),"expandedKeys"in e.props||e.setExpandedKeys(j),m==null||m(j,{node:oe(l.props),expanded:!0,nativeEvent:a.nativeEvent})}},800)),R.props.eventKey===_&&N===0){e.resetDragState();return}e.setState({dragOverNodeKey:F,dropPosition:K,dropLevelOffset:N,dropTargetKey:_,dropContainerKey:D,dropTargetPos:P,dropAllowed:I}),y==null||y({event:a,node:oe(l.props),expandedKeys:d})},e.onNodeDragOver=function(a,l){var c=e.state,d=c.dragChildrenKeys,u=c.flattenNodes,v=c.keyEntities,f=c.expandedKeys,g=c.indent,h=e.props,y=h.onDragOver,m=h.allowDrop,C=h.direction,S=Ke(e),E=S.dragNode;if(E){var b=$n(a,E,l,g,e.dragStartMousePosition,m,u,v,f,C),k=b.dropPosition,x=b.dropLevelOffset,R=b.dropTargetKey,O=b.dropContainerKey,K=b.dropAllowed,N=b.dropTargetPos,_=b.dragOverNodeKey;d.indexOf(R)!==-1||!K||(E.props.eventKey===R&&x===0?e.state.dropPosition===null&&e.state.dropLevelOffset===null&&e.state.dropTargetKey===null&&e.state.dropContainerKey===null&&e.state.dropTargetPos===null&&e.state.dropAllowed===!1&&e.state.dragOverNodeKey===null||e.resetDragState():k===e.state.dropPosition&&x===e.state.dropLevelOffset&&R===e.state.dropTargetKey&&O===e.state.dropContainerKey&&N===e.state.dropTargetPos&&K===e.state.dropAllowed&&_===e.state.dragOverNodeKey||e.setState({dropPosition:k,dropLevelOffset:x,dropTargetKey:R,dropContainerKey:O,dropTargetPos:N,dropAllowed:K,dragOverNodeKey:_}),y==null||y({event:a,node:oe(l.props)}))}},e.onNodeDragLeave=function(a,l){e.currentMouseOverDroppableNodeKey===l.props.eventKey&&!a.currentTarget.contains(a.relatedTarget)&&(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var c=e.props.onDragLeave;c==null||c({event:a,node:oe(l.props)})},e.onWindowDragEnd=function(a){e.onNodeDragEnd(a,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)},e.onNodeDragEnd=function(a,l){var c=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),c==null||c({event:a,node:oe(l.props)}),e.dragNode=null,window.removeEventListener("dragend",e.onWindowDragEnd)},e.onNodeDrop=function(a,l){var c,d=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=e.state,v=u.dragChildrenKeys,f=u.dropPosition,g=u.dropTargetKey,h=u.dropTargetPos,y=u.dropAllowed;if(y){var m=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),g!==null){var C=w(w({},tt(g,e.getTreeNodeRequiredProps())),{},{active:((c=e.getActiveItem())===null||c===void 0?void 0:c.key)===g,data:Ce(e.state.keyEntities,g).node}),S=v.indexOf(g)!==-1;Re(!S,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var E=tn(h),b={event:a,node:oe(C),dragNode:e.dragNode?oe(e.dragNode.props):null,dragNodesKeys:[e.dragNode.props.eventKey].concat(v),dropToGap:f!==0,dropPosition:f+Number(E[E.length-1])};d||m==null||m(b),e.dragNode=null}}},e.cleanDragState=function(){var a=e.state.draggingNodeKey;a!==null&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null},e.triggerExpandActionExpand=function(a,l){var c=e.state,d=c.expandedKeys,u=c.flattenNodes,v=l.expanded,f=l.key,g=l.isLeaf;if(!(g||a.shiftKey||a.metaKey||a.ctrlKey)){var h=u.filter(function(m){return m.key===f})[0],y=oe(w(w({},tt(f,e.getTreeNodeRequiredProps())),{},{data:h.data}));e.setExpandedKeys(v?Me(d,f):Ie(d,f)),e.onNodeExpand(a,y)}},e.onNodeClick=function(a,l){var c=e.props,d=c.onClick,u=c.expandAction;u==="click"&&e.triggerExpandActionExpand(a,l),d==null||d(a,l)},e.onNodeDoubleClick=function(a,l){var c=e.props,d=c.onDoubleClick,u=c.expandAction;u==="doubleClick"&&e.triggerExpandActionExpand(a,l),d==null||d(a,l)},e.onNodeSelect=function(a,l){var c=e.state.selectedKeys,d=e.state,u=d.keyEntities,v=d.fieldNames,f=e.props,g=f.onSelect,h=f.multiple,y=l.selected,m=l[v.key],C=!y;C?h?c=Ie(c,m):c=[m]:c=Me(c,m);var S=c.map(function(E){var b=Ce(u,E);return b?b.node:null}).filter(function(E){return E});e.setUncontrolledState({selectedKeys:c}),g==null||g(c,{event:"select",selected:C,node:l,selectedNodes:S,nativeEvent:a.nativeEvent})},e.onNodeCheck=function(a,l,c){var d=e.state,u=d.keyEntities,v=d.checkedKeys,f=d.halfCheckedKeys,g=e.props,h=g.checkStrictly,y=g.onCheck,m=l.key,C,S={event:"check",node:l,checked:c,nativeEvent:a.nativeEvent};if(h){var E=c?Ie(v,m):Me(v,m),b=Me(f,m);C={checked:E,halfChecked:b},S.checkedNodes=E.map(function(N){return Ce(u,N)}).filter(function(N){return N}).map(function(N){return N.node}),e.setUncontrolledState({checkedKeys:E})}else{var k=$t([].concat(ze(v),[m]),!0,u),x=k.checkedKeys,R=k.halfCheckedKeys;if(!c){var O=new Set(x);O.delete(m);var K=$t(Array.from(O),{checked:!1,halfCheckedKeys:R},u);x=K.checkedKeys,R=K.halfCheckedKeys}C=x,S.checkedNodes=[],S.checkedNodesPositions=[],S.halfCheckedKeys=R,x.forEach(function(N){var _=Ce(u,N);if(_){var D=_.node,P=_.pos;S.checkedNodes.push(D),S.checkedNodesPositions.push({node:D,pos:P})}}),e.setUncontrolledState({checkedKeys:x},!1,{halfCheckedKeys:R})}y==null||y(C,S)},e.onNodeLoad=function(a){var l=a.key,c=new Promise(function(d,u){e.setState(function(v){var f=v.loadedKeys,g=f===void 0?[]:f,h=v.loadingKeys,y=h===void 0?[]:h,m=e.props,C=m.loadData,S=m.onLoad;if(!C||g.indexOf(l)!==-1||y.indexOf(l)!==-1)return null;var E=C(a);return E.then(function(){var b=e.state.loadedKeys,k=Ie(b,l);S==null||S(k,{event:"load",node:a}),e.setUncontrolledState({loadedKeys:k}),e.setState(function(x){return{loadingKeys:Me(x.loadingKeys,l)}}),d()}).catch(function(b){if(e.setState(function(x){return{loadingKeys:Me(x.loadingKeys,l)}}),e.loadingRetryTimes[l]=(e.loadingRetryTimes[l]||0)+1,e.loadingRetryTimes[l]>=Za){var k=e.state.loadedKeys;Re(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:Ie(k,l)}),d()}u(b)}),{loadingKeys:Ie(y,l)}})});return c.catch(function(){}),c},e.onNodeMouseEnter=function(a,l){var c=e.props.onMouseEnter;c==null||c({event:a,node:l})},e.onNodeMouseLeave=function(a,l){var c=e.props.onMouseLeave;c==null||c({event:a,node:l})},e.onNodeContextMenu=function(a,l){var c=e.props.onRightClick;c&&(a.preventDefault(),c({event:a,node:l}))},e.onFocus=function(){var a=e.props.onFocus;e.setState({focused:!0});for(var l=arguments.length,c=new Array(l),d=0;d<l;d++)c[d]=arguments[d];a==null||a.apply(void 0,c)},e.onBlur=function(){var a=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var l=arguments.length,c=new Array(l),d=0;d<l;d++)c[d]=arguments[d];a==null||a.apply(void 0,c)},e.getTreeNodeRequiredProps=function(){var a=e.state,l=a.expandedKeys,c=a.selectedKeys,d=a.loadedKeys,u=a.loadingKeys,v=a.checkedKeys,f=a.halfCheckedKeys,g=a.dragOverNodeKey,h=a.dropPosition,y=a.keyEntities;return{expandedKeys:l||[],selectedKeys:c||[],loadedKeys:d||[],loadingKeys:u||[],checkedKeys:v||[],halfCheckedKeys:f||[],dragOverNodeKey:g,dropPosition:h,keyEntities:y}},e.setExpandedKeys=function(a){var l=e.state,c=l.treeData,d=l.fieldNames,u=Ft(c,a,d);e.setUncontrolledState({expandedKeys:a,flattenNodes:u},!0)},e.onNodeExpand=function(a,l){var c=e.state.expandedKeys,d=e.state,u=d.listChanging,v=d.fieldNames,f=e.props,g=f.onExpand,h=f.loadData,y=l.expanded,m=l[v.key];if(!u){var C=c.indexOf(m),S=!y;if(Re(y&&C!==-1||!y&&C===-1,"Expand state not sync with index check"),S?c=Ie(c,m):c=Me(c,m),e.setExpandedKeys(c),g==null||g(c,{node:l,expanded:S,nativeEvent:a.nativeEvent}),S&&h){var E=e.onNodeLoad(l);E&&E.then(function(){var b=Ft(e.state.treeData,c,v);e.setUncontrolledState({flattenNodes:b})}).catch(function(){var b=e.state.expandedKeys,k=Me(b,m);e.setExpandedKeys(k)})}}},e.onListChangeStart=function(){e.setUncontrolledState({listChanging:!0})},e.onListChangeEnd=function(){setTimeout(function(){e.setUncontrolledState({listChanging:!1})})},e.onActiveChange=function(a){var l=e.state.activeKey,c=e.props,d=c.onActiveChange,u=c.itemScrollOffset,v=u===void 0?0:u;l!==a&&(e.setState({activeKey:a}),a!==null&&e.scrollTo({key:a,offset:v}),d==null||d(a))},e.getActiveItem=function(){var a=e.state,l=a.activeKey,c=a.flattenNodes;return l===null?null:c.find(function(d){var u=d.key;return u===l})||null},e.offsetActiveKey=function(a){var l=e.state,c=l.flattenNodes,d=l.activeKey,u=c.findIndex(function(g){var h=g.key;return h===d});u===-1&&a<0&&(u=c.length),u=(u+a+c.length)%c.length;var v=c[u];if(v){var f=v.key;e.onActiveChange(f)}else e.onActiveChange(null)},e.onKeyDown=function(a){var l=e.state,c=l.activeKey,d=l.expandedKeys,u=l.checkedKeys,v=l.fieldNames,f=e.props,g=f.onKeyDown,h=f.checkable,y=f.selectable;switch(a.which){case We.UP:{e.offsetActiveKey(-1),a.preventDefault();break}case We.DOWN:{e.offsetActiveKey(1),a.preventDefault();break}}var m=e.getActiveItem();if(m&&m.data){var C=e.getTreeNodeRequiredProps(),S=m.data.isLeaf===!1||!!(m.data[v.children]||[]).length,E=oe(w(w({},tt(c,C)),{},{data:m.data,active:!0}));switch(a.which){case We.LEFT:{S&&d.includes(c)?e.onNodeExpand({},E):m.parent&&e.onActiveChange(m.parent.key),a.preventDefault();break}case We.RIGHT:{S&&!d.includes(c)?e.onNodeExpand({},E):m.children&&m.children.length&&e.onActiveChange(m.children[0].key),a.preventDefault();break}case We.ENTER:case We.SPACE:{h&&!E.disabled&&E.checkable!==!1&&!E.disableCheckbox?e.onNodeCheck({},E,!u.includes(c)):!h&&y&&!E.disabled&&E.selectable!==!1&&e.onNodeSelect({},E);break}}}g==null||g(a)},e.setUncontrolledState=function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!e.destroyed){var d=!1,u=!0,v={};Object.keys(a).forEach(function(f){if(f in e.props){u=!1;return}d=!0,v[f]=a[f]}),d&&(!l||u)&&e.setState(w(w({},v),c))}},e.scrollTo=function(a){e.listRef.current.scrollTo(a)},e}return Xe(n,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,i=o.activeKey,s=o.itemScrollOffset,a=s===void 0?0:s;i!==void 0&&i!==this.state.activeKey&&(this.setState({activeKey:i}),i!==null&&this.scrollTo({key:i,offset:a}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o,i=this.state,s=i.focused,a=i.flattenNodes,l=i.keyEntities,c=i.draggingNodeKey,d=i.activeKey,u=i.dropLevelOffset,v=i.dropContainerKey,f=i.dropTargetKey,g=i.dropPosition,h=i.dragOverNodeKey,y=i.indent,m=this.props,C=m.prefixCls,S=m.className,E=m.style,b=m.showLine,k=m.focusable,x=m.tabIndex,R=x===void 0?0:x,O=m.selectable,K=m.showIcon,N=m.icon,_=m.switcherIcon,D=m.draggable,P=m.checkable,I=m.checkStrictly,F=m.disabled,j=m.motion,z=m.loadData,te=m.filterTreeNode,ne=m.height,Q=m.itemHeight,ee=m.virtual,J=m.titleRender,Se=m.dropIndicatorRender,pe=m.onContextMenu,Ee=m.onScroll,he=m.direction,ke=m.rootClassName,se=m.rootStyle,Y=Wn(this.props,{aria:!0,data:!0}),G;return D&&(we(D)==="object"?G=D:typeof D=="function"?G={nodeDraggable:D}:G={}),p.createElement(Xt.Provider,{value:{prefixCls:C,selectable:O,showIcon:K,icon:N,switcherIcon:_,draggable:G,draggingNodeKey:c,checkable:P,checkStrictly:I,disabled:F,keyEntities:l,dropLevelOffset:u,dropContainerKey:v,dropTargetKey:f,dropPosition:g,dragOverNodeKey:h,indent:y,direction:he,dropIndicatorRender:Se,loadData:z,filterTreeNode:te,titleRender:J,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop}},p.createElement("div",{role:"tree",className:fe(C,S,ke,(o={},M(o,"".concat(C,"-show-line"),b),M(o,"".concat(C,"-focused"),s),M(o,"".concat(C,"-active-focused"),d!==null),o)),style:se},p.createElement(Er,ge({ref:this.listRef,prefixCls:C,style:E,data:a,disabled:F,selectable:O,checkable:!!P,motion:j,dragging:c!==null,height:ne,itemHeight:Q,virtual:ee,focusable:k,focused:s,tabIndex:R,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:pe,onScroll:Ee},this.getTreeNodeRequiredProps(),Y))))}}],[{key:"getDerivedStateFromProps",value:function(o,i){var s=i.prevProps,a={prevProps:o};function l(k){return!s&&k in o||s&&s[k]!==o[k]}var c,d=i.fieldNames;if(l("fieldNames")&&(d=ht(o.fieldNames),a.fieldNames=d),l("treeData")?c=o.treeData:l("children")&&(Re(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),c=La(o.children)),c){a.treeData=c;var u=Aa(c,{fieldNames:d});a.keyEntities=w(M({},je,Sr),u.keyEntities)}var v=a.keyEntities||i.keyEntities;if(l("expandedKeys")||s&&l("autoExpandParent"))a.expandedKeys=o.autoExpandParent||!s&&o.defaultExpandParent?zn(o.expandedKeys,v):o.expandedKeys;else if(!s&&o.defaultExpandAll){var f=w({},v);delete f[je],a.expandedKeys=Object.keys(f).map(function(k){return f[k].key})}else!s&&o.defaultExpandedKeys&&(a.expandedKeys=o.autoExpandParent||o.defaultExpandParent?zn(o.defaultExpandedKeys,v):o.defaultExpandedKeys);if(a.expandedKeys||delete a.expandedKeys,c||a.expandedKeys){var g=Ft(c||i.treeData,a.expandedKeys||i.expandedKeys,d);a.flattenNodes=g}if(o.selectable&&(l("selectedKeys")?a.selectedKeys=Bn(o.selectedKeys,o):!s&&o.defaultSelectedKeys&&(a.selectedKeys=Bn(o.defaultSelectedKeys,o))),o.checkable){var h;if(l("checkedKeys")?h=Vt(o.checkedKeys)||{}:!s&&o.defaultCheckedKeys?h=Vt(o.defaultCheckedKeys)||{}:c&&(h=Vt(o.checkedKeys)||{checkedKeys:i.checkedKeys,halfCheckedKeys:i.halfCheckedKeys}),h){var y=h,m=y.checkedKeys,C=m===void 0?[]:m,S=y.halfCheckedKeys,E=S===void 0?[]:S;if(!o.checkStrictly){var b=$t(C,!0,v);C=b.checkedKeys,E=b.halfCheckedKeys}a.checkedKeys=C,a.halfCheckedKeys=E}}return l("loadedKeys")&&(a.loadedKeys=o.loadedKeys),a}}]),n}(p.Component);nn.defaultProps={prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:po,allowDrop:function(){return!0},expandAction:!1};nn.TreeNode=nt;const si=({selectedCategory:t,setSelectedCategory:r,setDefaultCategory:n})=>{var v;const{data:e,loading:o}=zr((v=jr)==null?void 0:v.getAllCategory),{showingTranslateValue:i}=Wr(),s=`
  .rc-tree-child-tree {
    display: block;
  }
  .node-motion {
    transition: all .3s;
    overflow-y: hidden;
  }
`,a={motionName:"node-motion",motionAppear:!1,onAppearStart:f=>({height:0}),onAppearActive:f=>({height:f.scrollHeight}),onLeaveStart:f=>({height:f.offsetHeight}),onLeaveActive:()=>({height:0})},l=f=>{var h;let g=[];for(let y of f)g.push({title:i(y.name),key:y._id,children:((h=y==null?void 0:y.children)==null?void 0:h.length)>0&&l(y.children)});return g},c=(f,g)=>{var h;return f._id===g?f:(h=f==null?void 0:f.children)==null?void 0:h.reduce((y,m)=>y??c(m,g),void 0)},d=f=>{const g=e[0],h=c(g,f);if(h!==void 0){if(t.filter(m=>m._id===h._id).length!==0)return Hr("This category already selected!");r(m=>[...m,{_id:h==null?void 0:h._id,name:i(h==null?void 0:h.name)}]),n(()=>[{_id:h==null?void 0:h._id,name:i(h==null?void 0:h.name)}])}},u=f=>{r(f)};return $e.jsxs($e.Fragment,{children:[$e.jsx("div",{className:"mb-2",children:$e.jsx(no,{displayValue:"name",groupBy:"name",isObject:!0,hidePlaceholder:!0,onKeyPressFn:function(){},onRemove:f=>u(f),onSearch:function(){},onSelect:f=>d(f),selectedValues:t,placeholder:"Select Category"})}),!o&&e!==void 0&&$e.jsxs("div",{className:"draggable-demo capitalize",children:[$e.jsx("style",{dangerouslySetInnerHTML:{__html:s}}),$e.jsx(nn,{expandAction:"click",treeData:l(e),onSelect:f=>d(f[0]),motion:a,animation:"slide-up"})]})]})};export{no as M,si as P,ii as R,nn as T};
