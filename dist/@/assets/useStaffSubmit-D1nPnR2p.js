import{d as w}from"./Layout-f_j_aP34.js";import{r as s,A as H,S as J,u as q,h as M}from"./index-DD5OQCzb.js";import{u as z}from"./index.esm-BPZGYcl8.js";import{A as y}from"./AdminServices-Crgje1Fu.js";import{n as U,a as _}from"./toast-C_V_NPJL.js";import{u as K}from"./useTranslationValue-d_-eYXcs.js";const te=c=>{const{state:C,dispatch:L}=s.useContext(H),{adminInfo:r}=C,{isDrawerOpen:Y,closeDrawer:h,setIsUpdate:b,lang:k}=s.useContext(J),[j,f]=s.useState(""),[D,x]=s.useState(w(new Date).format("YYYY-MM-DD")),[o,O]=s.useState("en"),[n,T]=s.useState({}),[v,p]=s.useState(!1),[g,A]=s.useState([]),E=q(),{handlerTextTranslateHandler:N}=K(),{register:R,handleSubmit:V,setValue:t,clearErrors:i,formState:{errors:B}}=z(),F=async a=>{var l,u;try{p(!0);const S={name:{...await N(a.name,o,n==null?void 0:n.name),[o]:a.name},email:a.email,password:a.password,phone:a.phone,role:a.role,access_list:g==null?void 0:g.map(m=>m.value),joiningDate:D||w(new Date).format("YYYY-MM-DD"),image:j,lang:o},d=(r==null?void 0:r._id)===(n==null?void 0:n._id);if(c){const m=await y.updateStaff(c,S);d&&(L({type:"USER_LOGIN",payload:m}),M.set("adminInfo",JSON.stringify(m),{expires:.5,sameSite:"None",secure:!0})),b(!0),p(!1),U("Staff Updated Successfully!"),h()}else{const m=await y.addStaff(S);b(!0),p(!1),U(m.message),h()}}catch(e){_(e?(u=(l=e==null?void 0:e.response)==null?void 0:l.data)==null?void 0:u.message:e==null?void 0:e.message),p(!1),h()}},I=async()=>{var a,l,u;try{const e=await y.getStaffById(c,{email:r.email});if(e){T(e),t("name",e.name[o||"en"]),t("email",e.email),t("password"),t("phone",e.phone),t("role",e.role),x(w(e.joiningData).format("YYYY-MM-DD")),f(e.image);const S=(a=e==null?void 0:e.access_list)==null?void 0:a.map(d=>({label:d,value:d}));A(S)}}catch(e){_(e?(u=(l=e==null?void 0:e.response)==null?void 0:l.data)==null?void 0:u.message:e==null?void 0:e.message)}},G=a=>{O(a),Object.keys(n).length>0&&t("name",n.name[a||"en"])};return s.useEffect(()=>{if(!Y){T({}),t("name"),t("email"),t("password"),t("phone"),t("role"),t("joiningDate"),f(""),i("name"),i("email"),i("password"),i("phone"),i("role"),i("joiningDate"),f(""),O(k),t("language",o);return}c&&I()},[c,t,Y,r.email,i]),s.useEffect(()=>{E.pathname==="/edit-profile"&&M.get("adminInfo")&&I()},[E.pathname,t]),{register:R,handleSubmit:V,onSubmit:F,language:o,errors:B,adminInfo:r,setImageUrl:f,imageUrl:j,selectedDate:D,setSelectedDate:x,isSubmitting:v,accessedRoutes:g,setAccessedRoutes:A,handleSelectLanguage:G}};export{te as u};
