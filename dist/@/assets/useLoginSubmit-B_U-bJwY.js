import{r as u,A as x,q as A,u as L,i as f}from"./index-DpMxJ5Hx.js";import{u as N}from"./index.esm-B8kiXavo.js";import{A as t}from"./AdminServices-CIs7colP.js";import{n as o,a as b}from"./toast-Be5Wd3gm.js";const C=()=>{const[p,a]=u.useState(!1),{dispatch:i}=u.useContext(x),r=A(),s=L(),{register:g,handleSubmit:d,formState:{errors:S}}=N();return{onSubmit:async({name:y,email:n,verifyEmail:l,password:c,role:h})=>{a(!0);const m=.5;try{if(s.pathname==="/login"){const e=await t.loginAdmin({email:n,password:c});e&&(o("Login Success!"),i({type:"USER_LOGIN",payload:e}),f.set("adminInfo",JSON.stringify(e),{expires:m,sameSite:"None",secure:!0}),r.replace("/dashboard"))}if(s.pathname==="/signup"){const e=await t.registerAdmin({name:y,email:n,password:c,role:h});e&&(o("Register Success!"),i({type:"USER_LOGIN",payload:e}),f.set("adminInfo",JSON.stringify(e),{expires:m,sameSite:"None",secure:!0}),r.replace("/"))}if(s.pathname==="/forgot-password"){const e=await t.forgetPassword({verifyEmail:l});o(e.message)}}catch(e){b(e?.response?.data?.message||e?.message)}finally{a(!1)}},register:g,handleSubmit:d,errors:S,loading:p}};export{C as u};
