import{r as j,S as Y,j as e,t as o,f as l,i as Q}from"./index-DD5OQCzb.js";import{u as $,d as R,l as X,e as ee,f as se,g as le}from"./Layout-f_j_aP34.js";import{C as U}from"./CouponServices-BvJiM6D0.js";import{u as ae}from"./useAsync-CdFiuEZy.js";import{T as L,D as te,u as W,M as Z}from"./DrawerButton-C1kY46U5.js";import{u as re}from"./useFilter-BHZ5O6jw.js";import{P as ie}from"./PageTitle-DUWCiaui.js";import{D as G,E as ne}from"./EditDeleteButton-B2PjzFtp.js";import{C as J,S as de,B as oe}from"./BulkActionDrawer-BEXxVAnh.js";import{u as me,E as F}from"./index.esm-BPZGYcl8.js";import{I as O}from"./InputArea-Cu6xCoGw.js";import{I as H}from"./InputValue-CWXhc3PW.js";import{L as D}from"./LabelArea-Zz4acQmF.js";import{U as ce}from"./Uploader-Bz_6zC0v.js";import{a as q,n as z}from"./toast-C_V_NPJL.js";import{u as xe}from"./useTranslationValue-d_-eYXcs.js";import{S as ge}from"./SwitchToggle-CiShsGtJ.js";import{a as pe}from"./index.prod-CisttSXz.js";import{T as ue}from"./TableLoading-D3D_P-TQ.js";import{N as he}from"./NotFound-DG_8Itz7.js";import{U as je}from"./UploadMany-Bdp4IRJJ.js";import{A as be}from"./AnimatedContent-DbKaf3qr.js";import"./iconBase-DTk8F31e.js";import"./SelectLanguageTwo-CQlbeojL.js";import"./spinner-CkndCogW.js";import"./ProductServices-CGXRs0W4.js";import"./index-0EvDzr9j.js";import"./useDisableForDemo-aTnQzb5-.js";import"./CurrencyServices-CicNeQxs.js";import"./AdminServices-Crgje1Fu.js";import"./Tooltip-DrdTd94n.js";import"./ParentCategory-BL1vwhP5.js";import"./_commonjs-dynamic-modules-LM44EJN2.js";import"./exportFromJSON-fDIoOtpr.js";const fe=a=>{const{isDrawerOpen:r,closeDrawer:b,setIsUpdate:i,lang:d}=j.useContext(Y),[T,f]=j.useState(""),[g,C]=j.useState("en"),[m,p]=j.useState({}),[u,w]=j.useState(!1),[N,s]=j.useState(!1),[h,x]=j.useState(!1),{currency:v}=$(),{handlerTextTranslateHandler:A}=xe(),{register:B,handleSubmit:I,setValue:n,clearErrors:S,formState:{errors:M}}=me(),E=async c=>{var k,P;try{x(!0);const t={title:{...await A(c.title,g,m==null?void 0:m.title),[g]:title},couponCode:c.couponCode,endTime:c.endTime,minimumAmount:c.minimumAmount,logo:T,lang:g,status:u?"show":"hide",discountType:{type:N?"percentage":"fixed",value:c.discountPercentage},productType:c.productType};if(a){const _=await U.updateCoupon(a,t);i(!0),x(!1),z(_.message),b()}else{const _=await U.addCoupon(t);i(!0),x(!1),z(_.message),b()}}catch(y){q(((P=(k=y==null?void 0:y.response)==null?void 0:k.data)==null?void 0:P.message)||(y==null?void 0:y.message)),x(!1),b()}},V=c=>{C(c),Object.keys(m).length>0&&n("title",m.title[c||"en"])};return j.useEffect(()=>{if(!r){p({}),n("title"),n("productType"),n("couponCode"),n("endTime"),n("discountPercentage"),n("minimumAmount"),f(""),S("title"),S("productType"),S("couponCode"),S("endTime"),S("discountPercentage"),S("minimumAmount"),C(d),n("language",g);return}a&&(async()=>{var c,k,P,y;try{const t=await U.getCouponById(a);t&&(p(t),n("title",t.title[g||"en"]),n("productType",t.productType),n("couponCode",t.couponCode),n("endTime",R(t.endTime).format("YYYY-MM-DD HH:mm")),n("discountPercentage",(c=t.discountType)==null?void 0:c.value),n("minimumAmount",t.minimumAmount),w(t.status==="show"),s(((k=t.discountType)==null?void 0:k.type)==="percentage"),f(t.logo))}catch(t){q(((y=(P=t==null?void 0:t.response)==null?void 0:P.data)==null?void 0:y.message)||(t==null?void 0:t.message))}})()},[a,n,r,S,g,d]),{register:B,handleSubmit:I,onSubmit:E,errors:M,setImageUrl:f,imageUrl:T,published:u,setPublished:w,currency:v,discountType:N,isSubmitting:h,setDiscountType:s,handleSelectLanguage:V}},Ce=({title:a,handleProcess:r,processOption:b,product:i,handleIsCombination:d})=>e.jsx(e.Fragment,{children:e.jsx("div",{className:`${i?"mb-3 flex flex-wrap justify-end items-center mr-8":"mb-3"}`,children:e.jsxs("div",{className:"flex flex-wrap items-center",children:[i?e.jsx("label",{className:"block text-base font-normal text-orange-500 dark:text-orange-400 mx-4",children:"Does this product have variants?"}):e.jsx("label",{className:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1",children:a}),e.jsx(pe,{onChange:i?d:r,checked:b,className:"react-switch",uncheckedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:14,color:"white",paddingRight:45,paddingTop:1},children:"Fixed"}),width:125,height:33,handleDiameter:28,offColor:"#E53E3E",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:14,color:"white",paddingLeft:50,paddingTop:1},children:"Percentage"})})]})})}),K=({id:a})=>{const{register:r,handleSubmit:b,onSubmit:i,errors:d,setImageUrl:T,imageUrl:f,published:g,setPublished:C,currency:m,discountType:p,setDiscountType:u,isSubmitting:w,handleSelectLanguage:N}=fe(a);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative  p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 ",children:a?e.jsx(L,{register:r,handleSelectLanguage:N,title:o("UpdateCoupon"),description:o("UpdateCouponDescription")}):e.jsx(L,{register:r,handleSelectLanguage:N,title:o("AddCoupon"),description:o("AddCouponDescription")})}),e.jsx(X.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:b(i),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("CouponBannerImage")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(ce,{imageUrl:f,setImageUrl:T,folder:"coupon",targetWidth:238,targetHeight:238})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("CampaignName")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(O,{required:!0,register:r,label:"Coupon title",name:"title",type:"text",placeholder:o("CampaignName")}),e.jsx(F,{errorName:d.title})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("CampaignCode")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(O,{required:!0,register:r,label:"Coupon Code",name:"couponCode",type:"text",placeholder:o("CampaignCode")}),e.jsx(F,{errorName:d.couponCode})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("CouponValidityTime")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(l.Input,{...r("endTime",{required:"Coupon Validation End Time"}),label:"Coupon Validation End Time",name:"endTime",type:"datetime-local",placeholder:o("CouponValidityTime")}),e.jsx(F,{errorName:d.endTime})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("DiscountType")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(Ce,{handleProcess:u,processOption:p}),e.jsx(F,{errorName:d.discountType})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("Discount")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(H,{product:!0,required:!0,register:r,maxValue:p?99:1e3,minValue:1,label:"Discount",name:"discountPercentage",type:"number",placeholder:p?"Percentage":"Fixed Amount",currency:p?"%":m}),e.jsx(F,{errorName:d.discountPercentage})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("MinimumAmount")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(H,{product:!0,required:!0,register:r,maxValue:2e5,minValue:100,label:"Minimum Amount",name:"minimumAmount",type:"number",placeholder:o("MinimumAmountPlasholder"),currency:m}),e.jsx(F,{errorName:d.minimumAmount})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(D,{label:o("Published")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(ge,{handleProcess:C,processOption:g}),e.jsx(F,{errorName:d.productType})]})]})]}),e.jsx(te,{id:a,title:"Coupon",isSubmitting:w})]})})]})},ye=({isCheck:a,coupons:r,setIsCheck:b})=>{const[i,d]=j.useState([]),{title:T,serviceId:f,handleModalOpen:g,handleUpdate:C}=W(),{currency:m,showDateFormat:p,globalSetting:u,showingTranslateValue:w}=$(),N=s=>{const{id:h,checked:x}=s.target;b([...a,h]),x||b(a.filter(v=>v!==h))};return j.useEffect(()=>{const s=r==null?void 0:r.map(h=>{const x=new Date(h==null?void 0:h.updatedAt).toLocaleString("en-US",{timeZone:u==null?void 0:u.default_time_zone});return{...h,updatedDate:x}});d(s)},[r,u==null?void 0:u.default_time_zone]),e.jsxs(e.Fragment,{children:[a.length<1&&e.jsx(G,{id:f,title:T}),a.length<2&&e.jsx(Z,{children:e.jsx(K,{id:f})}),e.jsx(l.TableBody,{children:i==null?void 0:i.map((s,h)=>{var x,v,A,B,I;return e.jsxs(l.TableRow,{children:[e.jsx(l.TableCell,{children:e.jsx(J,{type:"checkbox",name:(x=s==null?void 0:s.title)==null?void 0:x.en,id:s._id,handleClick:N,isChecked:a==null?void 0:a.includes(s._id)})}),e.jsxs(l.TableCell,{children:[e.jsxs("div",{className:"flex items-center",children:[s!=null&&s.logo?e.jsx(l.Avatar,{className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none",src:s==null?void 0:s.logo,alt:"product"}):e.jsx(l.Avatar,{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm",children:w(s==null?void 0:s.title)})," "]})]})," "]}),e.jsxs(l.TableCell,{children:[" ",e.jsxs("span",{className:"text-sm",children:[" ",s.couponCode]})," "]}),(v=s==null?void 0:s.discountType)!=null&&v.type?e.jsxs(l.TableCell,{children:[" ",e.jsxs("span",{className:"text-sm font-semibold",children:[" ",((A=s==null?void 0:s.discountType)==null?void 0:A.type)==="percentage"?`${(B=s==null?void 0:s.discountType)==null?void 0:B.value}%`:`${m}${(I=s==null?void 0:s.discountType)==null?void 0:I.value}`]})," "]}):e.jsxs(l.TableCell,{children:[" ",e.jsx("span",{className:"text-sm font-semibold",children:" "})," "]}),e.jsx(l.TableCell,{className:"text-center",children:e.jsx(de,{id:s._id,status:s.status})}),e.jsx(l.TableCell,{children:e.jsx("span",{className:"text-sm",children:p(s.startTime)})}),e.jsx(l.TableCell,{children:e.jsx("span",{className:"text-sm",children:p(s.endTime)})}),e.jsx(l.TableCell,{className:"align-middle ",children:R().isAfter(R(s.endTime))?e.jsx(l.Badge,{type:"danger",children:"Expired"}):e.jsx(l.Badge,{type:"success",children:"Active"})}),e.jsx(l.TableCell,{children:e.jsx(ne,{id:s==null?void 0:s._id,isCheck:a,handleUpdate:C,handleModalOpen:g,title:w(s==null?void 0:s.title)})})]},h+1)})})]})},ls=()=>{const{t:a}=Q(),{toggleDrawer:r,lang:b}=j.useContext(Y),{data:i,loading:d,error:T}=ae(U.getAllCoupons),[f,g]=j.useState(!1),[C,m]=j.useState([]),{allId:p,serviceId:u,handleDeleteMany:w,handleUpdateMany:N}=W(),{filename:s,isDisabled:h,couponRef:x,dataTable:v,serviceData:A,totalResults:B,resultsPerPage:I,handleChangePage:n,handleSelectFile:S,setSearchCoupon:M,handleSubmitCoupon:E,handleUploadMultiple:V,handleRemoveSelectFile:c}=re(i),k=()=>{g(!f),m(i==null?void 0:i.map(y=>y._id)),f&&m([])},P=()=>{M(""),x.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(ie,{children:a("CouponspageTitle")}),e.jsx(G,{ids:p,setIsCheck:m,title:"Selected Coupon"}),e.jsx(oe,{ids:p,title:"Coupons"}),e.jsx(Z,{children:e.jsx(K,{id:u})}),e.jsxs(be,{children:[e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:E,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6  xl:flex",children:[e.jsx("div",{className:"flex justify-start xl:w-1/2  md:w-full",children:e.jsx(je,{title:"Coupon",exportData:i,filename:s,isDisabled:h,handleSelectFile:S,handleUploadMultiple:V,handleRemoveSelectFile:c})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:C.length<1,onClick:()=>N(C),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(ee,{})}),a("BulkAction")]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:C.length<1,onClick:()=>w(C),className:"w-full rounded-md h-12 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(se,{})}),a("Delete")]})}),e.jsx("div",{className:"w-full md:w-48 lg:w-48 xl:w-48",children:e.jsxs(l.Button,{onClick:r,className:"w-full rounded-md h-12",children:[e.jsx("span",{className:"mr-2",children:e.jsx(le,{})}),a("AddCouponsBtn")]})})]})]})})}),e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:E,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(l.Input,{ref:x,type:"search",placeholder:a("SearchCoupon")})}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{layout:"outline",onClick:P,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),d?e.jsx(ue,{row:12,col:8,width:140,height:20}):T?e.jsx("span",{className:"text-center mx-auto text-red-500",children:T}):(A==null?void 0:A.length)!==0?e.jsxs(l.TableContainer,{className:"mb-8",children:[e.jsxs(l.Table,{children:[e.jsx(l.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(l.TableCell,{children:e.jsx(J,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:k,isChecked:f})}),e.jsx(l.TableCell,{children:a("CoupTblCampaignsName")}),e.jsx(l.TableCell,{children:a("CoupTblCode")}),e.jsx(l.TableCell,{children:a("Discount")}),e.jsx(l.TableCell,{className:"text-center",children:a("catPublishedTbl")}),e.jsx(l.TableCell,{children:a("CoupTblStartDate")}),e.jsx(l.TableCell,{children:a("CoupTblEndDate")}),e.jsx(l.TableCell,{children:a("CoupTblStatus")}),e.jsx(l.TableCell,{className:"text-right",children:a("CoupTblActions")})]})}),e.jsx(ye,{lang:b,isCheck:C,coupons:v,setIsCheck:m})]}),e.jsx(l.TableFooter,{children:e.jsx(l.Pagination,{totalResults:B,resultsPerPage:I,onChange:n,label:"Table navigation"})})]}):e.jsx(he,{title:"Sorry, There are no coupons right now."})]})};export{ls as default};
