import{k as u,j as e,r as n,S as z,h as l}from"./index-DpMxJ5Hx.js";import{e as O,f as H,g as _}from"./Layout-B-UGxzbM.js";import{u as f}from"./useAsync-Hr4bbxCm.js";import{C as j}from"./ProductServices-CnM1m97m.js";import{u as $,M as q}from"./DrawerButton-CKK8nF3h.js";import{u as G}from"./useFilter-Qjmg-ZWR.js";import{D as J}from"./EditDeleteButton-DsdeAqDJ.js";import{B as K,C as Q}from"./BulkActionDrawer-ChuFcR4n.js";import{P as V}from"./PageTitle-D-hGib5s.js";import{C as W,a as X}from"./CategoryTable-DSjXF0RY.js";import{U as Y}from"./UploadMany-BRz6m-Uw.js";import{a as Z}from"./index.prod-BR0InCj9.js";import{T as ee}from"./TableLoading-C5i6hQGj.js";import{N as le}from"./NotFound-BXAjvYR4.js";import{A as se}from"./AnimatedContent-DVQRys_r.js";import"./iconBase-BUmmAlr8.js";import"./index-C148XJoK.js";import"./SelectLanguageTwo-CnZMFe5S.js";import"./spinner-CkndCogW.js";import"./useDisableForDemo-DczgqPm6.js";import"./toast-Be5Wd3gm.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";import"./AdminServices-CIs7colP.js";import"./Tooltip-BQ_BZ_s8.js";import"./ParentCategory-C2ZW3iQo.js";import"./index.esm-B8kiXavo.js";import"./LabelArea-CQP0v-a8.js";import"./SwitchToggle-CmuQM---.js";import"./InputArea-BGdt-vbi.js";import"./Uploader-sUWHaRyq.js";import"./_commonjs-dynamic-modules-CNspEXFA.js";import"./useTranslationValue-DM8I18Uu.js";import"./exportFromJSON-fDIoOtpr.js";const ae=({title:d,handleProcess:r,processOption:a})=>{const{t:i}=u();return e.jsx(e.Fragment,{children:e.jsx("div",{className:"mb-3",children:e.jsxs("div",{className:"flex flex-wrap items-center float-right",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1",children:d}),e.jsx(Z,{onChange:r,checked:a,className:"react-switch md:ml-0 ml-3",uncheckedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"left",alignItems:"center",height:"100%",fontSize:12,color:"white",paddingRight:50,paddingTop:1,marginLeft:-40,whiteSpace:"nowrap"},children:i("ParentsOnly")}),width:115,height:28,handleDiameter:26,offColor:"#0e9f6e",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:12,color:"white",paddingLeft:8,paddingTop:1},children:i("All")})})]})})})},Le=()=>{const{toggleDrawer:d,lang:r}=n.useContext(z),{data:a,loading:i,error:x}=f(j.getAllCategory),{data:w}=f(j.getAllCategories),{handleDeleteMany:C,allId:h,handleUpdateMany:b,serviceId:y}=$(),{t:s}=u(),{handleSubmitCategory:g,categoryRef:p,totalResults:N,resultsPerPage:T,dataTable:k,serviceData:v,handleChangePage:S,filename:A,isDisabled:D,setCategoryType:F,handleSelectFile:B,handleUploadMultiple:I,handleRemoveSelectFile:P}=G(a[0]?.children?a[0]?.children:a),[c,M]=n.useState(!1),[t,o]=n.useState([]),[m,R]=n.useState(!1),E=()=>{M(!c),o(a[0]?.children.map(L=>L._id)),c&&o([])},U=()=>{F(""),p.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(V,{children:s("Category")}),e.jsx(J,{ids:h,setIsCheck:o}),e.jsx(K,{ids:h,title:"Categories",lang:r,data:a,isCheck:t}),e.jsx(q,{children:e.jsx(W,{id:y,data:a,lang:r})}),e.jsxs(se,{children:[e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{className:"",children:e.jsxs("form",{onSubmit:g,className:"py-3  grid gap-4 lg:gap-6 xl:gap-6  xl:flex",children:[e.jsx("div",{className:"flex justify-start w-1/2 xl:w-1/2 md:w-full",children:e.jsx(Y,{title:"Categories",exportData:w,filename:A,isDisabled:D,handleSelectFile:B,handleUploadMultiple:I,handleRemoveSelectFile:P})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:t.length<1,onClick:()=>b(t),className:"w-full rounded-md h-12 text-gray-600 btn-gray",children:[e.jsx("span",{className:"mr-2",children:e.jsx(O,{})}),s("BulkAction")]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32  mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:t.length<1,onClick:()=>C(t),className:"w-full rounded-md h-12 bg-red-500 disabled  btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(H,{})}),s("Delete")]})}),e.jsx("div",{className:"w-full md:w-48 lg:w-48 xl:w-48",children:e.jsxs(l.Button,{onClick:d,className:"rounded-md h-12 w-full",children:[e.jsx("span",{className:"mr-2",children:e.jsx(_,{})}),s("AddCategory")]})})]})]})})}),e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 rounded-t-lg rounded-0 mb-4",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:g,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(l.Input,{ref:p,type:"search",placeholder:s("SearchCategory")})}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{layout:"outline",onClick:U,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),e.jsx(ae,{title:" ",handleProcess:R,processOption:m,name:m}),i?e.jsx(ee,{row:12,col:6,width:190,height:20}):x?e.jsx("span",{className:"text-center mx-auto text-red-500",children:x}):v?.length!==0?e.jsxs(l.TableContainer,{className:"mb-8",children:[e.jsxs(l.Table,{children:[e.jsx(l.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(l.TableCell,{children:e.jsx(Q,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:E,isChecked:c})}),e.jsx(l.TableCell,{children:s("catIdTbl")}),e.jsx(l.TableCell,{children:s("catIconTbl")}),e.jsx(l.TableCell,{children:s("CatTbName")}),e.jsx(l.TableCell,{children:s("CatTbDescription")}),e.jsx(l.TableCell,{className:"text-center",children:s("catPublishedTbl")}),e.jsx(l.TableCell,{className:"text-right",children:s("catActionsTbl")})]})}),e.jsx(X,{data:a,lang:r,isCheck:t,categories:k,setIsCheck:o,showChild:m})]}),e.jsx(l.TableFooter,{children:e.jsx(l.Pagination,{totalResults:N,resultsPerPage:T,onChange:S,label:"Table navigation"})})]}):e.jsx(le,{title:"Sorry, There are no categories right now."})]})};export{Le as default};
