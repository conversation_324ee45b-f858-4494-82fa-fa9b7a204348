import{k as h,j as e,h as t,r as o,l as b}from"./index-DpMxJ5Hx.js";import{d as p,u as j,p as N,i as f}from"./Layout-B-UGxzbM.js";import{S as u}from"./Status-BjgMFBDv.js";import{T as y}from"./Tooltip-BQ_BZ_s8.js";import{l as g}from"./index-BYGsSwV0.js";import{a as T}from"./toast-Be5Wd3gm.js";import{O as C}from"./OrderServices-ByScXALP.js";import{S as w}from"./SelectStatus-DFwM1XF_.js";const F=({data:l,printRef:c,globalSetting:i})=>{const{t:a}=h(),n=i?.default_currency||"$";return e.jsx("div",{ref:c,className:"p-4",children:Array.isArray(l)?l?.map((s,r)=>e.jsxs("div",{className:"mb-8",children:[i?.logo&&e.jsx("img",{className:"flex mx-auto",size:"large",src:i?.logo,alt:"",width:50}),e.jsxs("div",{className:"my-1",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("h1",{className:"font-bold text-xl",children:i?.company_name})}),e.jsxs(t.ModalBody,{className:"flex flex-col justify-center text-center",children:[e.jsx("span",{className:"flex-row",children:i?.address}),e.jsx("span",{className:"flex justify-center",children:i?.contact}),i?.web_site,e.jsx("br",{}),i?.email]})]}),e.jsx(t.TableContainer,{className:"my-4 rounded-b-lg",children:e.jsxs(t.Table,{children:[e.jsx(t.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(t.TableCell,{className:"bg-white",children:e.jsx("span",{className:"text-xs capitalize text-gray-700",children:a("Item")})}),e.jsx(t.TableCell,{className:"text-xs bg-white capitalize text-center text-gray-700",children:a("QTY")}),e.jsx(t.TableCell,{className:"text-xs bg-white capitalize text-right text-gray-700",children:a("Amount")})]})}),e.jsx(t.TableBody,{className:"bg-white dark:bg-gray-800 divide-y divide-gray-100 text-serif text-sm",children:s?.cart?.map((x,d)=>e.jsxs(t.TableRow,{className:"dark:border-gray-700 dark:text-gray-400 bill",children:[e.jsx(t.TableCell,{className:"py-1",children:e.jsxs("span",{className:"font-normal text-gray-600 bill",children:[" ",x.title?.substring(0,15)]})}),e.jsx(t.TableCell,{className:"text-center py-1",children:e.jsxs("span",{className:"font-bold text-center bill",children:[" ",x.quantity," "]})}),e.jsx(t.TableCell,{className:"text-right py-1",children:e.jsxs("span",{className:"text-right font-bold text-gray-700 bill",children:[" ",n,(x.price*x.quantity).toFixed(2)]})})]},d))})]})}),e.jsx(t.ModalBody,{children:e.jsxs("div",{className:"flex justify-between -mt-3 mr-1 mb-4",children:[e.jsxs("div",{className:"mt-2",children:[s?.paymentMethod==="Combined"?e.jsxs("p",{className:"bill",children:[e.jsxs("span",{className:"mb-1 font-semibold bill font-serif text-xs text-gray-700 dark:text-gray-500 block",children:[a("Paymentmethod")," :"," ",e.jsx("span",{className:"text-gray-600 bill",children:s.paymentMethod})]}),s?.paymentDetails?.selectPaymentOption_Card!==void 0&&e.jsxs("span",{className:"text-xs bill",children:[s?.paymentDetails?.selectPaymentOption_Card,":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[" ",n,parseFloat(s?.paymentDetails?.paymentAmount_Card).toFixed(2)]})]}),e.jsx("br",{}),s?.paymentDetails?.selectPaymentOption_Cash!==void 0&&e.jsxs("span",{className:"text-xs bill",children:[s?.paymentDetails?.selectPaymentOption_Cash,":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[n,parseFloat(s?.paymentDetails?.paymentAmount_Cash).toFixed(2)]})]}),e.jsx("br",{}),s?.paymentDetails?.selectPaymentOption_Credit!==void 0&&e.jsxs("span",{className:"text-xs bill",children:[s?.paymentDetails?.selectPaymentOption_Credit,":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[n,parseFloat(s?.paymentDetails?.paymentAmount_Credit).toFixed(2)]})]})]}):e.jsx("p",{className:"bill",children:e.jsxs("span",{className:"font-semibold bill font-serif text-xs text-gray-600 dark:text-gray-500 block",children:[a("Paymentmethod")," :"," ",e.jsx("span",{className:"text-gray-700 bill",children:s.paymentMethod})]})}),e.jsxs("div",{className:"text-xs bill",children:[s?.shippingOption&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-gray-600",children:[a("ShippingMethodLower")," :",e.jsx("span",{className:"font-semibold text-gray-900",children:s?.shippingOption})]}),e.jsx("br",{})]}),e.jsxs("span",{className:"text-gray-600",children:[a("NoofItems")," :"," ",e.jsx("span",{className:"font-semibold text-gray-900",children:s?.cart?.length})," "]})," ",e.jsx("br",{}),e.jsxs("span",{className:"text-gray-600",children:[a("BillNo")," :"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[" ",s?.invoice]})," "]})," ",e.jsx("br",{}),e.jsx("br",{}),i?.vat_number&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-gray-600",children:[a("VATNumber"),":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[" ",i?.vat_number]})," "]}),e.jsx("br",{})]}),e.jsxs("span",{className:"text-gray-600",children:[a("Date")," :"," ",e.jsxs("span",{className:"font-semibold text-gray-700",children:[" ",p(new Date).format("MM/D/YYYY")]})," "]})]})]}),e.jsxs("div",{className:"mt-2",children:[e.jsxs("h5",{className:"flex justify-between font-medium text-xs ",children:[e.jsxs("span",{children:[a("GrossTotal")," :"]})," ",e.jsxs("span",{className:"font-semibold ",children:[n,parseFloat(s?.subTotal).toFixed(2)]})]}),s?.shippingCost>0&&e.jsxs("h5",{className:"flex justify-between font-medium text-xs",children:[e.jsxs("span",{children:[" ",a("ShippingCostLower")," :"]})," ",e.jsxs("span",{className:"font-semibold ",children:[n,parseFloat(s?.shippingCost).toFixed(2)]})]}),s?.discount>0&&e.jsxs("h5",{className:"flex justify-between font-medium text-xs",children:[e.jsxs("span",{children:[" ",a("DiscountLower")," :"]})," ",e.jsxs("span",{className:"font-semibold",children:[n,parseFloat(s?.discount).toFixed(2)]})]}),e.jsxs("h3",{className:"flex justify-between font-medium text-xs border-t border-black mt-2",children:[e.jsxs("span",{children:[" ",a("Total")," : "]}),e.jsxs("span",{className:"font-semibold ",children:[n,parseFloat(s?.total).toFixed(2)]})]})]})]})}),e.jsx("h2",{className:"mb-2 text-center font-medium text-sm",children:a("ThankYouMsg")})]},r+1)):e.jsxs(o.Fragment,{children:[i?.logo&&e.jsx("img",{className:"flex mx-auto",size:"large",src:i?.logo,alt:"",width:50}),e.jsxs("div",{className:"my-1",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("h1",{className:"font-bold text-xl",children:i?.company_name})}),e.jsxs(t.ModalBody,{className:"flex flex-col justify-center text-center",children:[e.jsx("span",{className:"flex-row",children:i?.address}),e.jsx("span",{className:"flex justify-center",children:i?.contact}),i?.web_site,e.jsx("br",{}),i?.email]})]}),e.jsx(t.TableContainer,{className:"my-4 rounded-b-lg",children:e.jsxs(t.Table,{children:[e.jsx(t.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(t.TableCell,{className:"bg-white",children:e.jsx("span",{className:"text-xs capitalize text-gray-700",children:a("Item")})}),e.jsx(t.TableCell,{className:"text-xs bg-white capitalize text-center text-gray-700",children:a("QTY")}),e.jsx(t.TableCell,{className:"text-xs bg-white capitalize text-right text-gray-700",children:a("Amount")})]})}),e.jsx(t.TableBody,{className:"bg-white dark:bg-gray-800 divide-y divide-gray-100 text-serif text-sm",children:l?.cart?.map((s,r)=>e.jsxs(t.TableRow,{className:"dark:border-gray-700 dark:text-gray-400 bill",children:[e.jsx(t.TableCell,{className:"py-1",children:e.jsxs("span",{className:"font-normal text-gray-600 bill",children:[" ",s.title?.substring(0,15)]})}),e.jsx(t.TableCell,{className:"text-center py-1",children:e.jsxs("span",{className:"font-bold text-center bill",children:[" ",s.quantity," "]})}),e.jsx(t.TableCell,{className:"text-right py-1",children:e.jsxs("span",{className:"text-right font-bold text-gray-700 bill",children:[" ",n,(s.price*s.quantity).toFixed(2)]})})]},r))})]})}),e.jsx(t.ModalBody,{children:e.jsxs("div",{className:"flex justify-between -mt-3 mr-1 mb-4",children:[e.jsxs("div",{className:"mt-2",children:[l?.paymentMethod==="Combined"?e.jsxs("p",{className:"bill",children:[e.jsxs("span",{className:"mb-1 font-semibold bill font-serif text-xs text-gray-700 dark:text-gray-500 block",children:[a("Paymentmethod")," :"," ",e.jsx("span",{className:"text-gray-600 bill",children:l.paymentMethod})]}),l?.paymentDetails?.selectPaymentOption_Card!==void 0&&e.jsxs("span",{className:"text-xs bill",children:[l?.paymentDetails?.selectPaymentOption_Card,":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[" ",n,parseFloat(l?.paymentDetails?.paymentAmount_Card).toFixed(2)]})]}),e.jsx("br",{}),l?.paymentDetails?.selectPaymentOption_Cash!==void 0&&e.jsxs("span",{className:"text-xs bill",children:[l?.paymentDetails?.selectPaymentOption_Cash,":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[n,parseFloat(l?.paymentDetails?.paymentAmount_Cash).toFixed(2)]})]}),e.jsx("br",{}),l?.paymentDetails?.selectPaymentOption_Credit!==void 0&&e.jsxs("span",{className:"text-xs bill",children:[l?.paymentDetails?.selectPaymentOption_Credit,":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[n,parseFloat(l?.paymentDetails?.paymentAmount_Credit).toFixed(2)]})]})]}):e.jsx("p",{className:"bill",children:e.jsxs("span",{className:"font-semibold bill font-serif text-xs text-gray-600 dark:text-gray-500 block",children:[a("Paymentmethod")," :"," ",e.jsx("span",{className:"text-gray-700 bill",children:l.paymentMethod})]})}),e.jsxs("div",{className:"text-xs bill",children:[l?.shippingOption&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-gray-600",children:[a("ShippingMethodLower")," :",e.jsx("span",{className:"font-semibold text-gray-900",children:l?.shippingOption})]}),e.jsx("br",{})]}),e.jsxs("span",{className:"text-gray-600",children:[a("NoofItems")," :"," ",e.jsx("span",{className:"font-semibold text-gray-900",children:l?.cart?.length})," "]})," ",e.jsx("br",{}),e.jsxs("span",{className:"text-gray-600",children:[a("BillNo")," :"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[" ",l?.invoice]})," "]})," ",e.jsx("br",{}),e.jsx("br",{}),i?.vat_number&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"text-gray-600",children:[a("VATNumber"),":"," ",e.jsxs("span",{className:"font-semibold text-gray-900",children:[" ",i?.vat_number]})," "]}),e.jsx("br",{})]}),e.jsxs("span",{className:"text-gray-600",children:[a("Date")," :"," ",e.jsxs("span",{className:"font-semibold text-gray-700",children:[" ",p(new Date).format("MM/D/YYYY")]})," "]})]})]}),e.jsxs("div",{className:"mt-2",children:[e.jsxs("h5",{className:"flex justify-between font-medium text-xs ",children:[e.jsxs("span",{children:[a("GrossTotal")," :"]})," ",e.jsxs("span",{className:"font-semibold ",children:[n,parseFloat(l?.subTotal).toFixed(2)]})]}),l?.shippingCost>0&&e.jsxs("h5",{className:"flex justify-between font-medium text-xs",children:[e.jsxs("span",{children:[" ",a("ShippingCostLower")," :"]})," ",e.jsxs("span",{className:"font-semibold ",children:[n,parseFloat(l?.shippingCost).toFixed(2)]})]}),l?.discount>0&&e.jsxs("h5",{className:"flex justify-between font-medium text-xs",children:[e.jsxs("span",{children:[" ",a("DiscountLower")," :"]})," ",e.jsxs("span",{className:"font-semibold",children:[n,parseFloat(l?.discount).toFixed(2)]})]}),e.jsxs("h3",{className:"flex justify-between font-medium text-xs border-t border-black mt-2",children:[e.jsxs("span",{children:[" ",a("Total")," : "]}),e.jsxs("span",{className:"font-semibold ",children:[n,parseFloat(l?.total).toFixed(2)]})]})]})]})}),e.jsx("h2",{className:"mb-2 text-center font-medium text-sm",children:a("ThankYouMsg")})]})})},v=({orderId:l})=>{const c=o.useRef(),[i,a]=o.useState({}),{globalSetting:n}=j(),s=`
    @media print {
      @page {
        size: ${n?.receipt_size==="A4"?"8.5in 14in":n?.receipt_size==="3-1/8"?"9.8in 13.8in":n?.receipt_size==="2-1/4"?"3in 8in":"3.5in 8.5in"};
        margin: 0;
        padding: 0;
        font-size: 10px !important;
      }
    
      @page: first {
        size: ${n?.receipt_size==="A4"?"8.5in 14in":n?.receipt_size==="3-1/8"?"9.8in 13.8in":n?.receipt_size==="2-1/4"?"3in 8in":"3.5in 8.5in"};
        margin: 0;
        font-size: 10px !important;
      }
    }
`,r=g.useReactToPrint({content:()=>c.current,pageStyle:s,documentTitle:"Invoice"}),x=async d=>{try{const m=await C.getOrderById(d);a(m),r()}catch(m){T(m?m?.response?.data?.message:m?.message)}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{display:"none"},children:Object.keys(i).length>0&&e.jsx(F,{data:i,printRef:c,globalSetting:n})}),e.jsx("button",{onClick:()=>x(l),type:"button",className:"ml-2 p-2 cursor-pointer text-gray-500 hover:text-emerald-600 focus:outline-none",children:e.jsx(y,{id:"receipt",Icon:N,title:"Print Receipt",bgColor:"#f59e0b"})})]})},I=({orders:l})=>{const{t:c}=h(),{showDateTimeFormat:i,currency:a,getNumberTwo:n}=j();return e.jsx(e.Fragment,{children:e.jsx(t.TableBody,{className:"dark:bg-gray-900",children:l?.map((s,r)=>e.jsxs(t.TableRow,{children:[e.jsx(t.TableCell,{children:e.jsx("span",{className:"font-semibold uppercase text-xs",children:s?.invoice})}),e.jsx(t.TableCell,{children:e.jsx("span",{className:"text-sm",children:i(s?.updatedDate)})}),e.jsxs(t.TableCell,{className:"text-xs",children:[e.jsx("span",{className:"text-sm",children:s?.user_info?.name})," "]}),e.jsx(t.TableCell,{children:e.jsx("span",{className:"text-sm font-semibold",children:s?.paymentMethod})}),e.jsx(t.TableCell,{children:e.jsxs("span",{className:"text-sm font-semibold",children:[a,n(s?.total)]})}),e.jsx(t.TableCell,{className:"text-xs",children:e.jsx(u,{status:s?.status})}),e.jsx(t.TableCell,{className:"text-center",children:e.jsx(w,{id:s._id,order:s})}),e.jsx(t.TableCell,{className:"text-right flex justify-end",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(v,{orderId:s._id}),e.jsx("span",{className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600",children:e.jsx(b,{to:`/order/${s._id}`,children:e.jsx(y,{id:"view",Icon:f,title:c("ViewInvoice"),bgColor:"#059669"})})})]})})]},r+1))})})};export{I as O};
