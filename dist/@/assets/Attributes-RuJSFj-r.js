import{j as e,i as v,f as l,k as H,r as f,S as z}from"./index-DD5OQCzb.js";import{l as K,u as $,e as C,f as G,g as J}from"./Layout-f_j_aP34.js";import{T as Q}from"./Tooltip-DrdTd94n.js";import{T as A,D as W,u as D,M as S}from"./DrawerButton-C1kY46U5.js";import{D as k,E as X}from"./EditDeleteButton-B2PjzFtp.js";import{C as _,S as Y,B as Z}from"./BulkActionDrawer-BEXxVAnh.js";import{E as w}from"./index.esm-BPZGYcl8.js";import{L as u}from"./LabelArea-Zz4acQmF.js";import{I as T}from"./InputArea-Cu6xCoGw.js";import{u as ee}from"./useAttributeSubmit-BHLRuQLh.js";import{U as le}from"./UploadMany-Bdp4IRJJ.js";import{T as se}from"./TableLoading-D3D_P-TQ.js";import{N as ae}from"./NotFound-DG_8Itz7.js";import{P as te}from"./PageTitle-DUWCiaui.js";import{u as re}from"./useAsync-CdFiuEZy.js";import{u as ie}from"./useFilter-BHZ5O6jw.js";import{A as ne}from"./ProductServices-CGXRs0W4.js";import{A as de}from"./AnimatedContent-DbKaf3qr.js";import"./iconBase-DTk8F31e.js";import"./SelectLanguageTwo-CQlbeojL.js";import"./spinner-CkndCogW.js";import"./AdminServices-Crgje1Fu.js";import"./CouponServices-BvJiM6D0.js";import"./CurrencyServices-CicNeQxs.js";import"./toast-C_V_NPJL.js";import"./useDisableForDemo-aTnQzb5-.js";import"./index.prod-CisttSXz.js";import"./ParentCategory-BL1vwhP5.js";import"./SwitchToggle-CiShsGtJ.js";import"./useTranslationValue-d_-eYXcs.js";import"./exportFromJSON-fDIoOtpr.js";import"./index-0EvDzr9j.js";const oe=({notes:t,addNote:o,removeNote:d})=>e.jsxs("div",{className:"react-tag-input",children:[e.jsx("ul",{id:"tags",children:t.map((r,n)=>e.jsxs("li",{className:"react-tag-input__tag",children:[e.jsx("span",{className:"tag-title react-tag-input__tag__content",children:r}),e.jsx("span",{className:"react-tag-input__tag__remove",onClick:()=>d(n)})]},n))}),e.jsx("input",{name:"note",className:"react-tag-input__input",type:"text",onBlur:r=>o(r),onKeyDown:r=>r.key==="Enter"?o(r):null,placeholder:"Press enter to add variant"})]}),B=({id:t})=>{const{handleSubmit:o,onSubmit:d,register:r,errors:n,variants:x,addVariant:m,isSubmitting:c,removeVariant:i,handleSelectLanguage:s}=ee(t),{t:a}=v();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:t?e.jsx(A,{register:r,handleSelectLanguage:s,title:a("UpdateAttribute"),description:a("UpdateAttributeDesc")}):e.jsx(A,{register:r,handleSelectLanguage:s,title:a("AddAttribute"),description:a("AddAttributeDesc")})}),e.jsxs(K.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:[e.jsxs("form",{onSubmit:o(d),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(u,{label:a("DrawerAttributeTitle")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(T,{required:!0,register:r,label:"Attribute Title",name:"title",type:"text",placeholder:"Color or Size or Dimension or Material or Fabric"}),e.jsx(w,{errorName:n.title})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 relative",children:[e.jsx(u,{label:a("DisplayName")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(T,{required:!0,register:r,label:"Display Name",name:"name",type:"text",placeholder:"Display Name"}),e.jsx(w,{errorName:n.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 relative",children:[e.jsx(u,{label:a("DrawerOptions")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4 ",children:[e.jsxs(l.Select,{name:"option",...r("option",{required:"Option is required!"}),children:[e.jsx("option",{value:"",defaultValue:!0,hidden:!0,children:a("DrawerSelecttype")}),e.jsx("option",{value:"Dropdown",children:a("Dropdown")}),e.jsx("option",{value:"Radio",children:a("Radio")})]}),e.jsx(w,{errorName:n.option})]})]})]}),e.jsx(W,{id:t,title:"Attribute",isSubmitting:c})]}),e.jsx("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40 ",children:!t&&e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 relative",children:[e.jsx(u,{label:a("Variants")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(oe,{notes:x,addNote:m,removeNote:i})})]})})]})]})},ce=({isCheck:t,setIsCheck:o,attributes:d})=>{const{title:r,serviceId:n,handleModalOpen:x,handleUpdate:m}=D(),{showingTranslateValue:c}=$(),i=s=>{const{id:a,checked:j}=s.target;o([...t,a]),j||o(t.filter(p=>p!==a))};return e.jsxs(e.Fragment,{children:[t.length<1&&e.jsx(k,{id:n,title:r}),t.length<2&&e.jsx(S,{children:e.jsx(B,{id:n})}),e.jsx(l.TableBody,{children:d==null?void 0:d.map(s=>{var a;return e.jsxs(l.TableRow,{children:[e.jsx(l.TableCell,{children:e.jsx(_,{type:"checkbox",name:"attribute",id:s._id,handleClick:i,isChecked:t==null?void 0:t.includes(s._id)})}),e.jsx(l.TableCell,{className:"font-semibold uppercase text-xs",children:(a=s==null?void 0:s._id)==null?void 0:a.substring(20,24)}),e.jsx(l.TableCell,{className:"font-medium text-sm",children:c(s.title)}),e.jsx(l.TableCell,{className:"font-medium text-sm",children:c(s.name)}),e.jsx(l.TableCell,{className:"font-medium text-sm",children:s.option}),e.jsx(l.TableCell,{className:"text-center",children:e.jsx(Y,{id:s._id,status:s.status})}),e.jsx(l.TableCell,{className:"flex justify-center",children:e.jsx(H,{to:`/attributes/${s._id}`,className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:e.jsx(Q,{id:"edit values",Icon:C,title:"Edit Values",bgColor:"#10B981"})})}),e.jsx(l.TableCell,{children:e.jsx(X,{id:s._id,isCheck:t,setIsCheck:o,handleUpdate:m,handleModalOpen:x,title:c(s.title)})})]},s._id)})})]})},He=()=>{const{toggleDrawer:t,lang:o}=f.useContext(z),{data:d,loading:r,error:n}=re(()=>ne.getAllAttributes({type:"attribute",option:"Dropdown",option1:"Radio"})),{handleDeleteMany:x,allId:m,handleUpdateMany:c}=D(),{t:i}=v(),{filename:s,isDisabled:a,dataTable:j,serviceData:p,totalResults:F,attributeRef:N,resultsPerPage:E,handleSelectFile:P,handleChangePage:R,setAttributeTitle:M,handleSubmitAttribute:y,handleUploadMultiple:I,handleRemoveSelectFile:U}=ie(d),[b,V]=f.useState(!1),[h,g]=f.useState([]),L=()=>{V(!b),g(d.map(O=>O._id)),b&&g([])},q=()=>{M(""),N.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(te,{children:i("AttributeTitle")}),e.jsx(k,{ids:m,setIsCheck:g,title:"Selected Attributes"}),e.jsx(Z,{ids:m,title:"Attributes"}),e.jsx(S,{children:e.jsx(B,{})}),e.jsxs(de,{children:[e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:y,className:"py-3  grid gap-4 lg:gap-6 xl:gap-6  xl:flex",children:[e.jsx("div",{className:"flex justify-start xl:w-1/2  md:w-full",children:e.jsx(le,{title:"Attribute",exportData:d,filename:s,isDisabled:a,handleSelectFile:P,handleUploadMultiple:I,handleRemoveSelectFile:U})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:h.length<1,onClick:()=>c(h),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(C,{})}),i("BulkAction")]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:h.length<1,onClick:()=>x(h),className:"w-full rounded-md h-12 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(G,{})}),i("Delete")]})}),e.jsx("div",{className:"w-full md:w-48 lg:w-48 xl:w-48",children:e.jsxs(l.Button,{onClick:t,className:"w-full rounded-md h-12 ",children:[e.jsx("span",{className:"mr-2",children:e.jsx(J,{})}),i("CouponsAddAttributeBtn")]})})]})]})})}),e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:y,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(l.Input,{ref:N,type:"search",placeholder:i("SearchAttributePlaceholder")})}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{layout:"outline",onClick:q,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),r?e.jsx(se,{row:12,col:6,width:180,height:20}):n?e.jsx("span",{className:"text-center mx-auto text-red-500",children:n}):(p==null?void 0:p.length)!==0?e.jsxs(l.TableContainer,{className:"mb-8",children:[e.jsxs(l.Table,{children:[e.jsx(l.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(l.TableCell,{children:e.jsx(_,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:L,isChecked:b})}),e.jsxs(l.TableCell,{children:[" ",i("Id")," "]}),e.jsxs(l.TableCell,{children:[" ",i("AName")]}),e.jsxs(l.TableCell,{children:[" ",i("ADisplayName")]}),e.jsx(l.TableCell,{children:i("AOption")}),e.jsx(l.TableCell,{className:"text-center",children:i("catPublishedTbl")}),e.jsx(l.TableCell,{className:"text-center",children:i("Avalues")}),e.jsx(l.TableCell,{className:"text-right",children:i("AAction")})]})}),e.jsx(ce,{lang:o,isCheck:h,setIsCheck:g,attributes:j})]}),e.jsx(l.TableFooter,{children:e.jsx(l.Pagination,{totalResults:F,resultsPerPage:E,onChange:R,label:"Table navigation"})})]}):e.jsx(ae,{title:"Sorry, There are no attributes right now."})]})};export{He as default};
