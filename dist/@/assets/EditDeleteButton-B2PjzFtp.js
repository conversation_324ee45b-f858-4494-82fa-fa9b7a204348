import{o as T,r as M,S as $,u as I,i as F,j as t,f,L as S,k as O}from"./index-DD5OQCzb.js";import{f as A,i as R,e as N}from"./Layout-f_j_aP34.js";import{s as H}from"./spinner-CkndCogW.js";import{A as K}from"./AdminServices-Crgje1Fu.js";import{P as D,C as y,b as V,A as x}from"./ProductServices-CGXRs0W4.js";import{C as B}from"./CouponServices-BvJiM6D0.js";import{u as Z}from"./DrawerButton-C1kY46U5.js";import{C as E}from"./CurrencyServices-CicNeQxs.js";import{n as l,a as b}from"./toast-C_V_NPJL.js";import{u as _}from"./useDisableForDemo-aTnQzb5-.js";import{T as h}from"./Tooltip-DrdTd94n.js";const q=({id:r,ids:n,setIsCheck:u,category:p,title:c,useParamId:j})=>{const{isModalOpen:d,closeModal:s,setIsUpdate:a}=M.useContext($),{setServiceId:i}=Z(),m=I(),[C,o]=M.useState(!1),{handleDisableForDemo:L}=_(),P=async()=>{var w,v;if(!L())try{if(o(!0),m.pathname==="/products")if(n){const e=await D.deleteManyProducts({ids:n});a(!0),l(e.message),u([]),i(),s(),o(!1)}else{const e=await D.deleteProduct(r);a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname==="/coupons")if(n){const e=await B.deleteManyCoupons({ids:n});a(!0),l(e.message),u([]),i(),s(),o(!1)}else{const e=await B.deleteCoupon(r);a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname==="/categories"||p)if(n){const e=await y.deleteManyCategory({ids:n});a(!0),l(e.message),u([]),i(),s(),o(!1)}else{if(r===void 0||!r)return b("Please select a category first!"),o(!1),s();const e=await y.deleteCategory(r);a(!0),l(e.message),s(),i(),o(!1)}else if(m.pathname===`/categories/${j}`||p){if(r===void 0||!r)return b("Please select a category first!"),o(!1),s();const e=await y.deleteCategory(r);a(!0),l(e.message),s(),i(),o(!1)}if(m.pathname==="/customers"){const e=await V.deleteCustomer(r);a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname==="/attributes")if(n){const e=await x.deleteManyAttribute({ids:n});a(!0),l(e.message),u([]),i(),s(),o(!1)}else{const e=await x.deleteAttribute(r);a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname===`/attributes/${m.pathname.split("/")[2]}`)if(n){const e=await x.deleteManyChildAttribute({id:m.pathname.split("/")[2],ids:n});a(!0),l(e.message),i(),u([]),s(),o(!1)}else{const e=await x.deleteChildAttribute({id:r,ids:m.pathname.split("/")[2]});a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname==="/our-staff"){const e=await K.deleteStaff(r);a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname==="/languages")if(n){const e=await S.deleteManyLanguage({ids:n});a(!0),l(e.message),u([]),s(),o(!1)}else{const e=await S.deleteLanguage(r);a(!0),l(e.message),i(),s(),o(!1)}if(m.pathname==="/currencies")if(n){const e=await E.deleteManyCurrency({ids:n});a(!0),l(e.message),u([]),s(),o(!1)}else{const e=await E.deleteCurrency(r);a(!0),l(e.message),i(),s(),o(!1)}}catch(e){b(e?(v=(w=e==null?void 0:e.response)==null?void 0:w.data)==null?void 0:v.message:e==null?void 0:e.message),i(),u([]),s(),o(!1)}},{t:g}=F();return t.jsx(t.Fragment,{children:t.jsxs(f.Modal,{isOpen:d,onClose:s,children:[t.jsxs(f.ModalBody,{className:"text-center custom-modal px-8 pt-6 pb-4",children:[t.jsx("span",{className:"flex justify-center text-3xl mb-6 text-red-500",children:t.jsx(A,{})}),t.jsxs("h2",{className:"text-xl font-medium mb-2",children:[g("DeleteModalH2")," ",t.jsx("span",{className:"text-red-500",children:c}),"?"]}),t.jsx("p",{children:g("DeleteModalPtag")})]}),t.jsxs(f.ModalFooter,{className:"justify-center",children:[t.jsx(f.Button,{className:"w-full sm:w-auto hover:bg-white hover:border-gray-50",layout:"outline",onClick:s,children:g("modalKeepBtn")}),t.jsx("div",{className:"flex justify-end",children:C?t.jsxs(f.Button,{disabled:!0,type:"button",className:"w-full h-12 sm:w-auto",children:[t.jsx("img",{src:H,alt:"Loading",width:20,height:10})," ",t.jsx("span",{className:"font-serif ml-2 font-light",children:g("Processing")})]}):t.jsx(f.Button,{onClick:P,className:"w-full h-12 sm:w-auto",children:g("modalDeletBtn")})})]})]})})},se=T.memo(q),ae=({id:r,title:n,handleUpdate:u,handleModalOpen:p,isCheck:c,product:j,parent:d,children:s})=>{const{t:a}=F();return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"flex justify-end text-right",children:[(s==null?void 0:s.length)>0?t.jsxs(t.Fragment,{children:[t.jsx(O,{to:`/categories/${d==null?void 0:d._id}`,className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:t.jsx(h,{id:"view",Icon:R,title:a("View"),bgColor:"#10B981"})}),t.jsx("button",{disabled:(c==null?void 0:c.length)>0,onClick:()=>u(r),className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:t.jsx(h,{id:"edit",Icon:N,title:a("Edit"),bgColor:"#10B981"})})]}):t.jsx("button",{disabled:(c==null?void 0:c.length)>0,onClick:()=>u(r),className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:t.jsx(h,{id:"edit",Icon:N,title:a("Edit"),bgColor:"#10B981"})}),t.jsx("button",{disabled:(c==null?void 0:c.length)>0,onClick:()=>p(r,n,j),className:"p-2 cursor-pointer text-gray-400 hover:text-red-600 focus:outline-none",children:t.jsx(h,{id:"delete",Icon:A,title:a("Delete"),bgColor:"#EF4444"})})]})})};export{se as D,ae as E};
