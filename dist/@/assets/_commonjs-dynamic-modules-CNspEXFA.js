import{g as y}from"./index-DpMxJ5Hx.js";import{r as p}from"./index-C148XJoK.js";function w(o,l,r,n){var a=arguments.length,e=a<3?l:n===null?n=Object.getOwnPropertyDescriptor(l,r):n,i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,l,r,n);else for(var u=o.length-1;u>=0;u--)(i=o[u])&&(e=(a<3?i(e):a>3?i(l,r,e):i(l,r))||e);return a>3&&e&&Object.defineProperty(l,r,e),e}function m(o,l,r,n){function a(e){return e instanceof r?e:new r(function(i){i(e)})}return new(r||(r=Promise))(function(e,i){function u(c){try{t(n.next(c))}catch(s){i(s)}}function f(c){try{t(n.throw(c))}catch(s){i(s)}}function t(c){c.done?e(c.value):a(c.value).then(u,f)}t((n=n.apply(o,l||[])).next())})}function v(o,l){var r={label:0,sent:function(){if(e[0]&1)throw e[1];return e[1]},trys:[],ops:[]},n,a,e,i;return i={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function u(t){return function(c){return f([t,c])}}function f(t){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,t[0]&&(r=0)),r;)try{if(n=1,a&&(e=t[0]&2?a.return:t[0]?a.throw||((e=a.return)&&e.call(a),0):a.next)&&!(e=e.call(a,t[1])).done)return e;switch(a=0,e&&(t=[t[0]&2,e.value]),t[0]){case 0:case 1:e=t;break;case 4:return r.label++,{value:t[1],done:!1};case 5:r.label++,a=t[1],t=[0];continue;case 7:t=r.ops.pop(),r.trys.pop();continue;default:if(e=r.trys,!(e=e.length>0&&e[e.length-1])&&(t[0]===6||t[0]===2)){r=0;continue}if(t[0]===3&&(!e||t[1]>e[0]&&t[1]<e[3])){r.label=t[1];break}if(t[0]===6&&r.label<e[1]){r.label=e[1],e=t;break}if(e&&r.label<e[2]){r.label=e[2],r.ops.push(t);break}e[2]&&r.ops.pop(),r.trys.pop();continue}t=l.call(o,r)}catch(c){t=[6,c],a=0}finally{n=e=0}if(t[0]&5)throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}function _(o,l){var r=typeof Symbol=="function"&&o[Symbol.iterator];if(!r)return o;var n=r.call(o),a,e=[],i;try{for(;(l===void 0||l-- >0)&&!(a=n.next()).done;)e.push(a.value)}catch(u){i={error:u}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return e}function g(o,l,r){if(r||arguments.length===2)for(var n=0,a=l.length,e;n<a;n++)(e||!(n in l))&&(e||(e=Array.prototype.slice.call(l,0,n)),e[n]=l[n]);return o.concat(e||Array.prototype.slice.call(l))}var h=p();const E=y(h);function q(o){throw new Error('Could not dynamically require "'+o+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}export{E as $,w as _,m as a,v as b,q as c,g as d,_ as e};
