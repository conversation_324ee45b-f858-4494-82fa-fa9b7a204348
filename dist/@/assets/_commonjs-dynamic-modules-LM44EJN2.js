function y(o,i,r,t){var l=arguments.length,e=l<3?i:t===null?t=Object.getOwnPropertyDescriptor(i,r):t,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(o,i,r,t);else for(var u=o.length-1;u>=0;u--)(a=o[u])&&(e=(l<3?a(e):l>3?a(i,r,e):a(i,r))||e);return l>3&&e&&Object.defineProperty(i,r,e),e}function h(o,i,r,t){function l(e){return e instanceof r?e:new r(function(a){a(e)})}return new(r||(r=Promise))(function(e,a){function u(c){try{n(t.next(c))}catch(s){a(s)}}function f(c){try{n(t.throw(c))}catch(s){a(s)}}function n(c){c.done?e(c.value):l(c.value).then(u,f)}n((t=t.apply(o,i||[])).next())})}function d(o,i){var r={label:0,sent:function(){if(e[0]&1)throw e[1];return e[1]},trys:[],ops:[]},t,l,e,a;return a={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function u(n){return function(c){return f([n,c])}}function f(n){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,n[0]&&(r=0)),r;)try{if(t=1,l&&(e=n[0]&2?l.return:n[0]?l.throw||((e=l.return)&&e.call(l),0):l.next)&&!(e=e.call(l,n[1])).done)return e;switch(l=0,e&&(n=[n[0]&2,e.value]),n[0]){case 0:case 1:e=n;break;case 4:return r.label++,{value:n[1],done:!1};case 5:r.label++,l=n[1],n=[0];continue;case 7:n=r.ops.pop(),r.trys.pop();continue;default:if(e=r.trys,!(e=e.length>0&&e[e.length-1])&&(n[0]===6||n[0]===2)){r=0;continue}if(n[0]===3&&(!e||n[1]>e[0]&&n[1]<e[3])){r.label=n[1];break}if(n[0]===6&&r.label<e[1]){r.label=e[1],e=n;break}if(e&&r.label<e[2]){r.label=e[2],r.ops.push(n);break}e[2]&&r.ops.pop(),r.trys.pop();continue}n=i.call(o,r)}catch(c){n=[6,c],l=0}finally{t=e=0}if(n[0]&5)throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}}function p(o,i){var r=typeof Symbol=="function"&&o[Symbol.iterator];if(!r)return o;var t=r.call(o),l,e=[],a;try{for(;(i===void 0||i-- >0)&&!(l=t.next()).done;)e.push(l.value)}catch(u){a={error:u}}finally{try{l&&!l.done&&(r=t.return)&&r.call(t)}finally{if(a)throw a.error}}return e}function b(o,i,r){if(r||arguments.length===2)for(var t=0,l=i.length,e;t<l;t++)(e||!(t in i))&&(e||(e=Array.prototype.slice.call(i,0,t)),e[t]=i[t]);return o.concat(e||Array.prototype.slice.call(i))}function w(o){throw new Error('Could not dynamically require "'+o+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}export{y as _,h as a,d as b,w as c,b as d,p as e};
