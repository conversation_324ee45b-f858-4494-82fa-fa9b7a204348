import{g as zo,r as W,p as Cn,k as Ai,j as y,h as q,S as Bo}from"./index-DpMxJ5Hx.js";import{u as Pn,d as wt,F as Ki,a as jo,b as No,c as Vo}from"./Layout-B-UGxzbM.js";import{i as Wo,a as Ho,u as $o}from"./useFilter-Qjmg-ZWR.js";import{b as qi,c as Gi}from"./index.esm-Clp-i8dk.js";import{u as Se}from"./useAsync-Hr4bbxCm.js";import{S as qt,T as Yo}from"./TableLoading-C5i6hQGj.js";import{O as Xo}from"./OrderTable-BibLRDnT.js";import{N as Uo}from"./NotFound-BXAjvYR4.js";import{P as Zi}from"./PageTitle-D-hGib5s.js";import{O as we}from"./OrderServices-ByScXALP.js";import{A as Ko}from"./AnimatedContent-DVQRys_r.js";import"./iconBase-BUmmAlr8.js";import"./ProductServices-CnM1m97m.js";import"./index-C148XJoK.js";import"./useDisableForDemo-DczgqPm6.js";import"./toast-Be5Wd3gm.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";import"./Status-BjgMFBDv.js";import"./Tooltip-BQ_BZ_s8.js";import"./index-BYGsSwV0.js";import"./SelectStatus-DFwM1XF_.js";var Ve={exports:{}},qo=Ve.exports,Ji;function Go(){return Ji||(Ji=1,function(i,t){(function(e,s){i.exports=s()})(qo,function(){return function(e,s,n){s.prototype.isYesterday=function(){var o="YYYY-MM-DD",r=n().subtract(1,"day");return this.format(o)===r.format(o)}}})}(Ve)),Ve.exports}var Zo=Go();const Jo=zo(Zo);/*!
 * @kurkle/color v0.3.2
 * https://github.com/kurkle/color#readme
 * (c) 2023 Jukka Kurkela
 * Released under the MIT License
 */function ke(i){return i+.5|0}const Ct=(i,t,e)=>Math.max(Math.min(i,e),t);function ae(i){return Ct(ke(i*2.55),0,255)}function Ot(i){return Ct(ke(i*255),0,255)}function _t(i){return Ct(ke(i/2.55)/100,0,1)}function Qi(i){return Ct(ke(i*100),0,100)}const lt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},_i=[..."0123456789ABCDEF"],Qo=i=>_i[i&15],tr=i=>_i[(i&240)>>4]+_i[i&15],Ce=i=>(i&240)>>4===(i&15),er=i=>Ce(i.r)&&Ce(i.g)&&Ce(i.b)&&Ce(i.a);function ir(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&lt[i[1]]*17,g:255&lt[i[2]]*17,b:255&lt[i[3]]*17,a:t===5?lt[i[4]]*17:255}:(t===7||t===9)&&(e={r:lt[i[1]]<<4|lt[i[2]],g:lt[i[3]]<<4|lt[i[4]],b:lt[i[5]]<<4|lt[i[6]],a:t===9?lt[i[7]]<<4|lt[i[8]]:255})),e}const sr=(i,t)=>i<255?t(i):"";function nr(i){var t=er(i)?Qo:tr;return i?"#"+t(i.r)+t(i.g)+t(i.b)+sr(i.a,t):void 0}const or=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Dn(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function rr(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function ar(i,t,e){const s=Dn(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function lr(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function Ti(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=lr(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function Li(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(Ot)}function Ri(i,t,e){return Li(Dn,i,t,e)}function cr(i,t,e){return Li(ar,i,t,e)}function hr(i,t,e){return Li(rr,i,t,e)}function On(i){return(i%360+360)%360}function dr(i){const t=or.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?ae(+t[5]):Ot(+t[5]));const n=On(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=cr(n,o,r):t[1]==="hsv"?s=hr(n,o,r):s=Ri(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function ur(i,t){var e=Ti(i);e[0]=On(e[0]+t),e=Ri(e),i.r=e[0],i.g=e[1],i.b=e[2]}function fr(i){if(!i)return;const t=Ti(i),e=t[0],s=Qi(t[1]),n=Qi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${_t(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const ts={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},es={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function gr(){const i={},t=Object.keys(es),e=Object.keys(ts);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,ts[o]);o=parseInt(es[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let Pe;function pr(i){Pe||(Pe=gr(),Pe.transparent=[0,0,0,0]);const t=Pe[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const mr=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function br(i){const t=mr.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?ae(r):Ct(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?ae(s):Ct(s,0,255)),n=255&(t[4]?ae(n):Ct(n,0,255)),o=255&(t[6]?ae(o):Ct(o,0,255)),{r:s,g:n,b:o,a:e}}}function xr(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${_t(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const ai=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Ut=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function _r(i,t,e){const s=Ut(_t(i.r)),n=Ut(_t(i.g)),o=Ut(_t(i.b));return{r:Ot(ai(s+e*(Ut(_t(t.r))-s))),g:Ot(ai(n+e*(Ut(_t(t.g))-n))),b:Ot(ai(o+e*(Ut(_t(t.b))-o))),a:i.a+e*(t.a-i.a)}}function De(i,t,e){if(i){let s=Ti(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=Ri(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function An(i,t){return i&&Object.assign(t||{},i)}function is(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=Ot(i[3]))):(t=An(i,{r:0,g:0,b:0,a:1}),t.a=Ot(t.a)),t}function yr(i){return i.charAt(0)==="r"?br(i):dr(i)}class pe{constructor(t){if(t instanceof pe)return t;const e=typeof t;let s;e==="object"?s=is(t):e==="string"&&(s=ir(t)||pr(t)||yr(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=An(this._rgb);return t&&(t.a=_t(t.a)),t}set rgb(t){this._rgb=is(t)}rgbString(){return this._valid?xr(this._rgb):void 0}hexString(){return this._valid?nr(this._rgb):void 0}hslString(){return this._valid?fr(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=_r(this._rgb,t._rgb,e)),this}clone(){return new pe(this.rgb)}alpha(t){return this._rgb.a=Ot(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=ke(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return De(this._rgb,2,t),this}darken(t){return De(this._rgb,2,-t),this}saturate(t){return De(this._rgb,1,t),this}desaturate(t){return De(this._rgb,1,-t),this}rotate(t){return ur(this._rgb,t),this}}/*!
 * Chart.js v4.4.0
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */function mt(){}const vr=(()=>{let i=0;return()=>i++})();function R(i){return i===null||typeof i>"u"}function V(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function L(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function Y(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function rt(i,t){return Y(i)?i:t}function O(i,t){return typeof i>"u"?t:i}const kr=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,Tn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function B(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function F(i,t,e,s){let n,o,r;if(V(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(L(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function $e(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Ye(i){if(V(i))return i.map(Ye);if(L(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=Ye(i[e[n]]);return t}return i}function Ln(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function Mr(i,t,e,s){if(!Ln(i))return;const n=t[i],o=e[i];L(n)&&L(o)?me(n,o,s):t[i]=Ye(o)}function me(i,t,e){const s=V(t)?t:[t],n=s.length;if(!L(i))return i;e=e||{};const o=e.merger||Mr;let r;for(let a=0;a<n;++a){if(r=s[a],!L(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function de(i,t){return me(i,t,{merger:Sr})}function Sr(i,t,e){if(!Ln(i))return;const s=t[i],n=e[i];L(s)&&L(n)?de(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=Ye(n))}const ss={"":i=>i,x:i=>i.x,y:i=>i.y};function wr(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Cr(i){const t=wr(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function At(i,t){return(ss[t]||(ss[t]=Cr(t)))(i)}function Ei(i){return i.charAt(0).toUpperCase()+i.slice(1)}const be=i=>typeof i<"u",Tt=i=>typeof i=="function",ns=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Pr(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const N=Math.PI,j=2*N,Dr=j+N,Xe=Number.POSITIVE_INFINITY,Or=N/180,U=N/2,It=N/4,os=N*2/3,Pt=Math.log10,gt=Math.sign;function ue(i,t,e){return Math.abs(i-t)<e}function rs(i){const t=Math.round(i);i=ue(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(Pt(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Ar(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Zt(i){return!isNaN(parseFloat(i))&&isFinite(i)}function Tr(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Rn(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ht(i){return i*(N/180)}function Fi(i){return i*(180/N)}function as(i){if(!Y(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function En(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*N&&(o+=j),{angle:o,distance:n}}function yi(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Lr(i,t){return(i-t+Dr)%j-N}function at(i){return(i%j+j)%j}function xe(i,t,e,s){const n=at(i),o=at(t),r=at(e),a=at(o-n),l=at(r-n),c=at(n-o),h=at(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function J(i,t,e){return Math.max(t,Math.min(e,i))}function Rr(i){return J(i,-32768,32767)}function yt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function Ii(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const vt=(i,t,e,s)=>Ii(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Er=(i,t,e)=>Ii(i,e,s=>i[s][t]>=e);function Fr(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Fn=["push","pop","shift","splice","unshift"];function Ir(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Fn.forEach(e=>{const s="_onData"+Ei(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function ls(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Fn.forEach(o=>{delete i[o]}),delete i._chartjs)}function In(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const zn=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Bn(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,zn.call(window,()=>{s=!1,i.apply(t,e)}))}}function zr(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const zi=i=>i==="start"?"left":i==="end"?"right":"center",tt=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,Br=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function jn(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:r,_parsed:a}=i,l=r.axis,{min:c,max:h,minDefined:d,maxDefined:u}=r.getUserBounds();d&&(n=J(Math.min(vt(a,l,c).lo,e?s:vt(t,l,r.getPixelForValue(c)).lo),0,s-1)),u?o=J(Math.max(vt(a,r.axis,h,!0).hi+1,e?0:vt(t,l,r.getPixelForValue(h),!0).hi+1),n,s)-n:o=s-n}return{start:n,count:o}}function Nn(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const Oe=i=>i===0||i===1,cs=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*j/e)),hs=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*j/e)+1,fe={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*U)+1,easeOutSine:i=>Math.sin(i*U),easeInOutSine:i=>-.5*(Math.cos(N*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>Oe(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>Oe(i)?i:cs(i,.075,.3),easeOutElastic:i=>Oe(i)?i:hs(i,.075,.3),easeInOutElastic(i){return Oe(i)?i:i<.5?.5*cs(i*2,.1125,.45):.5+.5*hs(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-fe.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?fe.easeInBounce(i*2)*.5:fe.easeOutBounce(i*2-1)*.5+.5};function Bi(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function ds(i){return Bi(i)?i:new pe(i)}function li(i){return Bi(i)?i:new pe(i).saturate(.5).darken(.1).hexString()}const jr=["x","y","borderWidth","radius","tension"],Nr=["color","borderColor","backgroundColor"];function Vr(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:Nr},numbers:{type:"number",properties:jr}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Wr(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const us=new Map;function Hr(i,t){t=t||{};const e=i+JSON.stringify(t);let s=us.get(e);return s||(s=new Intl.NumberFormat(i,t),us.set(e,s)),s}function Me(i,t,e){return Hr(t,e).format(i)}const Vn={values(i){return V(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=$r(i,e)}const r=Pt(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Me(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(Pt(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Vn.numeric.call(this,i,t,e):""}};function $r(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var Je={formatters:Vn};function Yr(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Je.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const $t=Object.create(null),vi=Object.create(null);function ge(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function ci(i,t,e){return typeof t=="string"?me(ge(i,t),e):me(ge(i,""),t)}class Xr{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>li(n.backgroundColor),this.hoverBorderColor=(s,n)=>li(n.borderColor),this.hoverColor=(s,n)=>li(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return ci(this,t,e)}get(t){return ge(this,t)}describe(t,e){return ci(vi,t,e)}override(t,e){return ci($t,t,e)}route(t,e,s,n){const o=ge(this,t),r=ge(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return L(l)?Object.assign({},c,l):O(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var X=new Xr({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Vr,Wr,Yr]);function Ur(i){return!i||R(i.size)||R(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Ue(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function Kr(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let r=0;const a=e.length;let l,c,h,d,u;for(l=0;l<a;l++)if(d=e[l],d!=null&&!V(d))r=Ue(i,n,o,r,d);else if(V(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!V(u)&&(r=Ue(i,n,o,r,u));i.restore();const f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return r}function zt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function fs(i,t){t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore()}function ki(i,t,e,s){Wn(i,t,e,s,null)}function Wn(i,t,e,s,n){let o,r,a,l,c,h,d,u;const f=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*Or;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,p,0,0,j):i.arc(e,s,p,0,j),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=os,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=os,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+It)*l,d=Math.cos(m+It)*(n?n/2-c:l),a=Math.sin(m+It)*l,u=Math.sin(m+It)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-N,m-U),i.arc(e+u,s-r,c,m-U,m),i.arc(e+d,s+a,c,m,m+U),i.arc(e-u,s+r,c,m+U,m+N),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=It;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+u,s-r),i.lineTo(e+d,s+a),i.lineTo(e-u,s+r),i.closePath();break;case"crossRot":m+=It;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r),m+=It,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function kt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Qe(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function ti(i){i.restore()}function qr(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function Gr(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function Zr(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),R(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Jr(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function Qr(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Yt(i,t,e,s,n,o={}){const r=V(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,Zr(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Qr(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),R(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Jr(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function _e(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*N,N,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,N,U,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,U,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-U,!0),i.lineTo(e+r.topLeft,s)}const ta=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ea=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function ia(i,t){const e=(""+i).match(ta);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const sa=i=>+i||0;function ji(i,t){const e={},s=L(t),n=s?Object.keys(t):t,o=L(i)?s?r=>O(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=sa(o(r));return e}function Hn(i){return ji(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Wt(i){return ji(i,["topLeft","topRight","bottomLeft","bottomRight"])}function it(i){const t=Hn(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function G(i,t){i=i||{},t=t||X.font;let e=O(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=O(i.style,t.style);s&&!(""+s).match(ea)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:O(i.family,t.family),lineHeight:ia(O(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:O(i.weight,t.weight),string:""};return n.string=Ur(n),n}function le(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function na(i,t,e){const{min:s,max:n}=i,o=Tn(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Lt(i,t){return Object.assign(Object.create(i),t)}function Ni(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=Un("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>Ni([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Yn(a,l,()=>ua(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return ps(a).includes(l)},ownKeys(a){return ps(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Jt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:$n(i,s),setContext:o=>Jt(i,o,e,s),override:o=>Jt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Yn(o,r,()=>ra(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function $n(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:Tt(e)?e:()=>e,isIndexable:Tt(s)?s:()=>s}}const oa=(i,t)=>i?i+Ei(t):t,Vi=(i,t)=>L(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Yn(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t))return i[t];const s=e();return i[t]=s,s}function ra(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return Tt(a)&&r.isScriptable(t)&&(a=aa(t,a,i,e)),V(a)&&a.length&&(a=la(t,a,i,r.isIndexable)),Vi(t,a)&&(a=Jt(a,n,o&&o[t],r)),a}function aa(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),Vi(i,l)&&(l=Wi(n._scopes,n,i,l)),l}function la(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(L(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=Wi(c,n,i,h);t.push(Jt(d,o,r&&r[i],a))}}return t}function Xn(i,t,e){return Tt(i)?i(t,e):i}const ca=(i,t)=>i===!0?t:typeof i=="string"?At(t,i):void 0;function ha(i,t,e,s,n){for(const o of t){const r=ca(e,o);if(r){i.add(r);const a=Xn(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function Wi(i,t,e,s){const n=t._rootScopes,o=Xn(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=gs(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=gs(a,r,o,l,s),l===null)?!1:Ni(Array.from(a),[""],n,o,()=>da(t,e,s))}function gs(i,t,e,s,n){for(;e;)e=ha(i,t,e,s,n);return e}function da(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return V(n)&&L(e)?e:n||{}}function ua(i,t,e,s){let n;for(const o of t)if(n=Un(oa(o,i),e),typeof n<"u")return Vi(i,n)?Wi(e,s,i,n):n}function Un(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function ps(i){let t=i._keys;return t||(t=i._keys=fa(i._scopes)),t}function fa(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function Kn(i,t,e,s){const{iScale:n}=i,{key:o="r"}=this._parsing,r=new Array(s);let a,l,c,h;for(a=0,l=s;a<l;++a)c=a+e,h=t[c],r[a]={r:n.parse(At(h,o),c)};return r}const ga=Number.EPSILON||1e-14,Qt=(i,t)=>t<i.length&&!i[t].skip&&i[t],qn=i=>i==="x"?"y":"x";function pa(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=yi(o,n),l=yi(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+u*(r.x-n.x),y:o.y+u*(r.y-n.y)}}}function ma(i,t,e){const s=i.length;let n,o,r,a,l,c=Qt(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Qt(i,h+1),!(!l||!c)){if(ue(t[h],0,ga)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function ba(i,t,e="x"){const s=qn(e),n=i.length;let o,r,a,l=Qt(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Qt(i,c+1),!a)continue;const h=a[e],d=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=d+o*t[c])}}function xa(i,t="x"){const e=qn(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=Qt(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=Qt(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?gt(n[r-1])!==gt(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}ma(i,n,o),ba(i,o,t)}function Ae(i,t,e){return Math.max(Math.min(i,e),t)}function _a(i,t){let e,s,n,o,r,a=kt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&kt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=Ae(n.cp1x,t.left,t.right),n.cp1y=Ae(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=Ae(n.cp2x,t.left,t.right),n.cp2y=Ae(n.cp2y,t.top,t.bottom)))}function ya(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")xa(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=pa(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&_a(i,e)}function Gn(){return typeof window<"u"&&typeof document<"u"}function Hi(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ke(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const ei=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function va(i,t){return ei(i).getPropertyValue(t)}const ka=["top","right","bottom","left"];function Ht(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=ka[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Ma=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Sa(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(Ma(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function Nt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=ei(e),o=n.boxSizing==="border-box",r=Ht(n,"padding"),a=Ht(n,"border","width"),{x:l,y:c,box:h}=Sa(i,e),d=r.left+(h&&a.left),u=r.top+(h&&a.top);let{width:f,height:g}=t;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/g*e.height/s)}}function wa(i,t,e){let s,n;if(t===void 0||e===void 0){const o=Hi(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=ei(o),l=Ht(a,"border","width"),c=Ht(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=Ke(a.maxWidth,o,"clientWidth"),n=Ke(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Xe,maxHeight:n||Xe}}const Te=i=>Math.round(i*10)/10;function Ca(i,t,e,s){const n=ei(i),o=Ht(n,"margin"),r=Ke(n.maxWidth,i,"clientWidth")||Xe,a=Ke(n.maxHeight,i,"clientHeight")||Xe,l=wa(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const u=Ht(n,"border","width"),f=Ht(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=Te(Math.min(c,r,l.maxWidth)),h=Te(Math.min(h,a,l.maxHeight)),c&&!h&&(h=Te(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=Te(Math.floor(h*s))),{width:c,height:h}}function ms(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Pa=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch{}return i}();function bs(i,t){const e=va(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Vt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Da(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function Oa(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=Vt(i,n,e),a=Vt(n,o,e),l=Vt(o,t,e),c=Vt(r,a,e),h=Vt(a,l,e);return Vt(c,h,e)}const Aa=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Ta=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Gt(i,t,e){return i?Aa(t,e):Ta()}function Zn(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function Jn(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function Qn(i){return i==="angle"?{between:xe,compare:Lr,normalize:at}:{between:yt,compare:(t,e)=>t-e,normalize:t=>t}}function xs({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function La(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=Qn(s),l=t.length;let{start:c,end:h,loop:d}=i,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&r(a(t[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function to(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=Qn(s),{start:h,end:d,loop:u,style:f}=La(i,t,e),g=[];let p=!1,m=null,b,x,k;const v=()=>l(n,k,b)&&a(n,k)!==0,_=()=>a(o,b)===0||l(o,k,b),M=()=>p||v(),S=()=>!p||_();for(let w=h,P=h;w<=d;++w)x=t[w%r],!x.skip&&(b=c(x[s]),b!==k&&(p=l(b,n,o),m===null&&M()&&(m=a(b,n)===0?w:P),m!==null&&S()&&(g.push(xs({start:m,end:w,loop:u,count:r,style:f})),m=null),P=w,k=b));return m!==null&&g.push(xs({start:m,end:d,loop:u,count:r,style:f})),g}function eo(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=to(s[n],i.points,t);o.length&&e.push(...o)}return e}function Ra(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function Ea(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function Fa(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=Ra(e,n,o,s);if(s===!0)return _s(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return _s(i,Ea(e,r,l,c),e,t)}function _s(i,t,e,s){return!s||!s.setContext||!e?t:Ia(i,t,e,s)}function Ia(i,t,e,s){const n=i._chart.getContext(),o=ys(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,d=t[0].start,u=d;function f(g,p,m,b){const x=a?-1:1;if(g!==p){for(g+=l;e[g%l].skip;)g-=x;for(;e[p%l].skip;)p+=x;g%l!==p%l&&(c.push({start:g%l,end:p%l,loop:m,style:b}),h=b,d=p%l)}}for(const g of t){d=a?d:g.start;let p=e[d%l],m;for(u=d+1;u<=g.end;u++){const b=e[u%l];m=ys(s.setContext(Lt(n,{type:"segment",p0:p,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),za(m,h)&&f(d,u-1,g.loop,h),p=b,h=m}d<u-1&&f(d,u-1,g.loop,h)}return c}function ys(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function za(i,t){if(!t)return!1;const e=[],s=function(n,o){return Bi(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}/*!
 * Chart.js v4.4.0
 * https://www.chartjs.org
 * (c) 2023 Chart.js Contributors
 * Released under the MIT License
 */class Ba{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=zn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var bt=new Ba;const vs="transparent",ja={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=ds(i||vs),n=s.valid&&ds(t||vs);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Na{constructor(t,e,s,n){const o=e[s];n=le([t.to,n,o,t.from]);const r=le([t.from,o,n]);this._active=!0,this._fn=t.fn||ja[t.type||typeof r],this._easing=fe[t.easing]||fe.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=le([t.to,e,n,t.from]),this._from=le([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class io{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!L(t))return;const e=Object.keys(X.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!L(o))return;const r={};for(const a of e)r[a]=o[a];(V(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=Wa(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Va(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,a);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new Na(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return bt.add(this._chart,s),!0}}function Va(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function Wa(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function ks(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Ha(i,t,e){if(e===!1)return!1;const s=ks(i,e),n=ks(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function $a(i){let t,e,s,n;return L(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function so(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Ms(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t!==null){for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(s.all)continue;break}c=i.values[l],Y(c)&&(o||t===0||gt(t)===gt(c))&&(t+=c)}return t}}function Ya(i){const t=Object.keys(i),e=new Array(t.length);let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],e[s]={x:o,y:i[o]};return e}function Ss(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Xa(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function Ua(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Ka(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function ws(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Cs(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=Xa(o,r,s),d=t.length;let u;for(let f=0;f<d;++f){const g=t[f],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});u=b[c]=Ka(n,h,p),u[a]=m,u._top=ws(u,r,!0,s.type),u._bottom=ws(u,r,!1,s.type);const x=u._visualValues||(u._visualValues={});x[a]=m}}function hi(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function qa(i,t){return Lt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Ga(i,t,e){return Lt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function ee(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const di=i=>i==="reset"||i==="none",Ps=(i,t)=>t?i:Object.assign({},i),Za=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:so(e,!0),values:null};class Rt{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ss(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&ee(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=e.xAxisID=O(s.xAxisID,hi(t,"x")),r=e.yAxisID=O(s.yAxisID,hi(t,"y")),a=e.rAxisID=O(s.rAxisID,hi(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&ls(this._data,this),t._stacked&&ee(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(L(e))this._data=Ya(e);else if(s!==e){if(s){ls(s,this);const n=this._cachedMeta;ee(n),n._parsed=[]}e&&Object.isExtensible(e)&&Ir(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Ss(e.vScale,e),e.stack!==s.stack&&(n=!0,ee(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&Cs(this,e._parsed)}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{V(n[t])?u=this.parseArrayData(s,n,t,e):L(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}r&&Cs(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let u,f,g;for(u=0,f=n;u<f;++u)g=u+s,d[u]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(At(f,a),u),y:r.parse(At(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:so(n,!0),values:e._stacks[t.axis]._visualValues};return Ms(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Ms(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=Za(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Ua(a);let u,f;function g(){f=n[u];const p=f[a.axis];return!Y(f[t.axis])||h>p||d<p}for(u=0;u<r&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=r-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],Y(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=$a(O(this.options.clip,Ha(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Ga(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=qa(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&be(s);if(a)return Ps(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(X.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(u,f,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(Ps(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}const c=new io(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||di(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){di(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!di(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&ee(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function Ja(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=In(s.sort((n,o)=>n-o))}return i._cache.$bar}function Qa(i){const t=i.iScale,e=Ja(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(be(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function tl(i,t,e,s){const n=e.barThickness;let o,r;return R(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function el(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function il(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function no(i,t,e,s){return V(i)?il(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Ds(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,u;for(c=e,h=e+s;c<h;++c)u=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(no(u,d,o,c));return l}function ui(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function sl(i,t,e){return i!==0?gt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function nl(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function ol(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=nl(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Os(h,r,a,l)]=!0,n=c)),o[Os(n,r,a,l)]=!0,i.borderSkipped=o}function Os(i,t,e,s){return s?(i=rl(i,t,e),i=As(i,e,t)):i=As(i,t,e),i}function rl(i,t,e){return i===t?e:i===e?t:i}function As(i,t,e){return i==="start"?t:i==="end"?e:i}function al(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class ll extends Rt{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,s,n){return Ds(t,e,s,n)}parseArrayData(t,e,s,n){return Ds(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let u,f,g,p;for(u=s,f=s+n;u<f;++u)p=e[u],g={},g[o.axis]=o.parse(At(p,c),u),d.push(no(At(p,h),g,r,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=ui(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){const g=this.getParsed(f),p=o||R(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(g._stacks||{})[a.axis],x={horizontal:c,base:p.base,enableBorderRadius:!b||ui(g._custom)||r===b._top||r===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};u&&(x.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));const k=x.options||t[f].options;ol(x,k,b,r),al(x,k,h.ratio),this.updateElement(t[f],f,x,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(l=>l.controller.options.grouped),o=s.options.stacked,r=[],a=l=>{const c=l.controller.getParsed(e),h=c&&c[l.vScale.axis];if(R(h)||isNaN(h))return!0};for(const l of n)if(!(e!==void 0&&a(l))&&((o===!1||r.indexOf(l.stack)===-1||o===void 0&&l.stack===void 0)&&r.push(l.stack),l.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||Qa(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=ui(c);let d=l[e.axis],u=0,f=s?this.applyStack(e,l,s):d,g,p;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&gt(d)!==gt(c.barEnd)&&(u=0),u+=d);const m=!R(o)&&!h?o:u;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(u+f):g=b,p=g-b,Math.abs(p)<r){p=sl(p,e,a)*r,d===a&&(b-=p/2);const x=e.getPixelForDecimal(0),k=e.getPixelForDecimal(1),v=Math.min(x,k),_=Math.max(x,k);b=Math.max(Math.min(b,_),v),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const x=gt(p)*e.getLineWidthForValue(a)/2;b+=x,p-=x}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=O(n.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?el(t,e,n,c):tl(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&s[o].draw(this._ctx)}}class cl extends Rt{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,s,n){const o=super.parsePrimitiveData(t,e,s,n);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+s).radius;return o}parseArrayData(t,e,s,n){const o=super.parseArrayData(t,e,s,n);for(let r=0;r<o.length;r++){const a=e[s+r];o[r]._custom=O(a[2],this.resolveDataElementOptions(r+s).radius)}return o}parseObjectData(t,e,s,n){const o=super.parseObjectData(t,e,s,n);for(let r=0;r<o.length;r++){const a=e[s+r];o[r]._custom=O(a&&a.r&&+a.r,this.resolveDataElementOptions(r+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let s=t.length-1;s>=0;--s)e=Math.max(e,t[s].size(this.resolveDataElementOptions(s))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,r=this.getParsed(t),a=n.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:s[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=r.axis,d=a.axis;for(let u=e;u<e+s;u++){const f=t[u],g=!o&&this.getParsed(u),p={},m=p[h]=o?r.getPixelForDecimal(.5):r.getPixelForValue(g[h]),b=p[d]=o?a.getBasePixel():a.getPixelForValue(g[d]);p.skip=isNaN(m)||isNaN(b),c&&(p.options=l||this.resolveDataElementOptions(u,f.active?"active":n),o&&(p.options.radius=0)),this.updateElement(f,u,p,n)}}resolveDataElementOptions(t,e){const s=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const o=n.radius;return e!=="active"&&(n.radius=0),n.radius+=O(s&&s._custom,o),n}}function hl(i,t,e){let s=1,n=1,o=0,r=0;if(t<j){const a=i,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),u=Math.sin(l),f=(k,v,_)=>xe(k,a,l,!0)?1:Math.max(v,v*e,_,_*e),g=(k,v,_)=>xe(k,a,l,!0)?-1:Math.min(v,v*e,_,_*e),p=f(0,c,d),m=f(U,h,u),b=g(N,c,d),x=g(N+U,h,u);s=(p-b)/2,n=(m-x)/2,o=-(p+b)/2,r=-(m+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}class $i extends Rt{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(L(s[t])){const{key:l="value"}=this._parsing;o=c=>+At(s[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return ht(this.options.rotation-90)}_getCircumference(){return ht(this.options.circumference)}_getRotationExtents(){let t=j,e=-j;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(kr(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:g,offsetY:p}=hl(d,h,l),m=(s.width-r)/u,b=(s.height-r)/f,x=Math.max(Math.min(m,b)/2,0),k=Tn(this.options.radius,x),v=Math.max(k*l,0),_=(k-v)/this._getVisibleDatasetWeightTotal();this.offsetX=g*k,this.offsetY=p*k,n.total=this.calculateTotal(),this.outerRadius=k-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/j)}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),x;for(x=0;x<e;++x)b+=this._circumference(x,o);for(x=e;x<e+s;++x){const k=this._circumference(x,o),v=t[x],_={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+k,circumference:k,outerRadius:g,innerRadius:f};m&&(_.options=p||this.resolveDataElementOptions(x,v.active?"active":n)),b+=k,this.updateElement(v,x,_,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?j*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Me(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,r,a,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){r=s.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(O(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class oo extends Rt{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,r=this.chart._animationsDisabled;let{start:a,count:l}=jn(e,n,r);this._drawStart=a,this._drawCount=l,Nn(e)&&(a=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),u=r.axis,f=a.axis,{spanGaps:g,segment:p}=this.options,m=Zt(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",x=e+s,k=t.length;let v=e>0&&this.getParsed(e-1);for(let _=0;_<k;++_){const M=t[_],S=b?M:{};if(_<e||_>=x){S.skip=!0;continue}const w=this.getParsed(_),P=R(w[f]),A=S[u]=r.getPixelForValue(w[u],_),D=S[f]=o||P?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,w,l):w[f],_);S.skip=isNaN(A)||isNaN(D)||P,S.stop=_>0&&Math.abs(w[u]-v[u])>m,p&&(S.parsed=w,S.raw=c.data[_]),d&&(S.options=h||this.resolveDataElementOptions(_,M.active?"active":n)),b||this.updateElement(M,_,S,n),v=w}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class ro extends Rt{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Me(e._parsed[t].r,s.options.locale);return{label:n[t]||"",value:o}}parseObjectData(t,e,s,n){return Kn.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,n)=>{const o=this.getParsed(n).r;!isNaN(o)&&this.chart.getDataVisibility(n)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,s=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(n/2,0),r=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,d=c.yCenter,u=c.getIndexAngle(0)-.5*N;let f=u,g;const p=360/this.countVisibleElements();for(g=0;g<e;++g)f+=this._computeAngle(g,n,p);for(g=e;g<e+s;g++){const m=t[g];let b=f,x=f+this._computeAngle(g,n,p),k=r.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;f=x,o&&(l.animateScale&&(k=0),l.animateRotate&&(b=x=u));const v={x:h,y:d,innerRadius:0,outerRadius:k,startAngle:b,endAngle:x,options:this.resolveDataElementOptions(g,m.active?"active":n)};this.updateElement(m,g,v,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((s,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,s){return this.chart.getDataVisibility(t)?ht(this.resolveDataElementOptions(t,e).angle||s):0}}class ao extends $i{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class dl extends Rt{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const e=this._cachedMeta.vScale,s=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(s[e.axis])}}parseObjectData(t,e,s,n){return Kn.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta,s=e.dataset,n=e.data||[],o=e.iScale.getLabels();if(s.points=n,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===n.length,options:r};this.updateElement(s,void 0,a,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,s,n){const o=this._cachedMeta.rScale,r=n==="reset";for(let a=e;a<e+s;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":n),h=o.getPointPositionForValue(a,this.getParsed(a).r),d=r?o.xCenter:h.x,u=r?o.yCenter:h.y,f={x:d,y:u,angle:h.angle,skip:isNaN(d)||isNaN(u),options:c};this.updateElement(l,a,f,n)}}}class ul extends Rt{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,r=this.getParsed(t),a=n.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:s[t]||"",value:"("+a+", "+l+")"}}update(t){const e=this._cachedMeta,{data:s=[]}=e,n=this.chart._animationsDisabled;let{start:o,count:r}=jn(e,s,n);if(this._drawStart=o,this._drawCount=r,Nn(e)&&(o=0,r=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(s,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),d=this.getSharedOptions(h),u=this.includeOptions(n,d),f=r.axis,g=a.axis,{spanGaps:p,segment:m}=this.options,b=Zt(p)?p:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||o||n==="none";let k=e>0&&this.getParsed(e-1);for(let v=e;v<e+s;++v){const _=t[v],M=this.getParsed(v),S=x?_:{},w=R(M[g]),P=S[f]=r.getPixelForValue(M[f],v),A=S[g]=o||w?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,M,l):M[g],v);S.skip=isNaN(P)||isNaN(A)||w,S.stop=v>0&&Math.abs(M[f]-k[f])>b,m&&(S.parsed=M,S.raw=c.data[v]),u&&(S.options=d||this.resolveDataElementOptions(v,_.active?"active":n)),x||this.updateElement(_,v,S,n),k=M}this.updateSharedOptions(d,n,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const s=t.dataset,n=s.options&&s.options.borderWidth||0;if(!e.length)return n;const o=e[0].size(this.resolveDataElementOptions(0)),r=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,o,r)/2}}var fl=Object.freeze({__proto__:null,BarController:ll,BubbleController:cl,DoughnutController:$i,LineController:oo,PieController:ao,PolarAreaController:ro,RadarController:dl,ScatterController:ul});function Bt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Yi{static override(t){Object.assign(Yi.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return Bt()}parse(){return Bt()}format(){return Bt()}add(){return Bt()}diff(){return Bt()}startOf(){return Bt()}endOf(){return Bt()}}var gl={_date:Yi};function pl(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const l=a._reversePixels?Er:vt;if(s){if(n._sharedOptions){const c=o[0],h=typeof c.getRange=="function"&&c.getRange(t);if(h){const d=l(o,t,e-h),u=l(o,t,e+h);return{lo:d.lo,hi:u.hi}}}}else return l(o,t,e)}return{lo:0,hi:o.length-1}}function ii(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:u}=pl(o[a],t,r,n);for(let f=d;f<=u;++f){const g=h[f];g.skip||s(g,c,f)}}}function ml(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function fi(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||ii(i,e,t,function(a,l,c){!n&&!kt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function bl(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=En(r,{x:t.x,y:t.y});xe(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return ii(i,e,t,o),n}function xl(i,t,e,s,n,o){let r=[];const a=ml(e);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!f)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:u})}return ii(i,e,t,c),r}function gi(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?bl(i,t,e,n):xl(i,t,e,s,n,o)}function Ts(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return ii(i,e,t,(l,c,h)=>{l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var _l={modes:{index(i,t,e,s){const n=Nt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?fi(i,n,o,s,r):gi(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=Nt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?fi(i,n,o,s,r):gi(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=Nt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return fi(i,n,o,s,r)},nearest(i,t,e,s){const n=Nt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return gi(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=Nt(t,i);return Ts(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=Nt(t,i);return Ts(i,n,"y",e.intersect,s)}}};const lo=["left","top","right","bottom"];function ie(i,t){return i.filter(e=>e.pos===t)}function Ls(i,t){return i.filter(e=>lo.indexOf(e.pos)===-1&&e.box.axis===t)}function se(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function yl(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function vl(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!lo.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function kl(i,t){const e=vl(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function Ml(i){const t=yl(i),e=se(t.filter(c=>c.box.fullSize),!0),s=se(ie(t,"left"),!0),n=se(ie(t,"right")),o=se(ie(t,"top"),!0),r=se(ie(t,"bottom")),a=Ls(t,"x"),l=Ls(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:ie(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Rs(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function co(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Sl(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!L(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&co(r,o.getPadding());const a=Math.max(0,t.outerWidth-Rs(r,i,"left","right")),l=Math.max(0,t.outerHeight-Rs(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function wl(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Cl(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function ce(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Cl(a.horizontal,t));const{same:d,other:u}=Sl(t,e,a,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(a)}return c&&ce(n,t,e,s)||h}function Le(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Es(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,u=c.size||l.height;be(c.start)&&(r=c.start),l.fullSize?Le(l,n.left,r,e.outerWidth-n.right-n.left,u):Le(l,t.left+c.placed,r,d,u),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,u=c.size||l.width;be(c.start)&&(o=c.start),l.fullSize?Le(l,o,n.top,u,e.outerHeight-n.bottom-n.top):Le(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var et={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=it(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Ml(i.boxes),l=a.vertical,c=a.horizontal;F(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),u=Object.assign({},n);co(u,it(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),g=kl(l.concat(c),d);ce(a.fullSize,f,d,g),ce(l,f,d,g),ce(c,f,d,g)&&ce(l,f,d,g),wl(f),Es(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Es(a.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},F(a.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class ho{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Pl extends ho{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const We="$chartjs",Dl={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Fs=i=>i===null||i==="";function Ol(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[We]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Fs(n)){const o=bs(i,"width");o!==void 0&&(i.width=o)}if(Fs(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=bs(i,"height");o!==void 0&&(i.height=o)}return i}const uo=Pa?{passive:!0}:!1;function Al(i,t,e){i.addEventListener(t,e,uo)}function Tl(i,t,e){i.canvas.removeEventListener(t,e,uo)}function Ll(i,t){const e=Dl[i.type]||i.type,{x:s,y:n}=Nt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function qe(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Rl(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||qe(a.addedNodes,s),r=r&&!qe(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function El(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||qe(a.removedNodes,s),r=r&&!qe(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const ye=new Map;let Is=0;function fo(){const i=window.devicePixelRatio;i!==Is&&(Is=i,ye.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Fl(i,t){ye.size||window.addEventListener("resize",fo),ye.set(i,t)}function Il(i){ye.delete(i),ye.size||window.removeEventListener("resize",fo)}function zl(i,t,e){const s=i.canvas,n=s&&Hi(s);if(!n)return;const o=Bn((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),Fl(i,o),r}function pi(i,t,e){e&&e.disconnect(),t==="resize"&&Il(i)}function Bl(i,t,e){const s=i.canvas,n=Bn(o=>{i.ctx!==null&&e(Ll(o,i))},i);return Al(s,t,n),n}class jl extends ho{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Ol(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[We])return!1;const s=e[We].initial;["height","width"].forEach(o=>{const r=s[o];R(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[We],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Rl,detach:El,resize:zl}[e]||Bl;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:pi,detach:pi,resize:pi}[e]||Tl)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Ca(t,e,s,n)}isAttached(t){const e=Hi(t);return!!(e&&e.isConnected)}}function Nl(i){return!Gn()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Pl:jl}class Mt{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Zt(this.x)&&Zt(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}function Vl(i,t){const e=i.options.ticks,s=Wl(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?$l(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return Yl(t,c,o,r/n),c;const h=Hl(o,t,n);if(r>0){let d,u;const f=r>1?Math.round((l-a)/(r-1)):null;for(Re(t,c,h,R(f)?0:a-f,a),d=0,u=r-1;d<u;d++)Re(t,c,h,o[d],o[d+1]);return Re(t,c,h,l,R(f)?t.length:l+f),c}return Re(t,c,h),c}function Wl(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Hl(i,t,e){const s=Xl(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Ar(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function $l(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function Yl(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function Re(i,t,e,s,n){const o=O(s,0),r=Math.min(O(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function Xl(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const Ul=i=>i==="left"?"right":i==="right"?"left":i,zs=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Bs=(i,t)=>Math.min(t||i,i);function js(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function Kl(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function ql(i,t){F(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function ne(i){return i.drawTicks?i.tickLength:0}function Ns(i,t){if(!i.display)return 0;const e=G(i.font,t),s=it(i.padding);return(V(i.text)?i.text.length:1)*e.lineHeight+s.height}function Gl(i,t){return Lt(i,{scale:t,type:"scale"})}function Zl(i,t,e){return Lt(i,{tick:e,index:t,type:"tick"})}function Jl(i,t,e){let s=zi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=Ul(s)),s}function Ql(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,u,f,g;const p=r-n,m=a-o;if(i.isHorizontal()){if(f=tt(s,o,a),L(e)){const b=Object.keys(e)[0],x=e[b];g=h[b].getPixelForValue(x)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=zs(i,e,t);u=a-o}else{if(L(e)){const b=Object.keys(e)[0],x=e[b];f=h[b].getPixelForValue(x)-m+t}else e==="center"?f=(c.left+c.right)/2-m+t:f=zs(i,e,t);g=tt(s,r,n),d=e==="left"?-U:U}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}class Xt extends Mt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=rt(t,Number.POSITIVE_INFINITY),e=rt(e,Number.NEGATIVE_INFINITY),s=rt(s,Number.POSITIVE_INFINITY),n=rt(n,Number.NEGATIVE_INFINITY),{min:rt(t,s),max:rt(e,n),minDefined:Y(t),maxDefined:Y(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:rt(e,rt(s,e)),max:rt(s,rt(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){B(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=na(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?js(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Vl(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){B(this.options.afterUpdate,[this])}beforeSetDimensions(){B(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){B(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),B(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){B(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=B(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){B(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){B(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Bs(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=J(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:f/(s-1),d+6>a&&(a=f/(s-(t.offset?.5:1)),l=this.maxHeight-ne(t.grid)-e.padding-Ns(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),r=Fi(Math.min(Math.asin(J((h.highest.height+6)/a,-1,1)),Math.asin(J(l/c,-1,1))-Math.asin(J(u/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){B(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){B(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Ns(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=ne(o)+l):(t.height=this.maxHeight,t.width=ne(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=ht(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=s.mirror?0:m*d.width+p*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:p*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){B(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)R(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=js(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Bs(e,s));let c=0,h=0,d,u,f,g,p,m,b,x,k,v,_;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},x=p.lineHeight,k=v=0,!R(g)&&!V(g))k=Ue(n,b.data,b.gc,k,g),v=x;else if(V(g))for(u=0,f=g.length;u<f;++u)_=g[u],!R(_)&&!V(_)&&(k=Ue(n,b.data,b.gc,k,_),v+=x);r.push(k),a.push(v),c=Math.max(k,c),h=Math.max(v,h)}ql(o,e);const M=r.indexOf(c),S=a.indexOf(h),w=P=>({width:r[P]||0,height:a[P]||0});return{first:w(0),last:w(e-1),widest:w(M),highest:w(S),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Rr(this._alignToPixels?zt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Zl(this.getContext(),t,s))}return this.$context||(this.$context=Gl(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=ht(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=ne(o),f=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(z){return zt(s,z,p)};let x,k,v,_,M,S,w,P,A,D,T,I;if(r==="top")x=b(this.bottom),S=this.bottom-u,P=x-m,D=b(t.top)+m,I=t.bottom;else if(r==="bottom")x=b(this.top),D=t.top,I=b(t.bottom)-m,S=x+m,P=this.top+u;else if(r==="left")x=b(this.right),M=this.right-u,w=x-m,A=b(t.left)+m,T=t.right;else if(r==="right")x=b(this.left),A=t.left,T=b(t.right)-m,M=x+m,w=this.left+u;else if(e==="x"){if(r==="center")x=b((t.top+t.bottom)/2+.5);else if(L(r)){const z=Object.keys(r)[0],H=r[z];x=b(this.chart.scales[z].getPixelForValue(H))}D=t.top,I=t.bottom,S=x+m,P=S+u}else if(e==="y"){if(r==="center")x=b((t.left+t.right)/2);else if(L(r)){const z=Object.keys(r)[0],H=r[z];x=b(this.chart.scales[z].getPixelForValue(H))}M=x-m,w=M-u,A=t.left,T=t.right}const K=O(n.ticks.maxTicksLimit,d),E=Math.max(1,Math.ceil(d/K));for(k=0;k<d;k+=E){const z=this.getContext(k),H=o.setContext(z),ot=a.setContext(z),Q=H.lineWidth,St=H.color,te=ot.dash||[],ct=ot.dashOffset,dt=H.tickWidth,pt=H.tickColor,Et=H.tickBorderDash||[],Ft=H.tickBorderDashOffset;v=Kl(this,k,l),v!==void 0&&(_=zt(s,v,Q),c?M=w=A=T=_:S=P=D=I=_,f.push({tx1:M,ty1:S,tx2:w,ty2:P,x1:A,y1:D,x2:T,y2:I,width:Q,color:St,borderDash:te,borderDashOffset:ct,tickWidth:dt,tickColor:pt,tickBorderDash:Et,tickBorderDashOffset:Ft}))}return this._ticksLength=d,this._borderValue=x,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=ne(s.grid),f=u+h,g=d?-h:f,p=-ht(this.labelRotation),m=[];let b,x,k,v,_,M,S,w,P,A,D,T,I="middle";if(n==="top")M=this.bottom-g,S=this._getXAxisLabelAlignment();else if(n==="bottom")M=this.top+g,S=this._getXAxisLabelAlignment();else if(n==="left"){const E=this._getYAxisLabelAlignment(u);S=E.textAlign,_=E.x}else if(n==="right"){const E=this._getYAxisLabelAlignment(u);S=E.textAlign,_=E.x}else if(e==="x"){if(n==="center")M=(t.top+t.bottom)/2+f;else if(L(n)){const E=Object.keys(n)[0],z=n[E];M=this.chart.scales[E].getPixelForValue(z)+f}S=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")_=(t.left+t.right)/2-f;else if(L(n)){const E=Object.keys(n)[0],z=n[E];_=this.chart.scales[E].getPixelForValue(z)}S=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?I="top":l==="end"&&(I="bottom"));const K=this._getLabelSizes();for(b=0,x=a.length;b<x;++b){k=a[b],v=k.label;const E=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),A=P.lineHeight,D=V(v)?v.length:1;const z=D/2,H=E.color,ot=E.textStrokeColor,Q=E.textStrokeWidth;let St=S;r?(_=w,S==="inner"&&(b===x-1?St=this.options.reverse?"left":"right":b===0?St=this.options.reverse?"right":"left":St="center"),n==="top"?c==="near"||p!==0?T=-D*A+A/2:c==="center"?T=-K.highest.height/2-z*A+A:T=-K.highest.height+A/2:c==="near"||p!==0?T=A/2:c==="center"?T=K.highest.height/2-z*A:T=K.highest.height-D*A,d&&(T*=-1),p!==0&&!E.showLabelBackdrop&&(_+=A/2*Math.sin(p))):(M=w,T=(1-D)*A/2);let te;if(E.showLabelBackdrop){const ct=it(E.backdropPadding),dt=K.heights[b],pt=K.widths[b];let Et=T-ct.top,Ft=0-ct.left;switch(I){case"middle":Et-=dt/2;break;case"bottom":Et-=dt;break}switch(S){case"center":Ft-=pt/2;break;case"right":Ft-=pt;break}te={left:Ft,top:Et,width:pt+ct.width,height:dt+ct.height,color:E.backdropColor}}m.push({label:v,font:P,textOffset:T,options:{rotation:p,color:H,strokeColor:ot,strokeWidth:Q,textAlign:St,textBaseline:I,translation:[_,M],backdrop:te}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-ht(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=zt(t,this.left,r)-r/2,h=zt(t,this.right,a)+a/2,d=u=l):(d=zt(t,this.top,r)-r/2,u=zt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Qe(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Yt(s,c,0,h,l,a)}n&&ti(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=G(s.font),r=it(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||L(e)?(l+=r.bottom,V(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=Ql(this,l,e,a);Yt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:Jl(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=O(t.grid&&t.grid.z,-1),n=O(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Xt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return G(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Ee{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;ic(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,tc(t,r,s),this.override&&X.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in X[n]&&(delete X[n][s],this.override&&delete $t[s])}}function tc(i,t,e){const s=me(Object.create(null),[e?X.get(e):{},X.get(t),i.defaults]);X.set(t,s),i.defaultRoutes&&ec(t,i.defaultRoutes),i.descriptors&&X.describe(t,i.descriptors)}function ec(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");X.route(o,n,l,a)})}function ic(i){return"id"in i&&"defaults"in i}class sc{constructor(){this.controllers=new Ee(Rt,"datasets",!0),this.elements=new Ee(Mt,"elements"),this.plugins=new Ee(Object,"plugins"),this.scales=new Ee(Xt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):F(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=Ei(t);B(s["before"+n],[],s),e[t](s),B(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var ft=new sc;class nc{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(B(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){R(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=O(s.options&&s.options.plugins,{}),o=oc(s);return n===!1&&!e?[]:ac(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function oc(i){const t={},e=[],s=Object.keys(ft.plugins.items);for(let o=0;o<s.length;o++)e.push(ft.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function rc(i,t){return!t&&i===!1?null:i===!0?{}:i}function ac(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=rc(s[l],n);c!==null&&o.push({plugin:a,options:lc(i.config,{plugin:a,local:e[l]},c,r)})}return o}function lc(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Mi(i,t){const e=X.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function cc(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function hc(i,t){return i===t?"_index_":"_value_"}function Vs(i){if(i==="x"||i==="y"||i==="r")return i}function dc(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Si(i,...t){if(Vs(i))return i;for(const e of t){const s=e.axis||dc(e.position)||i.length>1&&Vs(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Ws(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function uc(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return Ws(i,"x",e[0])||Ws(i,"y",e[0])}return{}}function fc(i,t){const e=$t[i.type]||{scales:{}},s=t.scales||{},n=Mi(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!L(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Si(r,a,uc(r,i),X.scales[a.type]),c=hc(l,n),h=e.scales||{};o[r]=de(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||Mi(a,t),h=($t[a]||{}).scales||{};Object.keys(h).forEach(d=>{const u=cc(d,l),f=r[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),de(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];de(a,[X.scales[a.type],X.scale])}),o}function go(i){const t=i.options||(i.options={});t.plugins=O(t.plugins,{}),t.scales=fc(i,t)}function po(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function gc(i){return i=i||{},i.data=po(i.data),go(i),i}const Hs=new Map,mo=new Set;function Fe(i,t){let e=Hs.get(i);return e||(e=t(),Hs.set(i,e),mo.add(e)),e}const oe=(i,t,e)=>{const s=At(t,e);s!==void 0&&i.add(s)};class pc{constructor(t){this._config=gc(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=po(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),go(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Fe(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return Fe(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return Fe(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return Fe(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>oe(l,t,d))),h.forEach(d=>oe(l,n,d)),h.forEach(d=>oe(l,$t[o]||{},d)),h.forEach(d=>oe(l,X,d)),h.forEach(d=>oe(l,vi,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),mo.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,$t[e]||{},X.datasets[e]||{},{type:e},X,vi]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=$s(this._resolverCache,t,n);let l=r;if(bc(r,e)){o.$shared=!1,s=Tt(s)?s():s;const c=this.createResolver(t,s,a);l=Jt(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=$s(this._resolverCache,t,s);return L(e)?Jt(o,e,void 0,n):o}}function $s(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:Ni(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const mc=i=>L(i)&&Object.getOwnPropertyNames(i).reduce((t,e)=>t||Tt(i[e]),!1);function bc(i,t){const{isScriptable:e,isIndexable:s}=$n(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(Tt(a)||mc(a))||r&&V(a))return!0}return!1}var xc="4.4.0";const _c=["top","bottom","left","right","chartArea"];function Ys(i,t){return i==="top"||i==="bottom"||_c.indexOf(i)===-1&&t==="x"}function Xs(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function Us(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),B(e&&e.onComplete,[i],t)}function yc(i){const t=i.chart,e=t.options.animation;B(e&&e.onProgress,[i],t)}function bo(i){return Gn()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const He={},Ks=i=>{const t=bo(i);return Object.values(He).filter(e=>e.canvas===t).pop()};function vc(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function kc(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function Ie(i,t,e){return i.options.clip?i[e]:t[e]}function Mc(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:Ie(e,t,"left"),right:Ie(e,t,"right"),top:Ie(s,t,"top"),bottom:Ie(s,t,"bottom")}:t}let si=class{static defaults=X;static instances=He;static overrides=$t;static registry=ft;static version=xc;static getChart=Ks;static register(...t){ft.add(...t),qs()}static unregister(...t){ft.remove(...t),qs()}constructor(t,e){const s=this.config=new pc(e),n=bo(t),o=Ks(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Nl(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=vr(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new nc,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=zr(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],He[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}bt.listen(this,"complete",Us),bt.listen(this,"progress",yc),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return R(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return ft}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():ms(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return fs(this.canvas,this.ctx),this}stop(){return bt.stop(this),this}resize(t,e){bt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,ms(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),B(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};F(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=Si(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),F(o,r=>{const a=r.options,l=a.id,c=Si(l,a),h=O(a.type,r.dtype);(a.position===void 0||Ys(a.position,c)!==Ys(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const u=ft.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),F(n,(r,a)=>{r||delete s[a]}),F(s,r=>{et.configure(this,r,r.options),et.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(Xs("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||Mi(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=ft.getController(a),{datasetElementType:c,dataElementType:h}=X.datasets[a];Object.assign(l,{dataElementType:ft.getElement(h),datasetElementType:c&&ft.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){F(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||F(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Xs("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){F(this.scales,t=>{et.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!ns(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;vc(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!ns(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;et.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],F(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,Tt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(bt.has(this)?this.attached&&!bt.running(this)&&bt.start(this):(this.draw(),Us({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resize(s,n),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=Mc(t,this.chartArea),r={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(n&&Qe(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&ti(e),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(t){return kt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=_l.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Lt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);be(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),bt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),fs(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete He[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};F(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){F(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},F(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!$e(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=Pr(t),c=kc(t,this._lastEvent,s,l);s&&(this._lastEvent=null,B(o.onHover,[t,a,this],this),l&&B(o.onClick,[t,a,this],this));const h=!$e(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}};function qs(){return F(si.instances,i=>i._plugins.invalidate())}function Sc(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=n/a;i.beginPath(),i.arc(o,r,a,s-c,e+c),l>n?(c=n/l,i.arc(o,r,l,e+c,s-c,!0)):i.arc(o,r,n,e+U,s-U),i.closePath(),i.clip()}function wc(i){return ji(i,["outerStart","outerEnd","innerStart","innerEnd"])}function Cc(i,t,e,s){const n=wc(i.options.borderRadius),o=(e-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(e-Math.min(o,l))*s/2;return J(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:J(n.innerStart,0,r),innerEnd:J(n.innerEnd,0,r)}}function Kt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function Ge(i,t,e,s,n,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),u=h>0?h+s+e+c:0;let f=0;const g=n-l;if(s){const E=h>0?h-s:0,z=d>0?d-s:0,H=(E+z)/2,ot=H!==0?g*H/(H+s):g;f=(g-ot)/2}const p=Math.max(.001,g*d-e/N)/d,m=(g-p)/2,b=l+m+f,x=n-m-f,{outerStart:k,outerEnd:v,innerStart:_,innerEnd:M}=Cc(t,u,d,x-b),S=d-k,w=d-v,P=b+k/S,A=x-v/w,D=u+_,T=u+M,I=b+_/D,K=x-M/T;if(i.beginPath(),o){const E=(P+A)/2;if(i.arc(r,a,d,P,E),i.arc(r,a,d,E,A),v>0){const Q=Kt(w,A,r,a);i.arc(Q.x,Q.y,v,A,x+U)}const z=Kt(T,x,r,a);if(i.lineTo(z.x,z.y),M>0){const Q=Kt(T,K,r,a);i.arc(Q.x,Q.y,M,x+U,K+Math.PI)}const H=(x-M/u+(b+_/u))/2;if(i.arc(r,a,u,x-M/u,H,!0),i.arc(r,a,u,H,b+_/u,!0),_>0){const Q=Kt(D,I,r,a);i.arc(Q.x,Q.y,_,I+Math.PI,b-U)}const ot=Kt(S,b,r,a);if(i.lineTo(ot.x,ot.y),k>0){const Q=Kt(S,P,r,a);i.arc(Q.x,Q.y,k,b-U,P)}}else{i.moveTo(r,a);const E=Math.cos(P)*d+r,z=Math.sin(P)*d+a;i.lineTo(E,z);const H=Math.cos(A)*d+r,ot=Math.sin(A)*d+a;i.lineTo(H,ot)}i.closePath()}function Pc(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Ge(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(a)||(l=r+(a%j||j))}return Ge(i,t,e,s,l,n),i.fill(),l}function Dc(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,f?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){Ge(i,t,e,s,g,n);for(let p=0;p<o;++p)i.stroke();isNaN(a)||(g=r+(a%j||j))}f&&Sc(i,t,g),o||(Ge(i,t,e,s,g,n),i.stroke())}class Oc extends Mt{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.getProps(["x","y"],s),{angle:o,distance:r}=En(n,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),u=(this.options.spacing+this.options.borderWidth)/2,g=O(d,l-a)>=j||xe(o,a,l),p=yt(r,c+u,h+u);return g&&p}getCenterPoint(t){const{x:e,y:s,startAngle:n,endAngle:o,innerRadius:r,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(r+a+c+l)/2;return{x:e+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:s}=this,n=(e.offset||0)/4,o=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=s>j?Math.floor(s/j):0,s===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*n,Math.sin(a)*n);const l=1-Math.sin(Math.min(N,s||0)),c=n*l;t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,Pc(t,this,c,o,r),Dc(t,this,c,o,r),t.restore()}}function xo(i,t,e=t){i.lineCap=O(e.borderCapStyle,t.borderCapStyle),i.setLineDash(O(e.borderDash,t.borderDash)),i.lineDashOffset=O(e.borderDashOffset,t.borderDashOffset),i.lineJoin=O(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=O(e.borderWidth,t.borderWidth),i.strokeStyle=O(e.borderColor,t.borderColor)}function Ac(i,t,e){i.lineTo(e.x,e.y)}function Tc(i){return i.stepped?qr:i.tension||i.cubicInterpolationMode==="monotone"?Gr:Ac}function _o(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function Lc(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=_o(n,e,s),h=Tc(o);let{move:d=!0,reverse:u}=s||{},f,g,p;for(f=0;f<=c;++f)g=n[(a+(u?c-f:f))%r],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,p,g,u,o.stepped),p=g);return l&&(g=n[(a+(u?c:0))%r],h(i,p,g,u,o.stepped)),!!l}function Rc(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=_o(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,u,f,g,p,m,b;const x=v=>(r+(c?a-v:v))%o,k=()=>{p!==m&&(i.lineTo(h,m),i.lineTo(h,p),i.lineTo(h,b))};for(l&&(f=n[x(0)],i.moveTo(f.x,f.y)),u=0;u<=a;++u){if(f=n[x(u)],f.skip)continue;const v=f.x,_=f.y,M=v|0;M===g?(_<p?p=_:_>m&&(m=_),h=(d*h+v)/++d):(k(),i.lineTo(v,_),g=M,d=0,p=m=_),b=_}k()}function wi(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Rc:Lc}function Ec(i){return i.stepped?Da:i.tension||i.cubicInterpolationMode==="monotone"?Oa:Vt}function Fc(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),xo(i,t.options),i.stroke(n)}function Ic(i,t,e,s){const{segments:n,options:o}=t,r=wi(t);for(const a of n)xo(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const zc=typeof Path2D=="function";function Bc(i,t,e,s){zc&&!t.options.segment?Fc(i,t,e,s):Ic(i,t,e,s)}class ni extends Mt{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;ya(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Fa(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=eo(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=Ec(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:u}=r[c],f=o[d],g=o[u];if(f===g){a.push(f);continue}const p=Math.abs((n-f[e])/(g[e]-f[e])),m=l(f,g,p,s.stepped);m[e]=t[e],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,e,s){return wi(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=wi(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Bc(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function Gs(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class jc extends Mt{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.options,{x:o,y:r}=this.getProps(["x","y"],s);return Math.pow(t-o,2)+Math.pow(e-r,2)<Math.pow(n.hitRadius+n.radius,2)}inXRange(t,e){return Gs(this,t,"x",e)}inYRange(t,e){return Gs(this,t,"y",e)}getCenterPoint(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);const s=e&&t.borderWidth||0;return(e+s)*2}draw(t,e){const s=this.options;this.skip||s.radius<.1||!kt(this,e,this.size(s)/2)||(t.strokeStyle=s.borderColor,t.lineWidth=s.borderWidth,t.fillStyle=s.backgroundColor,ki(t,s,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function yo(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function Dt(i,t,e,s){return i?0:J(t,e,s)}function Nc(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Hn(s);return{t:Dt(n.top,o.top,0,e),r:Dt(n.right,o.right,0,t),b:Dt(n.bottom,o.bottom,0,e),l:Dt(n.left,o.left,0,t)}}function Vc(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Wt(n),r=Math.min(t,e),a=i.borderSkipped,l=s||L(n);return{topLeft:Dt(!l||a.top||a.left,o.topLeft,0,r),topRight:Dt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:Dt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:Dt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Wc(i){const t=yo(i),e=t.right-t.left,s=t.bottom-t.top,n=Nc(i,e/2,s/2),o=Vc(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function mi(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&yo(i,s);return a&&(n||yt(t,a.left,a.right))&&(o||yt(e,a.top,a.bottom))}function Hc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function $c(i,t){i.rect(t.x,t.y,t.w,t.h)}function bi(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class Yc extends Mt{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=Wc(this),a=Hc(r.radius)?_e:$c;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,bi(r,e,o)),t.clip(),a(t,bi(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,bi(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return mi(this,t,e,s)}inXRange(t,e){return mi(this,t,null,e)}inYRange(t,e){return mi(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}var Xc=Object.freeze({__proto__:null,ArcElement:Oc,BarElement:Yc,LineElement:ni,PointElement:jc});const Ci=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Zs=Ci.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function vo(i){return Ci[i%Ci.length]}function ko(i){return Zs[i%Zs.length]}function Uc(i,t){return i.borderColor=vo(t),i.backgroundColor=ko(t),++t}function Kc(i,t){return i.backgroundColor=i.data.map(()=>vo(t++)),t}function qc(i,t){return i.backgroundColor=i.data.map(()=>ko(t++)),t}function Gc(i){let t=0;return(e,s)=>{const n=i.getDatasetMeta(s).controller;n instanceof $i?t=Kc(e,t):n instanceof ro?t=qc(e,t):n&&(t=Uc(e,t))}}function Js(i){let t;for(t in i)if(i[t].borderColor||i[t].backgroundColor)return!0;return!1}function Zc(i){return i&&(i.borderColor||i.backgroundColor)}var Jc={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,t,e){if(!e.enabled)return;const{data:{datasets:s},options:n}=i.config,{elements:o}=n;if(!e.forceOverride&&(Js(s)||Zc(n)||o&&Js(o)))return;const r=Gc(i);s.forEach(r)}};function Qc(i,t,e,s,n){const o=n.samples||s;if(o>=e)return i.slice(t,t+e);const r=[],a=(e-2)/(o-2);let l=0;const c=t+e-1;let h=t,d,u,f,g,p;for(r[l++]=i[h],d=0;d<o-2;d++){let m=0,b=0,x;const k=Math.floor((d+1)*a)+1+t,v=Math.min(Math.floor((d+2)*a)+1,e)+t,_=v-k;for(x=k;x<v;x++)m+=i[x].x,b+=i[x].y;m/=_,b/=_;const M=Math.floor(d*a)+1+t,S=Math.min(Math.floor((d+1)*a)+1,e)+t,{x:w,y:P}=i[h];for(f=g=-1,x=M;x<S;x++)g=.5*Math.abs((w-m)*(i[x].y-P)-(w-i[x].x)*(b-P)),g>f&&(f=g,u=i[x],p=x);r[l++]=u,h=p}return r[l++]=i[c],r}function th(i,t,e,s){let n=0,o=0,r,a,l,c,h,d,u,f,g,p;const m=[],b=t+e-1,x=i[t].x,v=i[b].x-x;for(r=t;r<t+e;++r){a=i[r],l=(a.x-x)/v*s,c=a.y;const _=l|0;if(_===h)c<g?(g=c,d=r):c>p&&(p=c,u=r),n=(o*n+a.x)/++o;else{const M=r-1;if(!R(d)&&!R(u)){const S=Math.min(d,u),w=Math.max(d,u);S!==f&&S!==M&&m.push({...i[S],x:n}),w!==f&&w!==M&&m.push({...i[w],x:n})}r>0&&M!==f&&m.push(i[M]),m.push(a),h=_,o=0,g=p=c,d=u=f=r}}return m}function Mo(i){if(i._decimated){const t=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function Qs(i){i.data.datasets.forEach(t=>{Mo(t)})}function eh(i,t){const e=t.length;let s=0,n;const{iScale:o}=i,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=J(vt(t,o.axis,r).lo,0,e-1)),c?n=J(vt(t,o.axis,a).hi+1,s,e)-s:n=e-s,{start:s,count:n}}var ih={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,t,e)=>{if(!e.enabled){Qs(i);return}const s=i.width;i.data.datasets.forEach((n,o)=>{const{_data:r,indexAxis:a}=n,l=i.getDatasetMeta(o),c=r||n.data;if(le([a,i.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=i.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||i.options.parsing)return;let{start:d,count:u}=eh(l,c);const f=e.threshold||4*s;if(u<=f){Mo(n);return}R(r)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(p){this._data=p}}));let g;switch(e.algorithm){case"lttb":g=Qc(c,d,u,s,e);break;case"min-max":g=th(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=g})},destroy(i){Qs(i)}};function sh(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=Xi(l,c,n);const h=Pi(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const d=eo(t,h);for(const u of d){const f=Pi(e,o[u.start],o[u.end],u.loop),g=to(a,n,f);for(const p of g)r.push({source:p,target:u,start:{[e]:tn(h,f,"start",Math.max)},end:{[e]:tn(h,f,"end",Math.min)}})}}return r}function Pi(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=at(n),o=at(o)),{property:i,start:n,end:o}}function nh(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=Xi(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function Xi(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function tn(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function So(i,t){let e=[],s=!1;return V(i)?(s=!0,e=i):e=nh(i,t),e.length?new ni({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function en(i){return i&&i.fill!==!1}function oh(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!Y(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function rh(i,t,e){const s=hh(i);if(L(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return Y(n)&&Math.floor(n)===n?ah(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function ah(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function lh(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:L(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function ch(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:L(i)?s=i.value:s=t.getBaseValue(),s}function hh(i){const t=i.options,e=t.fill;let s=O(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function dh(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=uh(t,e);a.push(So({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)fh(n,r[h],a)}return new ni({points:n,options:{}})}function uh(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function fh(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=gh(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function gh(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][e],u=r[h.end][e];if(yt(n,d,u)){a=n===d,l=n===u;break}}return{first:a,last:l,point:s}}class wo{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:j},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function ph(i){const{chart:t,fill:e,line:s}=i;if(Y(e))return mh(t,e);if(e==="stack")return dh(i);if(e==="shape")return!0;const n=bh(i);return n instanceof wo?n:So(n,s)}function mh(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function bh(i){return(i.scale||{}).getPointPositionForValue?_h(i):xh(i)}function xh(i){const{scale:t={},fill:e}=i,s=lh(e,t);if(Y(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function _h(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=ch(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new wo({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function xi(i,t,e){const s=ph(t),{line:n,scale:o,axis:r}=t,a=n.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:d=c}=l||{};s&&n.points.length&&(Qe(i,e),yh(i,{line:n,target:s,above:h,below:d,area:e,scale:o,axis:r}),ti(i))}function yh(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a}=t,l=e._loop?"angle":t.axis;i.save(),l==="x"&&o!==n&&(sn(i,s,r.top),nn(i,{line:e,target:s,color:n,scale:a,property:l}),i.restore(),i.save(),sn(i,s,r.bottom)),nn(i,{line:e,target:s,color:o,scale:a,property:l}),i.restore()}function sn(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[Xi(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function nn(i,t){const{line:e,target:s,property:n,color:o,scale:r}=t,a=sh(e,s,n);for(const{source:l,target:c,start:h,end:d}of a){const{style:{backgroundColor:u=o}={}}=l,f=s!==!0;i.save(),i.fillStyle=u,vh(i,r,f&&Pi(n,h,d)),i.beginPath();const g=!!e.pathSegment(i,l);let p;if(f){g?i.closePath():on(i,s,d,n);const m=!!s.pathSegment(i,c,{move:g,reverse:!0});p=g&&m,p||on(i,s,h,n)}i.closePath(),i.fill(p?"evenodd":"nonzero"),i.restore()}}function vh(i,t,e){const{top:s,bottom:n}=t.chart.chartArea,{property:o,start:r,end:a}=e||{};o==="x"&&(i.beginPath(),i.rect(r,s,a-r,n-s),i.clip())}function on(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var kh={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof ni&&(l={visible:i.isDatasetVisible(r),index:r,fill:rh(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=oh(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&xi(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;en(o)&&xi(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!en(s)||e.drawTime!=="beforeDatasetDraw"||xi(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const rn=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},Mh=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class an extends Mt{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=B(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=G(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=rn(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,f+=h,u++),l[p]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,u=0,f=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:x,itemHeight:k}=Sh(s,e,o,m,n);b>0&&f+k+2*a>h&&(d+=u+a,c.push({width:u,height:f}),g+=u+a,p++,u=f=0),l[b]={left:g,top:f,col:p,width:x,height:k},u=Math.max(u,x),f+=k+a}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Gt(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=tt(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=tt(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=tt(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=tt(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Qe(t,this),this._draw(),ti(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=X.color,l=Gt(t.rtl,this.left,this.width),c=G(r.font),{padding:h}=r,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=rn(r,d),b=function(M,S,w){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const P=O(w.lineWidth,1);if(n.fillStyle=O(w.fillStyle,a),n.lineCap=O(w.lineCap,"butt"),n.lineDashOffset=O(w.lineDashOffset,0),n.lineJoin=O(w.lineJoin,"miter"),n.lineWidth=P,n.strokeStyle=O(w.strokeStyle,a),n.setLineDash(O(w.lineDash,[])),r.usePointStyle){const A={radius:p*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:P},D=l.xPlus(M,g/2),T=S+u;Wn(n,A,D,T,r.pointStyleWidth&&g)}else{const A=S+Math.max((d-p)/2,0),D=l.leftForLtr(M,g),T=Wt(w.borderRadius);n.beginPath(),Object.values(T).some(I=>I!==0)?_e(n,{x:D,y:A,w:g,h:p,radius:T}):n.rect(D,A,g,p),n.fill(),P!==0&&n.stroke()}n.restore()},x=function(M,S,w){Yt(n,w.text,M,S+m/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},k=this.isHorizontal(),v=this._computeTitleHeight();k?f={x:tt(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:f={x:this.left+h,y:tt(o,this.top+v+h,this.bottom-e[0].height),line:0},Zn(this.ctx,t.textDirection);const _=m+h;this.legendItems.forEach((M,S)=>{n.strokeStyle=M.fontColor,n.fillStyle=M.fontColor;const w=n.measureText(M.text).width,P=l.textAlign(M.textAlign||(M.textAlign=r.textAlign)),A=g+u+w;let D=f.x,T=f.y;l.setWidth(this.width),k?S>0&&D+A+h>this.right&&(T=f.y+=_,f.line++,D=f.x=tt(o,this.left+h,this.right-s[f.line])):S>0&&T+_>this.bottom&&(D=f.x=D+e[f.line].width+h,f.line++,T=f.y=tt(o,this.top+v+h,this.bottom-e[f.line].height));const I=l.x(D);if(b(I,T,M),D=Br(P,D+g+u,k?D+A:this.right,t.rtl),x(l.x(D),T,M),k)f.x+=A+h;else if(typeof M.text!="string"){const K=c.lineHeight;f.y+=Co(M,K)+h}else f.y+=_}),Jn(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=G(e.font),n=it(e.padding);if(!e.display)return;const o=Gt(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=tt(t.align,d,this.right-u);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+tt(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=tt(a,d,d+u);r.textAlign=o.textAlign(zi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Yt(r,e.text,f,h,s)}_computeTitleHeight(){const t=this.options.title,e=G(t.font),s=it(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(yt(t,this.left,this.right)&&yt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],yt(t,n.left,n.left+n.width)&&yt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!Ph(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=Mh(n,s);n&&!o&&B(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&B(e.onHover,[t,s,this],this)}else s&&B(e.onClick,[t,s,this],this)}}function Sh(i,t,e,s,n){const o=wh(s,i,t,e),r=Ch(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function wh(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Ch(i,t,e){let s=i;return typeof t.text!="string"&&(s=Co(t,e)),s}function Co(i,t){const e=i.text?i.text.length:0;return t*e}function Ph(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Dh={id:"legend",_element:an,start(i,t,e){const s=i.legend=new an({ctx:i.ctx,options:e,chart:i});et.configure(i,s,e),et.addBox(i,s)},stop(i){et.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;et.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=it(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class Ui extends Mt{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=V(s.text)?s.text.length:1;this._padding=it(s.padding);const o=n*G(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=tt(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=tt(a,n,e),l=N*-.5):(h=o-t,d=tt(a,e,n),l=N*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=G(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Yt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:zi(e.align),textBaseline:"middle",translation:[r,a]})}}function Oh(i,t){const e=new Ui({ctx:i.ctx,options:t,chart:i});et.configure(i,e,t),et.addBox(i,e),i.titleBlock=e}var Ah={id:"title",_element:Ui,start(i,t,e){Oh(i,e)},stop(i){const t=i.titleBlock;et.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;et.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ze=new WeakMap;var Th={id:"subtitle",start(i,t,e){const s=new Ui({ctx:i.ctx,options:e,chart:i});et.configure(i,s,e),et.addBox(i,s),ze.set(i,s)},stop(i){et.removeBox(i,ze.get(i)),ze.delete(i)},beforeUpdate(i,t,e){const s=ze.get(i);et.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const he={average(i){if(!i.length)return!1;let t,e,s=0,n=0,o=0;for(t=0,e=i.length;t<e;++t){const r=i[t].element;if(r&&r.hasValue()){const a=r.tooltipPosition();s+=a.x,n+=a.y,++o}}return{x:s/o,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=yi(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function ut(i,t){return t&&(V(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function xt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function Lh(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function ln(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=G(t.bodyFont),c=G(t.titleFont),h=G(t.footerFont),d=o.length,u=n.length,f=s.length,g=it(t.padding);let p=g.height,m=0,b=s.reduce((v,_)=>v+_.before.length+_.lines.length+_.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=f*v+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(p+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let x=0;const k=function(v){m=Math.max(m,e.measureText(v).width+x)};return e.save(),e.font=c.string,F(i.title,k),e.font=l.string,F(i.beforeBody.concat(i.afterBody),k),x=t.displayColors?r+2+t.boxPadding:0,F(s,v=>{F(v.before,k),F(v.lines,k),F(v.after,k)}),x=0,e.font=h.string,F(i.footer,k),e.restore(),m+=g.width,{width:m,height:p}}function Rh(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Eh(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function Fh(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Eh(c,i,t,e)&&(c="center"),c}function cn(i,t,e){const s=e.yAlign||t.yAlign||Rh(i,e);return{xAlign:e.xAlign||t.xAlign||Fh(i,t,e,s),yAlign:s}}function Ih(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function zh(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function hn(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Wt(r);let g=Ih(t,a);const p=zh(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,u)+n:a==="right"&&(g+=Math.max(d,f)+n),{x:J(g,0,s.width-t.width),y:J(p,0,s.height-t.height)}}function Be(i,t,e){const s=it(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function dn(i){return ut([],xt(i))}function Bh(i,t,e){return Lt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function un(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const Po={beforeTitle:mt,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:mt,beforeBody:mt,beforeLabel:mt,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return R(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:mt,afterBody:mt,beforeFooter:mt,footer:mt,afterFooter:mt};function st(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?Po[t].call(e,s):n}class fn extends Mt{static positioners=he;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new io(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Bh(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=st(s,"beforeTitle",this,t),o=st(s,"title",this,t),r=st(s,"afterTitle",this,t);let a=[];return a=ut(a,xt(n)),a=ut(a,xt(o)),a=ut(a,xt(r)),a}getBeforeBody(t,e){return dn(st(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return F(t,o=>{const r={before:[],lines:[],after:[]},a=un(s,o);ut(r.before,xt(st(a,"beforeLabel",this,o))),ut(r.lines,st(a,"label",this,o)),ut(r.after,xt(st(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return dn(st(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=st(s,"beforeFooter",this,t),o=st(s,"footer",this,t),r=st(s,"afterFooter",this,t);let a=[];return a=ut(a,xt(n)),a=ut(a,xt(o)),a=ut(a,xt(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(Lh(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),F(a,h=>{const d=un(t.callbacks,h);n.push(st(d,"labelColor",this,h)),o.push(st(d,"labelPointStyle",this,h)),r.push(st(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=he[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=ln(this,s),c=Object.assign({},a,l),h=cn(this.chart,s,c),d=hn(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Wt(a),{x:u,y:f}=t,{width:g,height:p}=e;let m,b,x,k,v,_;return o==="center"?(v=f+p/2,n==="left"?(m=u,b=m-r,k=v+r,_=v-r):(m=u+g,b=m+r,k=v-r,_=v+r),x=m):(n==="left"?b=u+Math.max(l,h)+r:n==="right"?b=u+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(k=f,v=k-r,m=b-r,x=b+r):(k=f+p,v=k+r,m=b+r,x=b-r),_=k),{x1:m,x2:b,x3:x,y1:k,y2:v,y3:_}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Gt(s.rtl,this.x,this.width);for(t.x=Be(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=G(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=G(o.bodyFont),d=Be(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,ki(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,ki(t,p,m,b)}else{t.lineWidth=L(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=n.leftForLtr(u,c),m=n.leftForLtr(n.xPlus(u,1),c-2),b=Wt(r.borderRadius);Object.values(b).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,_e(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),_e(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=G(s.bodyFont);let u=d.lineHeight,f=0;const g=Gt(s.rtl,this.x,this.width),p=function(w){e.fillText(w,g.x(t.x+f),t.y+u/2),t.y+=u+o},m=g.textAlign(r);let b,x,k,v,_,M,S;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=Be(this,m,s),e.fillStyle=s.bodyColor,F(this.beforeBody,p),f=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,M=n.length;v<M;++v){for(b=n[v],x=this.labelTextColors[v],e.fillStyle=x,F(b.before,p),k=b.lines,a&&k.length&&(this._drawColorBox(e,t,v,g,s),u=Math.max(d.lineHeight,l)),_=0,S=k.length;_<S;++_)p(k[_]),u=d.lineHeight;F(b.after,p)}f=0,u=d.lineHeight,F(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Gt(s.rtl,this.x,this.width);for(t.x=Be(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=G(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=Wt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-u,l),e.quadraticCurveTo(a+c,l,a+c,l+u),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=he[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=ln(this,t),l=Object.assign({},r,this._size),c=cn(e,t,l),h=hn(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=it(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),Zn(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),Jn(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!$e(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!$e(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e;const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=he[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}var jh={id:"tooltip",_element:fn,positioners:he,afterInit(i,t,e){e&&(i.tooltip=new fn({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Po},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Nh=Object.freeze({__proto__:null,Colors:Jc,Decimation:ih,Filler:kh,Legend:Dh,SubTitle:Th,Title:Ah,Tooltip:jh});const Vh=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function Wh(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Vh(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Hh=(i,t)=>i===null?null:J(Math.round(i),0,t);function gn(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class $h extends Xt{static id="category";static defaults={ticks:{callback:gn}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(R(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:Wh(s,t,O(e,t),this._addedLabels),Hh(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return gn.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function Yh(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:p,max:m}=t,b=!R(r),x=!R(a),k=!R(c),v=(m-p)/(d+1);let _=rs((m-p)/g/f)*f,M,S,w,P;if(_<1e-14&&!b&&!x)return[{value:p},{value:m}];P=Math.ceil(m/_)-Math.floor(p/_),P>g&&(_=rs(P*_/g/f)*f),R(l)||(M=Math.pow(10,l),_=Math.ceil(_*M)/M),n==="ticks"?(S=Math.floor(p/_)*_,w=Math.ceil(m/_)*_):(S=p,w=m),b&&x&&o&&Tr((a-r)/o,_/1e3)?(P=Math.round(Math.min((a-r)/_,h)),_=(a-r)/P,S=r,w=a):k?(S=b?r:S,w=x?a:w,P=c-1,_=(w-S)/P):(P=(w-S)/_,ue(P,Math.round(P),_/1e3)?P=Math.round(P):P=Math.ceil(P));const A=Math.max(as(_),as(S));M=Math.pow(10,R(l)?A:l),S=Math.round(S*M)/M,w=Math.round(w*M)/M;let D=0;for(b&&(u&&S!==r?(e.push({value:r}),S<r&&D++,ue(Math.round((S+D*_)*M)/M,r,pn(r,v,i))&&D++):S<r&&D++);D<P;++D){const T=Math.round((S+D*_)*M)/M;if(x&&T>a)break;e.push({value:T})}return x&&u&&w!==a?e.length&&ue(e[e.length-1].value,a,pn(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!x||w===a)&&e.push({value:w}),e}function pn(i,t,{horizontal:e,minRotation:s}){const n=ht(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class Ze extends Xt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return R(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=gt(n),c=gt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=Yh(n,o);return t.bounds==="ticks"&&Rn(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Me(t,this.chart.options.locale,this.options.ticks.format)}}class Xh extends Ze{static id="linear";static defaults={ticks:{callback:Je.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Y(t)?t:0,this.max=Y(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=ht(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const ve=i=>Math.floor(Pt(i)),jt=(i,t)=>Math.pow(10,ve(i)+t);function mn(i){return i/Math.pow(10,ve(i))===1}function bn(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function Uh(i,t){const e=t-i;let s=ve(e);for(;bn(i,t,s)>10;)s++;for(;bn(i,t,s)<10;)s--;return Math.min(s,ve(i))}function Kh(i,{min:t,max:e}){t=rt(i.min,t);const s=[],n=ve(t);let o=Uh(t,e),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*r)/r,h=Math.floor((t-l)/a/10)*a*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=rt(i.min,Math.round((l+h+d*Math.pow(10,o))*r)/r);for(;u<e;)s.push({value:u,major:mn(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),u=Math.round((l+h+d*Math.pow(10,o))*r)/r;const f=rt(i.max,u);return s.push({value:f,major:mn(f),significand:d}),s}class qh extends Xt{static id="logarithmic";static defaults={ticks:{callback:Je.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=Ze.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return Y(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Y(t)?Math.max(0,t):null,this.max=Y(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Y(this._userMin)&&(this.min=t===jt(this.min,0)?jt(this.min,-1):jt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=a=>s=t?s:a,r=a=>n=e?n:a;s===n&&(s<=0?(o(1),r(10)):(o(jt(s,-1)),r(jt(n,1)))),s<=0&&o(jt(n,-1)),n<=0&&r(jt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=Kh(e,this);return t.bounds==="ticks"&&Rn(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":Me(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=Pt(t),this._valueRange=Pt(this.max)-Pt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(Pt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function Di(i){const t=i.ticks;if(t.display&&i.display){const e=it(t.backdropPadding);return O(t.font&&t.font.size,X.font.size)+e.height}return 0}function Gh(i,t,e){return e=V(e)?e:[e],{w:Kr(i,t.string,e),h:e.length*t.lineHeight}}function xn(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function Zh(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,r=i.options.pointLabels,a=r.centerPointLabels?N/o:0;for(let l=0;l<o;l++){const c=r.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],a),d=G(c.font),u=Gh(i.ctx,d,i._pointLabels[l]);s[l]=u;const f=at(i.getIndexAngle(l)+a),g=Math.round(Fi(f)),p=xn(g,h.x,u.w,0,180),m=xn(g,h.y,u.h,90,270);Jh(e,t,f,p,m)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=ed(i,s,n)}function Jh(i,t,e,s,n){const o=Math.abs(Math.sin(e)),r=Math.abs(Math.cos(e));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+a)),n.start<t.t?(l=(t.t-n.start)/r,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/r,i.b=Math.max(i.b,t.b+l))}function Qh(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=e,l=i.getPointPosition(t,s+n+r,o),c=Math.round(Fi(at(l.angle+U))),h=nd(l.y,a.h,c),d=id(c),u=sd(l.x,a.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+a.w,bottom:h+a.h}}function td(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(kt({x:e,y:s},t)||kt({x:e,y:o},t)||kt({x:n,y:s},t)||kt({x:n,y:o},t))}function ed(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Di(o)/2,additionalAngle:r?N/n:0};let c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];const d=Qh(i,h,l);s.push(d),a==="auto"&&(d.visible=td(d,c),d.visible&&(c=d))}return s}function id(i){return i===0||i===180?"center":i<180?"left":"right"}function sd(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function nd(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function od(i,t,e){const{left:s,top:n,right:o,bottom:r}=e,{backdropColor:a}=t;if(!R(a)){const l=Wt(t.borderRadius),c=it(t.backdropPadding);i.fillStyle=a;const h=s-c.left,d=n-c.top,u=o-s+c.width,f=r-n+c.height;Object.values(l).some(g=>g!==0)?(i.beginPath(),_e(i,{x:h,y:d,w:u,h:f,radius:l}),i.fill()):i.fillRect(h,d,u,f)}}function rd(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const r=s.setContext(i.getPointLabelContext(n));od(e,r,o);const a=G(r.font),{x:l,y:c,textAlign:h}=o;Yt(e,i._pointLabels[n],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function Do(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,j);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=i.getPointPosition(r,t),n.lineTo(o.x,o.y)}}function ad(i,t,e,s,n){const o=i.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||e<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash),o.lineDashOffset=n.dashOffset,o.beginPath(),Do(i,e,r,s),o.closePath(),o.stroke(),o.restore())}function ld(i,t,e){return Lt(i,{label:e,index:t,type:"pointLabel"})}class cd extends Ze{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Je.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=it(Di(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=Y(t)&&!isNaN(t)?t:0,this.max=Y(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Di(this.options))}generateTickLabels(t){Ze.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=B(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?Zh(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=j/(this._pointLabels.length||1),s=this.options.startAngle||0;return at(t*e+ht(s))}getDistanceFromCenterForValue(t){if(R(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(R(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return ld(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-U+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),Do(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,r=this._pointLabels.length;let a,l,c;if(e.pointLabels.display&&rd(this,r),n.display&&this.ticks.forEach((h,d)=>{if(d!==0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);ad(this,f,l,r,g)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const h=s.setContext(this.getPointLabelContext(a)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&!e.reverse)return;const c=s.setContext(this.getContext(l)),h=G(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const d=it(c.backdropPadding);t.fillRect(-r/2-d.left,-o-h.size/2-d.top,r+d.width,h.size+d.height)}Yt(t,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}const oi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},nt=Object.keys(oi);function _n(i,t){return i-t}function yn(i,t){if(R(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),Y(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Zt(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function vn(i,t,e,s){const n=nt.length;for(let o=nt.indexOf(i);o<n-1;++o){const r=oi[nt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return nt[o]}return nt[n-1]}function hd(i,t,e,s,n){for(let o=nt.length-1;o>=nt.indexOf(e);o--){const r=nt[o];if(oi[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return nt[e?nt.indexOf(e):0]}function dd(i){for(let t=nt.indexOf(i)+1,e=nt.length;t<e;++t)if(oi[nt[t]].common)return nt[t]}function kn(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=Ii(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function ud(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function Mn(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:ud(i,s,n,e)}class Oi extends Xt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new gl._date(t.adapters.date);n.init(e),de(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:yn(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=Y(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=Y(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=Fr(n,o,r);return this._unit=e.unit||(s.autoSkip?vn(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):hd(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:dd(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Mn(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=J(e,0,r),s=J(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||vn(o.minUnit,e,s,this._getLabelCapacity(e)),a=O(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Zt(l)||l===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,a,r),f++)kn(h,u,g);return(u===s||n.bounds==="ticks"||f===1)&&kn(h,u,g),Object.keys(h).sort(_n).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return B(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=ht(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Mn(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(yn(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return In(t.sort(_n))}}function je(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=vt(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=vt(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class fd extends Oi{static id="timeseries";static defaults=Oi.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=je(e,this.min),this._tableRange=je(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(je(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return je(this._table,s*this._tableRange+this._minPos,!0)}}var gd=Object.freeze({__proto__:null,CategoryScale:$h,LinearScale:Xh,LogarithmicScale:qh,RadialLinearScale:cd,TimeScale:Oi,TimeSeriesScale:fd});const pd=[fl,Xc,Nh,gd];si.register(...pd);const Oo="label";function Sn(i,t){typeof i=="function"?i(t):i&&(i.current=t)}function md(i,t){const e=i.options;e&&t&&Object.assign(e,t)}function Ao(i,t){i.labels=t}function To(i,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Oo;const s=[];i.datasets=t.map(n=>{const o=i.datasets.find(r=>r[e]===n[e]);return!o||!n.data||s.includes(o)?{...n}:(s.push(o),Object.assign(o,n),o)})}function bd(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Oo;const e={labels:[],datasets:[]};return Ao(e,i.labels),To(e,i.datasets,t),e}function xd(i,t){const{height:e=150,width:s=300,redraw:n=!1,datasetIdKey:o,type:r,data:a,options:l,plugins:c=[],fallbackContent:h,updateMode:d,...u}=i,f=W.useRef(null),g=W.useRef(),p=()=>{f.current&&(g.current=new si(f.current,{type:r,data:bd(a,o),options:l&&{...l},plugins:c}),Sn(t,g.current))},m=()=>{Sn(t,null),g.current&&(g.current.destroy(),g.current=null)};return W.useEffect(()=>{!n&&g.current&&l&&md(g.current,l)},[n,l]),W.useEffect(()=>{!n&&g.current&&Ao(g.current.config.data,a.labels)},[n,a.labels]),W.useEffect(()=>{!n&&g.current&&a.datasets&&To(g.current.config.data,a.datasets,o)},[n,a.datasets]),W.useEffect(()=>{g.current&&(n?(m(),setTimeout(p)):g.current.update(d))},[n,l,a.labels,a.datasets,d]),W.useEffect(()=>{g.current&&(m(),setTimeout(p))},[r]),W.useEffect(()=>(p(),()=>m()),[]),Cn.createElement("canvas",Object.assign({ref:f,role:"img",height:e,width:s},u),h)}const _d=W.forwardRef(xd);function Lo(i,t){return si.register(t),W.forwardRef((e,s)=>Cn.createElement(_d,Object.assign({},e,{ref:s,type:i})))}const yd=Lo("line",oo),vd=Lo("pie",ao),kd=({salesReport:i})=>{const t=new Set,e=i?.filter(l=>{const c=!t.has(l.date);return t.add(l.date),c}),[s,n]=W.useState({title:"Sales",color:"emerald"}),o=({title:l,color:c})=>{n({title:l,color:c})},r={data:{labels:e?.sort((l,c)=>new Date(l.date)-new Date(c.date))?.map(l=>l.date),datasets:[s.title==="Sales"?{label:"Sales",data:e?.sort((l,c)=>new Date(l.date)-new Date(c.date))?.map(l=>l.total),borderColor:"#10B981",backgroundColor:"#10B981",borderWidth:3,yAxisID:"y"}:{label:"Order",data:e?.sort((l,c)=>new Date(l.date)-new Date(c.date))?.map(l=>l.order),borderColor:"#F97316",backgroundColor:"#F97316",borderWidth:3,yAxisID:"y"}]},options:{responsive:!0},legend:{display:!1}},{t:a}=Ai();return y.jsxs(y.Fragment,{children:[y.jsx("div",{className:"text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:text-gray-400 dark:border-gray-700 mb-4",children:y.jsxs("ul",{className:"flex flex-wrap -mb-px",children:[y.jsx("li",{className:"mr-2",children:y.jsx("button",{onClick:()=>o({title:"Sales",color:"emerald"}),type:"button",className:`inline-block p-2 rounded-t-lg border-b-2 border-transparent ${s.title==="Sales"?"text-emerald-600 border-emerald-600 dark:text-emerald-500 dark:border-emerald-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"}  focus:outline-none`,children:a("Sales")})}),y.jsx("li",{className:"mr-2",children:y.jsx("button",{onClick:()=>o({title:"Orders",color:"red"}),type:"button",className:`inline-block p-2 rounded-t-lg border-b-2 border-transparent ${s.title==="Orders"?"text-orange-500 border-orange-500 dark:text-orange-500 dark:border-orange-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"}  focus:outline-none`,children:a("Orders")})})]})}),y.jsx(yd,{...r})]})},Md=({data:i})=>{const t={data:{datasets:[{data:i?.bestSellingProduct?.map(e=>e.count),backgroundColor:["#10B981","#3B82F6","#F97316","#0EA5E9"],label:"Dataset 1"}],labels:i?.bestSellingProduct?.map(e=>e._id)},options:{responsive:!0,cutoutPercentage:80},legend:{display:!1}};return y.jsx("div",{children:y.jsx(vd,{...t,className:"chart"})})},Ne=({title:i,Icon:t,quantity:e,amount:s,className:n,loading:o,mode:r,pending:a,todayPending:l,olderPending:c})=>{const{getNumberTwo:h}=Pn();return y.jsx(y.Fragment,{children:o?y.jsx(qt,{count:2,height:40,className:"dark:bg-gray-800 bg-gray-200",baseColor:`${r==="dark"?"#010101":"#f9f9f9"}`,highlightColor:`${r==="dark"?"#1a1c23":"#f8f8f8"} `}):y.jsx(q.Card,{className:"flex h-full",children:y.jsxs(q.CardBody,{className:"flex items-center border border-gray-200 dark:border-gray-800 w-full rounded-lg",children:[y.jsx("div",{className:`flex items-center justify-center p-3 rounded-full h-12 w-12 text-center mr-4 text-lg ${n}`,children:y.jsx(t,{})}),y.jsxs("div",{children:[y.jsxs("h6",{className:"text-sm mb-1 font-medium text-gray-600 dark:text-gray-400",children:[y.jsx("span",{children:i})," ",s&&y.jsxs("span",{className:"text-red-500 text-sm font-semibold",children:["(",h(s),")"]})]}),a&&y.jsxs("div",{className:"grid grid-cols-2 gap-4 w-full mb-1 text-sm font-medium text-gray-600 dark:text-gray-400",children:[y.jsxs("div",{children:[y.jsx("span",{className:"font-semibold",children:"Today"})," ",y.jsxs("span",{className:"text-emerald-600 text-sm font-semibold",children:["(",h(l),")"]})]}),y.jsxs("div",{children:[y.jsx("span",{className:"font-semibold",children:"Older"})," ",y.jsxs("span",{className:"text-orange-400 text-sm font-semibold",children:["(",h(c),")"]})]})]}),y.jsx("p",{className:"text-2xl font-bold leading-none text-gray-600 dark:text-gray-200",children:e})]})]})})})},re=({mode:i,title:t,Icon:e,className:s,price:n,cash:o,card:r,credit:a,loading:l,title2:c})=>{const{t:h}=Ai(),{currency:d,getNumberTwo:u}=Pn();return y.jsx(y.Fragment,{children:l?y.jsx(qt,{count:4,height:40,className:"dark:bg-gray-800 bg-gray-200",baseColor:`${i==="dark"?"#010101":"#f9f9f9"}`,highlightColor:`${i==="dark"?"#1a1c23":"#f8f8f8"} `}):y.jsx(y.Fragment,{children:t==="Today Order"||t==="Yesterday Order"?y.jsx(q.Card,{className:"flex justify-center h-full",children:y.jsx(q.CardBody,{className:`border border-gray-200 justify-between dark:border-gray-800 w-full p-6 rounded-lg ${s}`,children:y.jsxs("div",{className:"text-center xl:mb-0 mb-3",children:[y.jsx("div",{className:`text-center inline-block text-3xl ${s}`,children:y.jsx(e,{})}),y.jsxs("div",{children:[y.jsx("p",{className:"mb-3 text-base font-medium text-gray-50 dark:text-gray-100",children:c?h(`${c}`):y.jsx(qt,{count:1,height:20})}),y.jsxs("p",{className:"text-2xl font-bold leading-none text-gray-50 dark:text-gray-50",children:[d,u(n)]})]}),y.jsxs("div",{className:"flex text-center text-xs font-normal text-gray-50 dark:text-gray-100",children:[y.jsxs("div",{className:"px-1 mt-3",children:[h("Cash")," : ",d,u(o)]}),y.jsxs("div",{className:"px-1 mt-3",children:[h("Card")," : ",d,u(r)]}),y.jsxs("div",{className:"px-1 mt-3",children:[h("Credit")," : ",d,u(a)]})]})]})})}):y.jsx(q.Card,{className:"flex justify-center text-center h-full",children:y.jsxs(q.CardBody,{className:`border border-gray-200 dark:border-gray-800 w-full p-6 rounded-lg ${s}`,children:[y.jsx("div",{className:`text-center inline-block text-3xl ${s}`,children:y.jsx(e,{})}),y.jsxs("div",{children:[y.jsx("p",{className:"mb-3 text-base font-medium text-gray-50 dark:text-gray-100",children:h(`${c}`)}),y.jsxs("p",{className:"text-2xl font-bold leading-none text-gray-50 dark:text-gray-50",children:[d,u(n)]})]})]})})})})},wn=({children:i,title:t,loading:e,mode:s})=>y.jsxs("div",{className:"min-w-0 p-4 bg-white rounded-lg shadow-xs dark:bg-gray-800",children:[y.jsx("p",{className:"mb-4 font-semibold text-gray-800 dark:text-gray-300",children:e?y.jsx(qt,{count:1,height:20,className:"dark:bg-gray-800 bg-gray-200",baseColor:`${s==="dark"?"#010101":"#f9f9f9"}`,highlightColor:`${s==="dark"?"#1a1c23":"#f8f8f8"} `}):t}),t==="Best Selling Products"?y.jsx(y.Fragment,{children:e?y.jsx("div",{className:"flex justify-center",children:y.jsx(qt,{className:"dark:bg-gray-800 bg-gray-200",baseColor:`${s==="dark"?"#010101":"#f9f9f9"}`,highlightColor:`${s==="dark"?"#1a1c23":"#f8f8f8"} `,count:1,width:250,height:250,circle:!0})}):i}):y.jsx(y.Fragment,{children:e?y.jsx(qt,{className:"dark:bg-gray-800 bg-gray-200",baseColor:`${s==="dark"?"#010101":"#f9f9f9"}`,highlightColor:`${s==="dark"?"#1a1c23":"#f8f8f8"} `,count:13,height:20}):i})]}),Ud=()=>{const{t:i}=Ai(),{mode:t}=W.useContext(q.WindmillContext);wt.extend(Wo),wt.extend(Ho),wt.extend(Jo);const{currentPage:e,handleChangePage:s}=W.useContext(Bo),[n,o]=W.useState(0),[r,a]=W.useState(0),[l,c]=W.useState([]),[h,d]=W.useState(0),[u,f]=W.useState(0),[g,p]=W.useState(0),[m,b]=W.useState(0),[x,k]=W.useState(0),[v,_]=W.useState(0),{data:M,loading:S,error:w}=Se(we.getBestSellerProductChart),{data:P,loading:A}=Se(()=>we.getDashboardRecentOrder({page:e,limit:8})),{data:D,loading:T}=Se(we.getDashboardCount),{data:I,loading:K}=Se(we.getDashboardAmount),{dataTable:E,serviceData:z}=$o(P?.orders);return W.useEffect(()=>{const ot=I?.ordersData?.filter(C=>wt(C.updatedAt).isToday())?.reduce((C,Z)=>C+Z.total,0);o(ot);const St=I?.ordersData?.filter(C=>wt(C.updatedAt).set(-1,"day").isYesterday())?.reduce((C,Z)=>C+Z.total,0);a(St),I?.ordersData?.filter(C=>wt(C.updatedAt).isBetween(new Date().setDate(new Date().getDate()-7),new Date))?.reduce((C,Z)=>{let $=Z.updatedAt.split("T")[0];return C[$]||(C[$]={date:$,total:0,order:0},l.push(C[$])),C[$].total+=Z.total,C[$].order+=1,C},{}),c(l);const ct=[],dt=[];I?.ordersData?.filter((C,Z)=>{if(wt(C.updatedAt).isToday()){if(C.paymentMethod==="Cash"){let $={paymentMethod:"Cash",total:C.total};ct.push($)}if(C.paymentMethod==="Credit"){const $={paymentMethod:"Credit",total:C.total};ct.push($)}if(C.paymentMethod==="Card"){const $={paymentMethod:"Card",total:C.total};ct.push($)}}return C}),I?.ordersData?.filter((C,Z)=>{if(wt(C.updatedAt).set(-1,"day").isYesterday()){if(C.paymentMethod==="Cash"){let $={paymentMethod:"Cash",total:C.total};dt.push($)}if(C.paymentMethod==="Credit"){const $={paymentMethod:"Credit",total:C?.total};dt.push($)}if(C.paymentMethod==="Card"){const $={paymentMethod:"Card",total:C?.total};dt.push($)}}return C});const pt=Object.values(ct.reduce((C,{paymentMethod:Z,total:$})=>(C[Z]||(C[Z]={paymentMethod:Z,total:0}),C[Z].total+=$,C),{})),Et=pt.find(C=>C.paymentMethod==="Cash");d(Et?.total);const Ft=pt.find(C=>C.paymentMethod==="Card");f(Ft?.total);const Ro=pt.find(C=>C.paymentMethod==="Credit");p(Ro?.total);const ri=Object.values(dt.reduce((C,{paymentMethod:Z,total:$})=>(C[Z]||(C[Z]={paymentMethod:Z,total:0}),C[Z].total+=$,C),{})),Eo=ri.find(C=>C.paymentMethod==="Cash");b(Eo?.total);const Fo=ri.find(C=>C.paymentMethod==="Card");k(Fo?.total);const Io=ri.find(C=>C.paymentMethod==="Credit");_(Io?.total)},[I]),y.jsxs(y.Fragment,{children:[y.jsx(Zi,{children:i("DashboardOverview")}),y.jsxs(Ko,{children:[y.jsxs("div",{className:"grid gap-2 mb-8 xl:grid-cols-5 md:grid-cols-2",children:[y.jsx(re,{mode:t,title:"Today Order",title2:"TodayOrder",Icon:qi,cash:h||0,card:u||0,credit:g||0,price:n||0,className:"text-white dark:text-emerald-100 bg-teal-600",loading:K}),y.jsx(re,{mode:t,title:"Yesterday Order",title2:"YesterdayOrder",Icon:qi,cash:m||0,card:x||0,credit:v||0,price:r||0,className:"text-white dark:text-orange-100 bg-orange-400",loading:K}),y.jsx(re,{mode:t,title2:"ThisMonth",Icon:Ki,price:I?.thisMonthlyOrderAmount||0,className:"text-white dark:text-emerald-100 bg-blue-500",loading:K}),y.jsx(re,{mode:t,title2:"LastMonth",Icon:Gi,loading:K,price:I?.lastMonthOrderAmount||0,className:"text-white dark:text-teal-100 bg-cyan-600"}),y.jsx(re,{mode:t,title2:"AllTimeSales",Icon:Gi,price:I?.totalAmount||0,className:"text-white dark:text-emerald-100 bg-emerald-600",loading:K})]}),y.jsxs("div",{className:"grid gap-4 md:grid-cols-2 xl:grid-cols-4",children:[y.jsx(Ne,{title:"Total Order",Icon:Ki,loading:T,quantity:D?.totalOrder||0,className:"text-orange-600 dark:text-orange-100 bg-orange-100 dark:bg-orange-500"}),y.jsx(Ne,{title:i("OrderPending"),Icon:jo,loading:T,quantity:D?.totalPendingOrder?.count||0,amount:D?.totalPendingOrder?.total||0,className:"text-blue-600 dark:text-blue-100 bg-blue-100 dark:bg-blue-500"}),y.jsx(Ne,{title:i("OrderProcessing"),Icon:No,loading:T,quantity:D?.totalProcessingOrder||0,className:"text-teal-600 dark:text-teal-100 bg-teal-100 dark:bg-teal-500"}),y.jsx(Ne,{title:i("OrderDelivered"),Icon:Vo,loading:T,quantity:D?.totalDeliveredOrder||0,className:"text-emerald-600 dark:text-emerald-100 bg-emerald-100 dark:bg-emerald-500"})]}),y.jsxs("div",{className:"grid gap-4 md:grid-cols-2 my-8",children:[y.jsx(wn,{mode:t,loading:K,title:i("WeeklySales"),children:y.jsx(kd,{salesReport:l})}),y.jsx(wn,{mode:t,loading:S,title:i("BestSellingProducts"),children:y.jsx(Md,{data:M})})]})]}),y.jsx(Zi,{children:i("RecentOrder")}),A?y.jsx(Yo,{row:5,col:4}):w?y.jsx("span",{className:"text-center mx-auto text-red-500",children:w}):z?.length!==0?y.jsxs(q.TableContainer,{className:"mb-8",children:[y.jsxs(q.Table,{children:[y.jsx(q.TableHeader,{children:y.jsxs("tr",{children:[y.jsx(q.TableCell,{children:i("InvoiceNo")}),y.jsx(q.TableCell,{children:i("TimeTbl")}),y.jsxs(q.TableCell,{children:[i("CustomerName")," "]}),y.jsxs(q.TableCell,{children:[" ",i("MethodTbl")," "]}),y.jsxs(q.TableCell,{children:[" ",i("AmountTbl")," "]}),y.jsx(q.TableCell,{children:i("OderStatusTbl")}),y.jsx(q.TableCell,{children:i("ActionTbl")}),y.jsx(q.TableCell,{className:"text-right",children:i("InvoiceTbl")})]})}),y.jsx(Xo,{orders:E})]}),y.jsx(q.TableFooter,{children:y.jsx(q.Pagination,{totalResults:P?.totalOrder,resultsPerPage:8,onChange:s,label:"Table navigation"})})]}):y.jsx(Uo,{title:"Sorry, There are no orders right now."})]})};export{Ud as default};
