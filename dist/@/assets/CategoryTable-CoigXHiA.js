import{r as f,S as Q,i as X,j as e,f as m,k as q}from"./index-DD5OQCzb.js";import{u as K,S as Z,I as y}from"./Layout-f_j_aP34.js";import{T as ee,C as se,S as ae}from"./BulkActionDrawer-BEXxVAnh.js";import{T as $,D as te,u as ne,M as re}from"./DrawerButton-C1kY46U5.js";import{D as le,E as ie}from"./EditDeleteButton-B2PjzFtp.js";import{T as de}from"./ParentCategory-BL1vwhP5.js";import{a as B,n as k}from"./toast-C_V_NPJL.js";import{u as oe,E as z}from"./index.esm-BPZGYcl8.js";import{I as me}from"./InputArea-Cu6xCoGw.js";import{L as U}from"./LabelArea-Zz4acQmF.js";import{S as pe}from"./SwitchToggle-CiShsGtJ.js";import{U as ce}from"./Uploader-Bz_6zC0v.js";import{C as P}from"./ProductServices-CGXRs0W4.js";import{u as xe}from"./useTranslationValue-d_-eYXcs.js";const ue=(i,p)=>{const{isDrawerOpen:n,closeDrawer:b,setIsUpdate:c,lang:I}=f.useContext(Q),[d,C]=f.useState({}),[S,T]=f.useState(""),[_,x]=f.useState(""),[D,s]=f.useState([]),[r,j]=f.useState("en"),[o,w]=f.useState(!0),[A,E]=f.useState(""),[L,v]=f.useState(!1),{handlerTextTranslateHandler:H}=xe(),{register:t,handleSubmit:u,setValue:a,clearErrors:h,reset:F,formState:{errors:W}}=oe(),Y=async({name:g,description:l})=>{var M,R;try{v(!0);const N=await H(g,r,d==null?void 0:d.name),J=await H(l,r,d==null?void 0:d.description),V={name:{...N,[r]:g},description:{...J,[r]:l||""},parentId:S||void 0,parentName:A||"Home",icon:_,status:o?"show":"hide",lang:r};if(i){const O=await P.updateCategory(i,V);c(!0),v(!1),k(O.message),b(),F()}else{const O=await P.addCategory(V);c(!0),v(!1),k(O.message),b()}}catch(N){v(!1),B(N?(R=(M=N==null?void 0:N.response)==null?void 0:M.data)==null?void 0:R.message:N==null?void 0:N.message),b()}},G=g=>{j(g),Object.keys(d).length>0&&(a("name",d.name[g||"en"]),a("description",d.description[g||"en"]))};return f.useEffect(()=>{var g;if(!n){C({}),a("name"),a("parentId"),a("parentName"),a("description"),a("icon"),x(""),w(!0),h("name"),h("parentId"),h("parentName"),h("description"),E("Home"),j(I),a("language",r),p!==void 0&&((g=p[0])==null?void 0:g._id)!==void 0&&T(p[0]._id);return}i&&(async()=>{try{const l=await P.getCategoryById(i);l&&(C(l),a("name",l.name[r||"en"]),a("description",l.description[r||"en"]),a("language",r),a("parentId",l.parentId),a("parentName",l.parentName),E(l.parentName),T(l.parentId),x(l.icon),w(l.status==="show"))}catch(l){B(l?l.response.data.message:l.message)}})()},[i,a,n,r,h,p,I]),{register:t,handleSubmit:u,onSubmit:Y,errors:W,imageUrl:_,setImageUrl:x,children:D,setChildren:s,published:o,setPublished:w,checked:S,setChecked:T,isSubmitting:L,selectCategoryName:A,setSelectCategoryName:E,handleSelectLanguage:G}},he=({id:i,data:p})=>{const{t:n}=X(),{checked:b,register:c,onSubmit:I,handleSubmit:d,errors:C,imageUrl:S,setImageUrl:T,published:_,setPublished:x,setChecked:D,selectCategoryName:s,setSelectCategoryName:r,handleSelectLanguage:j,isSubmitting:o}=ue(i,p),{showingTranslateValue:w}=K(),A=`
  .rc-tree-child-tree {
    display: hidden;
  }
  .node-motion {
    transition: all .3s;
    overflow-y: hidden;
  }
`,E={motionName:"node-motion",motionAppear:!1,onAppearStart:t=>({height:0}),onAppearActive:t=>({height:t.scrollHeight}),onLeaveStart:t=>({height:t.offsetHeight}),onLeaveActive:()=>({height:0})},L=t=>{let u=[];for(let a of t)u.push({title:w(a.name),key:a._id,children:a.children.length>0&&L(a.children)});return u},v=(t,u)=>{var a;return t._id===u?t:(a=t==null?void 0:t.children)==null?void 0:a.reduce((h,F)=>h??v(F,u),void 0)},H=async t=>{if(t!==void 0)if(i){const u=await P.getCategoryById(t);if(i===t)return B("This can't be select as a parent category!");if(i===u.parentId)return B("This can't be select as a parent category!");{if(t===void 0)return;D(t);const a=p[0],h=v(a,t);r(w(h==null?void 0:h.name))}}else{if(t===void 0)return;D(t);const u=p[0],a=v(u,t);r(w(a==null?void 0:a.name))}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:i?e.jsx($,{register:c,handleSelectLanguage:j,title:n("UpdateCategory"),description:n("UpdateCategoryDescription")}):e.jsx($,{register:c,handleSelectLanguage:j,title:n("AddCategoryTitle"),description:n("AddCategoryDescription")})}),e.jsx(Z,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:d(I),children:[e.jsxs("div",{className:"p-6 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(U,{label:n("Name")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(me,{required:!0,register:c,label:"Category title",name:"name",type:"text",placeholder:n("ParentCategoryPlaceholder")}),e.jsx(z,{errorName:C.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(U,{label:n("Description")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(ee,{register:c,label:"Description",name:"description",type:"text",placeholder:"Category Description"}),e.jsx(z,{errorName:C.description})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(U,{label:n("ParentCategory")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4 relative",children:[e.jsx(m.Input,{readOnly:!0,...c("parent",{required:!1}),name:"parent",value:s||"Home",placeholder:n("ParentCategory"),type:"text"}),e.jsxs("div",{className:"draggable-demo capitalize",children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:A}}),e.jsx(de,{expandAction:"click",treeData:L(p),selectedKeys:[b],onSelect:t=>H(t[0]),motion:E,animation:"slide-up"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(U,{label:n("CategoryIcon")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(ce,{imageUrl:S,setImageUrl:T,folder:"category",targetWidth:238,targetHeight:238})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(U,{label:n("Published")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(pe,{handleProcess:x,processOption:_})})]})]}),e.jsx(te,{id:i,title:"Category",isSubmitting:o})]})})]})},Ee=({data:i,lang:p,isCheck:n,categories:b,setIsCheck:c,useParamId:I,showChild:d})=>{const{title:C,serviceId:S,handleModalOpen:T,handleUpdate:_}=ne(),{showingTranslateValue:x}=K(),D=s=>{const{id:r,checked:j}=s.target;c([...n,r]),j||c(n.filter(o=>o!==r))};return e.jsxs(e.Fragment,{children:[(n==null?void 0:n.length)<1&&e.jsx(le,{useParamId:I,id:S,title:C}),e.jsx(re,{children:e.jsx(he,{id:S,data:i,lang:p})}),e.jsx(m.TableBody,{children:b==null?void 0:b.map(s=>{var r,j;return e.jsxs(m.TableRow,{children:[e.jsx(m.TableCell,{children:e.jsx(se,{type:"checkbox",name:"category",id:s._id,handleClick:D,isChecked:n==null?void 0:n.includes(s._id)})}),e.jsx(m.TableCell,{className:"font-semibold uppercase text-xs",children:(r=s==null?void 0:s._id)==null?void 0:r.substring(20,24)}),e.jsx(m.TableCell,{children:s!=null&&s.icon?e.jsx(m.Avatar,{className:"hidden mr-3 md:block bg-gray-50 p-1",src:s==null?void 0:s.icon,alt:s==null?void 0:s.parent}):e.jsx(m.Avatar,{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product",className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none"})}),e.jsx(m.TableCell,{className:"font-medium text-sm ",children:(s==null?void 0:s.children.length)>0?e.jsxs(q,{to:`/categories/${s==null?void 0:s._id}`,className:"text-blue-700",children:[x(s==null?void 0:s.name),e.jsx(e.Fragment,{children:d&&e.jsxs(e.Fragment,{children:[" ",e.jsx("div",{className:"pl-2 ",children:(j=s==null?void 0:s.children)==null?void 0:j.map(o=>e.jsx("div",{children:e.jsx(q,{to:`/categories/${o==null?void 0:o._id}`,className:"text-blue-700",children:e.jsxs("div",{className:"flex text-xs items-center  text-blue-800",children:[e.jsx("span",{className:" text-xs text-gray-500 pr-1",children:e.jsx(y,{})}),e.jsx("span",{className:"text-gray-500",children:x(o.name)})]})})},o._id))})]})})]}):e.jsx("span",{children:x(s==null?void 0:s.name)})}),e.jsx(m.TableCell,{className:"text-sm",children:x(s==null?void 0:s.description)}),e.jsx(m.TableCell,{className:"text-center",children:e.jsx(ae,{id:s._id,category:!0,status:s.status})}),e.jsx(m.TableCell,{children:e.jsx(ie,{id:s==null?void 0:s._id,parent:s,isCheck:n,children:s==null?void 0:s.children,handleUpdate:_,handleModalOpen:T,title:x(s==null?void 0:s.name)})})]},s._id)})})]})};export{he as C,Ee as a};
