import{r as c,S as h,j as s,f as p}from"./index-DD5OQCzb.js";import{O as x}from"./OrderServices-DB9CIA_J.js";import{n as g,a as f}from"./toast-C_V_NPJL.js";const S=({id:n,order:t})=>{const{setIsUpdate:l}=c.useContext(h),u=(e,i)=>{x.updateOrder(e,{status:i}).then(a=>{g(a.message),l(!0)}).catch(a=>f(a.message))};return s.jsx(s.Fragment,{children:s.jsxs(p.Select,{onChange:e=>u(n,e.target.value),className:"h-8",children:[s.jsx("option",{value:"status",defaultValue:!0,hidden:!0,children:t==null?void 0:t.status}),s.jsx("option",{defaultValue:(t==null?void 0:t.status)==="Delivered",value:"Delivered",children:"Delivered"}),s.jsx("option",{defaultValue:(t==null?void 0:t.status)==="Pending",value:"Pending",children:"Pending"}),s.jsx("option",{defaultValue:(t==null?void 0:t.status)==="Processing",value:"Processing",children:"Processing"}),s.jsx("option",{defaultValue:(t==null?void 0:t.status)==="Cancel",value:"Cancel",children:"Cancel"})]})})};export{S};
