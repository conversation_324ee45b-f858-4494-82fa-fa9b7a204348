import{j as n,f as r}from"./index-DD5OQCzb.js";const i=({status:e})=>n.jsx(n.Fragment,{children:n.jsxs("span",{className:"font-serif",children:[(e==="Pending"||e==="Inactive")&&n.jsx(r.<PERSON>,{type:"warning",children:e}),e==="Waiting for Password Reset"&&n.jsx(r.<PERSON>,{type:"warning",children:e}),e==="Processing"&&n.jsx(r.<PERSON>,{children:e}),(e==="Delivered"||e==="Active")&&n.jsx(r.<PERSON>,{type:"success",children:e}),e==="Cancel"&&n.jsx(r.<PERSON>,{type:"danger",children:e}),e==="POS-Completed"&&n.jsx(r.<PERSON>,{className:"dark:bg-teal-900 bg-teal-100",children:e})]})});export{i as S};
