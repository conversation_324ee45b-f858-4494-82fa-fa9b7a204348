import{j as e,h as s,s as U,r as u,S as H,l as O}from"./index-DpMxJ5Hx.js";import{S as q,u as S,h as z,g as G,e as J,f as K}from"./Layout-B-UGxzbM.js";import{T as w,D as Q,u as k,M as D}from"./DrawerButton-CKK8nF3h.js";import{C as B,S as W,B as X}from"./BulkActionDrawer-ChuFcR4n.js";import{D as F,E as Y}from"./EditDeleteButton-DsdeAqDJ.js";import{E as y}from"./index.esm-B8kiXavo.js";import{I as Z}from"./InputArea-BGdt-vbi.js";import{L as C}from"./LabelArea-CQP0v-a8.js";import{S as $}from"./SwitchToggle-CmuQM---.js";import{u as ee}from"./useAttributeSubmit-2eomMKE9.js";import{L as se}from"./Loading-DJVLGGxg.js";import{N as te}from"./NotFound-BXAjvYR4.js";import{P as ae}from"./PageTitle-D-hGib5s.js";import{u as T}from"./useAsync-Hr4bbxCm.js";import{u as le}from"./useFilter-Qjmg-ZWR.js";import{A as v}from"./ProductServices-CnM1m97m.js";import{A as re}from"./AnimatedContent-DVQRys_r.js";import"./iconBase-BUmmAlr8.js";import"./SelectLanguageTwo-CnZMFe5S.js";import"./spinner-CkndCogW.js";import"./index.prod-BR0InCj9.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";import"./toast-Be5Wd3gm.js";import"./ParentCategory-C2ZW3iQo.js";import"./useDisableForDemo-DczgqPm6.js";import"./AdminServices-CIs7colP.js";import"./Tooltip-BQ_BZ_s8.js";import"./useTranslationValue-DM8I18Uu.js";import"./index-C148XJoK.js";const P=({id:l})=>{const{handleSubmit:p,onSubmits:a,register:r,errors:d,published:m,isSubmitting:o,setPublished:n,handleSelectLanguage:i}=ee(l);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:l?e.jsx(w,{register:r,handleSelectLanguage:i,title:"Add/Update Attribute Valu",description:"Add your attribute values and necessary information from here"}):e.jsx(w,{register:r,handleSelectLanguage:i,title:"Add/Update Attribute Values",description:"Add your attribute values and necessary information from here"})}),e.jsx(q,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:p(a),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 items-center",children:[e.jsx(C,{label:"Display Name"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(Z,{required:!0,register:r,label:"Display Name",name:"name",type:"text",placeholder:"Color or Size or Dimension or Material or Fabric"}),e.jsx(y,{errorName:d.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 items-center",children:[e.jsx(C,{label:"Published"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx($,{handleProcess:n,processOption:m}),e.jsx(y,{errorName:d.published})]})]})]}),e.jsx(Q,{id:l,title:"Attribute",isSubmitting:o})]})})]})},ie=({att:l,loading:p,isCheck:a,setIsCheck:r,childAttributes:d})=>{const{title:m,serviceId:o,handleModalOpen:n,handleUpdate:i}=k(),{showingTranslateValue:x}=S(),j=t=>{const{id:c,checked:g}=t.target;r([...a,c]),g||r(a.filter(f=>f!==c))};return e.jsxs(e.Fragment,{children:[a.length<1&&e.jsx(F,{id:o,title:m}),a.length<2&&e.jsx(D,{children:e.jsx(P,{id:o})}),e.jsx(s.TableBody,{children:d?.map((t,c)=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx(B,{type:"checkbox",name:"child-attribute",id:t._id,handleClick:j,isChecked:a?.includes(t._id)})}),e.jsx(s.TableCell,{className:"font-semibold uppercase text-xs",children:t?._id?.substring(20,24)}),e.jsx(s.TableCell,{className:"font-medium text-sm",children:x(t?.name)}),e.jsx(s.TableCell,{className:"font-medium text-sm",children:l?.option}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(W,{id:t._id,status:t.status})}),e.jsx(s.TableCell,{children:e.jsx(Y,{id:t._id,isCheck:a,setIsCheck:r,handleUpdate:i,handleModalOpen:n,title:x(t.name)})})]},c+1))})]})},_e=()=>{let{id:l}=U();const{handleDeleteMany:p,allId:a,serviceId:r,handleUpdateMany:d}=k(),{toggleDrawer:m,lang:o}=u.useContext(H),{data:n,loading:i,error:x}=T(()=>v.getAttributeById(l)),{showingTranslateValue:j}=S(),{data:t}=T(()=>v.getAllAttributes({type:"attribute",option:"Dropdown",option1:"Radio"})),{totalResults:c,resultsPerPage:g,dataTable:f,serviceData:E,handleChangePage:I}=le(n?.variants),[N,V]=u.useState(!1),[h,b]=u.useState([]),[M,_]=u.useState([]),L=()=>{V(!N),b(n?.variants?.map(A=>A._id)),N&&b([])};return u.useEffect(()=>{const A=t?.filter(R=>R._id!==l);_(A)},[t,l]),e.jsxs(e.Fragment,{children:[e.jsx(ae,{children:"Attributes Values"}),e.jsx(F,{ids:a,setIsCheck:b,title:"Selected Attribute Value(s)"}),e.jsx(X,{attributes:M,ids:a,title:"Attribute Value(s)",childId:l}),e.jsx(D,{children:e.jsx(P,{id:r})}),e.jsxs(re,{children:[e.jsx("div",{className:"flex items-center pb-4",children:e.jsxs("ol",{className:"flex items-center w-full overflow-hidden font-serif",children:[e.jsx("li",{className:"text-sm pr-1 transition duration-200 ease-in cursor-pointer hover:text-emerald-500 font-semibold",children:e.jsx(O,{className:"text-blue-700",to:"/attributes",children:"Attributes"})}),e.jsxs("span",{className:"flex items-center font-serif dark:text-gray-400",children:[e.jsxs("li",{className:"text-sm mt-[1px]",children:[" ",e.jsx(z,{})," "]}),e.jsx("li",{className:"text-sm pl-1 font-semibold dark:text-gray-400",children:!i&&j(n?.title)})]})]})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsxs(s.CardBody,{className:"py-3 grid gap-4 justify-end lg:gap-4 xl:gap-4 md:flex xl:flex",children:[e.jsx("div",{className:"flex justify-end items-end",children:e.jsxs(s.Button,{onClick:m,className:"rounded-md h-12",children:[e.jsx("span",{className:"mr-3",children:e.jsx(G,{})}),"Add Value"]})}),e.jsx("div",{className:"w-full md:w-24 lg:w-24 xl:w-24",children:e.jsxs(s.Button,{disabled:h.length<1,onClick:()=>d(h),className:"w-full rounded-md h-12",children:[e.jsx(J,{}),"Bulk Action"]})}),e.jsxs(s.Button,{disabled:h.length<1,onClick:()=>p(h),className:"rounded-md h-12 bg-red-500",children:[e.jsx("span",{className:"mr-3",children:e.jsx(K,{})}),"Delete"]})]})})]}),i?e.jsx(se,{loading:i}):x?e.jsx("span",{className:"text-center mx-auto text-red-500",children:x}):E?.length!==0?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(B,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:L,isChecked:N})}),e.jsx(s.TableCell,{children:"Id"}),e.jsx(s.TableCell,{children:"Name"}),e.jsx(s.TableCell,{children:"Type"}),e.jsx(s.TableCell,{className:"text-center",children:"Status"}),e.jsx(s.TableCell,{className:"text-right",children:"Actions"})]})}),e.jsx(ie,{att:n,lang:o,loading:i,isCheck:h,setIsCheck:b,childAttributes:f})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:c,resultsPerPage:g,onChange:I,label:"Table navigation"})})]}):e.jsx(te,{title:"Sorry, There are no attributes right now."})]})};export{_e as default};
