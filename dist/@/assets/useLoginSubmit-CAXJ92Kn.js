import{r as p,A as L,q as N,u as b,h as g}from"./index-DD5OQCzb.js";import{u as O}from"./index.esm-BPZGYcl8.js";import{A as t}from"./AdminServices-Crgje1Fu.js";import{n as o,a as w}from"./toast-C_V_NPJL.js";const J=()=>{const[d,a]=p.useState(!1),{dispatch:i}=p.useContext(L),n=N(),e=b(),{register:S,handleSubmit:y,formState:{errors:l}}=O();return{onSubmit:async({name:h,email:r,verifyEmail:x,password:c,role:A})=>{var u,f;a(!0);const m=.5;try{if(e.pathname==="/login"){const s=await t.loginAdmin({email:r,password:c});s&&(o("Login Success!"),i({type:"USER_LOGIN",payload:s}),g.set("adminInfo",JSON.stringify(s),{expires:m,sameSite:"None",secure:!0}),n.replace("/dashboard"))}if(e.pathname==="/signup"){const s=await t.registerAdmin({name:h,email:r,password:c,role:A});s&&(o("Register Success!"),i({type:"USER_LOGIN",payload:s}),g.set("adminInfo",JSON.stringify(s),{expires:m,sameSite:"None",secure:!0}),n.replace("/"))}if(e.pathname==="/forgot-password"){const s=await t.forgetPassword({verifyEmail:x});o(s.message)}}catch(s){w(((f=(u=s==null?void 0:s.response)==null?void 0:u.data)==null?void 0:f.message)||(s==null?void 0:s.message))}finally{a(!1)}},register:S,handleSubmit:y,errors:l,loading:d}};export{J as u};
