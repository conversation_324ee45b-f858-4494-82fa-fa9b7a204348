import{j as e,f as s,s as o,i as d}from"./index-DD5OQCzb.js";import{u as h,m as j}from"./Layout-f_j_aP34.js";import{u}from"./useAsync-CdFiuEZy.js";import{O as b}from"./OrderServices-DB9CIA_J.js";import{u as p}from"./useFilter-BHZ5O6jw.js";import{P as C}from"./PageTitle-DUWCiaui.js";import{L as T}from"./Loading-D8j96Z5Y.js";import{S as g}from"./Status-dJqttBU1.js";import{S as f}from"./SelectStatus-FXtu805S.js";import"./iconBase-DTk8F31e.js";import"./ProductServices-CGXRs0W4.js";import"./index-0EvDzr9j.js";import"./useDisableForDemo-aTnQzb5-.js";import"./toast-C_V_NPJL.js";import"./CouponServices-BvJiM6D0.js";import"./CurrencyServices-CicNeQxs.js";const N=({orders:r})=>{const{showDateTimeFormat:l,getNumberTwo:n,currency:a}=h();return e.jsx(e.Fragment,{children:e.jsx(s.TableBody,{children:r==null?void 0:r.map(t=>{var i,m,c;return e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx("span",{className:"font-semibold uppercase text-xs",children:(i=t==null?void 0:t._id)==null?void 0:i.substring(20,24)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:l(t.createdAt)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:(m=t==null?void 0:t.user_info)==null?void 0:m.address})}),e.jsxs(s.TableCell,{children:[" ",e.jsx("span",{className:"text-sm",children:(c=t.user_info)==null?void 0:c.contact})," "]}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm font-semibold",children:t.paymentMethod})}),e.jsxs(s.TableCell,{children:[" ",e.jsxs("span",{className:"text-sm font-semibold",children:[a,n(t.total)]})," "]}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(g,{status:t.status})}),e.jsx(s.TableCell,{className:"text-right",children:e.jsx(f,{id:t._id,order:t})})]},t._id)})})})},k=()=>{const{id:r}=o(),{t:l}=d(),{data:n,loading:a,error:t}=u(()=>b.getOrderCustomer(r)),{handleChangePage:i,totalResults:m,resultsPerPage:c,dataTable:x}=p(n);return e.jsxs(e.Fragment,{children:[e.jsx(C,{children:l("CustomerOrderList")}),a&&e.jsx(T,{loading:a}),!t&&!a&&x.length===0&&e.jsx("div",{className:"w-full bg-white rounded-md dark:bg-gray-800",children:e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("span",{className:"flex justify-center my-30 text-red-500 font-semibold text-6xl",children:e.jsx(j,{})}),e.jsx("h2",{className:"font-medium text-base mt-4 text-gray-600",children:l("CustomerOrderEmpty")})]})}),n.length>0&&!t&&!a?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsxs(s.TableCell,{children:[" ",l("CustomerOrderId")," "]}),e.jsx(s.TableCell,{children:l("CustomerOrderTime")}),e.jsx(s.TableCell,{children:l("CustomerShippingAddress")}),e.jsxs(s.TableCell,{children:[l("Phone")," "]}),e.jsxs(s.TableCell,{children:[l("CustomerOrderMethod")," "]}),e.jsx(s.TableCell,{children:l("Amount")}),e.jsxs(s.TableCell,{className:"text-center",children:[" ",l("CustomerOrderStatus")," "]}),e.jsx(s.TableCell,{className:"text-center",children:l("CustomerOrderAction")})]})}),e.jsx(N,{orders:x})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:m,resultsPerPage:c,onChange:i,label:"Table navigation"})})]}):null]})};export{k as default};
