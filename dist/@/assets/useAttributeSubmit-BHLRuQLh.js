import{u as R,r as m,S as U}from"./index-DD5OQCzb.js";import{u as _}from"./index.esm-BPZGYcl8.js";import{A as f}from"./ProductServices-CGXRs0W4.js";import{a as g,n as y}from"./toast-C_V_NPJL.js";import{u as $}from"./DrawerButton-C1kY46U5.js";import{u as z}from"./useTranslationValue-d_-eYXcs.js";const X=r=>{const u=R(),{isDrawerOpen:x,closeDrawer:c,setIsUpdate:d,lang:C}=m.useContext(U),[l,h]=m.useState([]),[s,I]=m.useState("en"),[i,V]=m.useState({}),[w,E]=m.useState(!1),[D,o]=m.useState(!1),{setServiceId:b}=$(),{handlerTextTranslateHandler:v}=z();let A=[];(async()=>{for(let t=0;t<l.length;t++){const a=await v(l[t],s);A=[...A,{name:{[s]:l[t],...a}}]}})();const{handleSubmit:L,register:j,setValue:n,clearErrors:S,formState:{errors:B}}=_(),k=async({title:t,name:a,option:e})=>{try{if(o(!0),!r&&l.length===0){g("Minimum one value is required for add attribute!");return}const p=await v(t,s,i==null?void 0:i.title),P=await v(a,s,i==null?void 0:i.name),O={title:{...p,[s]:t},name:{...P,[s]:a},variants:A,option:e,type:"attribute",lang:s};if(r){const T=await f.updateAttributes(r,O);d(!0),o(!1),y(T.message),c(),b()}else{const T=await f.addAttribute(O);d(!0),o(!1),y(T.message),c(),b()}}catch(p){g(p?p.response.data.message:p.message),c(),o(!1),b()}},q=async({name:t})=>{try{if(o(!0),r){const a=await f.updateChildAttributes({ids:u.pathname.split("/")[2],id:r},{name:{[s]:t},status:w?"show":"hide"});d(!0),o(!1),y(a.message),c()}else{const a=await f.addChildAttribute(u.pathname.split("/")[2],{name:{[s]:t},status:w?"show":"hide"});d(!0),o(!1),y(a.message),c()}}catch(a){g(a?a.response.data.message:a.message),c(),o(!1),b()}},F=t=>{I(t),Object.keys(i).length>0&&(n("title",i.title[t||"en"]),n("name",i.name[t||"en"]))},H=t=>{h([...l.filter((a,e)=>e!==t)])},M=t=>{t.preventDefault(),t.target.value!==""&&(h([...l,t.target.value]),t.target.value="")};return m.useEffect(()=>{if(!x){V({}),n("title"),n("name"),n("option"),S("title"),S("name"),S("option"),h([]),I(C),n("language",s);return}u.pathname==="/attributes"&&r?(async()=>{var t,a;try{const e=await f.getAttributeById(r);e&&(V(e),n("title",e.title[s||"en"]),n("name",e.name[s||"en"]),n("option",e.option))}catch(e){g(((a=(t=e==null?void 0:e.response)==null?void 0:t.data)==null?void 0:a.message)||(e==null?void 0:e.message))}})():u.pathname===`/attributes/${u.pathname.split("/")[2]}`&&(async()=>{var t,a;try{const e=await f.getChildAttributeById({id:u.pathname.split("/")[2],ids:r});e&&(n("name",e.name[s||"en"]),E(e.status==="show"))}catch(e){g(((a=(t=e==null?void 0:e.response)==null?void 0:t.data)==null?void 0:a.message)||(e==null?void 0:e.message))}})()},[S,r,x,n,u,s,C]),{handleSubmit:L,onSubmits:q,onSubmit:k,register:j,errors:B,variants:l,setVariants:h,addVariant:M,removeVariant:H,published:w,setPublished:E,isSubmitting:D,handleSelectLanguage:F}};export{X as u};
