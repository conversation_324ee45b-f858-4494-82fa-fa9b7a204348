import{s as L,r as o,S as M,k as R,j as e,l as T,h as s}from"./index-DpMxJ5Hx.js";import{u as O,h as U,g as H,e as V,f as $}from"./Layout-B-UGxzbM.js";import{a as q}from"./CategoryTable-DSjXF0RY.js";import{B as z,C as G}from"./BulkActionDrawer-ChuFcR4n.js";import{D as J}from"./EditDeleteButton-DsdeAqDJ.js";import{L as K}from"./Loading-DJVLGGxg.js";import{N as Q}from"./NotFound-BXAjvYR4.js";import{P as W}from"./PageTitle-D-hGib5s.js";import{u as X}from"./useAsync-Hr4bbxCm.js";import{u as Y}from"./useFilter-Qjmg-ZWR.js";import{u as Z}from"./DrawerButton-CKK8nF3h.js";import{C as ee}from"./ProductServices-CnM1m97m.js";import{A as se}from"./AnimatedContent-DVQRys_r.js";import"./iconBase-BUmmAlr8.js";import"./ParentCategory-C2ZW3iQo.js";import"./toast-Be5Wd3gm.js";import"./index.esm-B8kiXavo.js";import"./InputArea-BGdt-vbi.js";import"./LabelArea-CQP0v-a8.js";import"./SwitchToggle-CmuQM---.js";import"./index.prod-BR0InCj9.js";import"./Uploader-sUWHaRyq.js";import"./_commonjs-dynamic-modules-CNspEXFA.js";import"./index-C148XJoK.js";import"./useTranslationValue-DM8I18Uu.js";import"./CouponServices-vUOVn0Wx.js";import"./CurrencyServices-Dk3mpScu.js";import"./useDisableForDemo-DczgqPm6.js";import"./spinner-CkndCogW.js";import"./AdminServices-CIs7colP.js";import"./Tooltip-BQ_BZ_s8.js";import"./SelectLanguageTwo-CnZMFe5S.js";const Ee=()=>{const{id:n}=L(),[p,N]=o.useState([]),[y,k]=o.useState([]),[j,w]=o.useState(!1),[c,m]=o.useState([]),{toggleDrawer:A,lang:g}=o.useContext(M),{handleDeleteMany:v,allId:f,handleUpdateMany:P}=Z(),{data:d,loading:h,error:C}=X(ee.getAllCategory),{showingTranslateValue:S}=O(),{t}=R();o.useEffect(()=>{const l=(r,a,u=[])=>{for(let i of a){if(i._id===r)return u.concat(i);const b=l(r,i?.children,u?.concat(i));if(b)return b}},x=(r,a)=>r._id===a?r:r?.children?.reduce((u,i)=>u??x(i,a),void 0);if(!h){const r=x(d[0],n),a=l(n,d[0]?.children);r?.children?.length>0&&(N(r?.children),k(a))}},[n,h,d,p]);const{totalResults:D,resultsPerPage:F,dataTable:B,serviceData:I,handleChangePage:E}=Y(p),_=()=>{w(!j),m(p?.map(l=>l._id)),j&&m([])};return e.jsxs(e.Fragment,{children:[e.jsx(W,{children:t("CategoryPageTitle")}),e.jsx(J,{ids:f,setIsCheck:m,category:!0}),e.jsx(z,{ids:f,title:"Child Categories",lang:g,data:d,childId:n}),e.jsxs(se,{children:[e.jsx("div",{className:"flex items-center pb-4",children:e.jsxs("ol",{className:"flex items-center w-full overflow-hidden font-serif",children:[e.jsx("li",{className:"text-sm pr-1 transition duration-200 ease-in cursor-pointer hover:text-emerald-500 font-semibold",children:e.jsx(T,{to:"/categories",children:t("Categories")})}),y?.map((l,x)=>e.jsxs("span",{className:"flex items-center font-serif",children:[e.jsxs("li",{className:"text-sm mt-[1px]",children:[" ",e.jsx(U,{})," "]}),e.jsx("li",{className:"text-sm pl-1 transition duration-200 ease-in cursor-pointer text-blue-700 hover:text-emerald-500 font-semibold ",children:e.jsx(T,{to:`/categories/${l._id}`,children:S(l?.name)})})]},x+1))]})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("div",{className:"flex justify-end items-end",children:[e.jsxs(s.Button,{onClick:A,className:"rounded-md h-12",children:[e.jsx("span",{className:"mr-3",children:e.jsx(H,{})}),t("AddCategory")]}),e.jsx("div",{className:"ml-3 w-full md:w-24 lg:w-24 xl:w-24",children:e.jsxs(s.Button,{disabled:c.length<1,onClick:()=>P(c),className:"w-full rounded-md h-12",children:[e.jsx(V,{}),t("BulkAction")]})}),e.jsxs(s.Button,{disabled:c.length<1,onClick:()=>v(c),className:"ml-3 rounded-md h-12 bg-red-500",children:[e.jsx("span",{className:"mr-3",children:e.jsx($,{})}),t("Delete")]})]})})})]}),h?e.jsx(K,{loading:h}):C?e.jsx("span",{className:"text-center mx-auto text-red-500",children:C}):I?.length!==0?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(G,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:_,isChecked:j})}),e.jsx(s.TableCell,{children:t("catIdTbl")}),e.jsx(s.TableCell,{children:t("catIconTbl")}),e.jsx(s.TableCell,{children:t("Name")}),e.jsx(s.TableCell,{children:t("Description")}),e.jsx(s.TableCell,{className:"text-center",children:t("catPublishedTbl")}),e.jsx(s.TableCell,{className:"text-right",children:t("catActionsTbl")})]})}),e.jsx(q,{categories:B,data:d,lang:g,isCheck:c,setIsCheck:m,useParamId:n})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:D,resultsPerPage:F,onChange:E,label:"Table navigation"})})]}):e.jsx(Q,{title:"Sorry, There are no categories right now."})]})};export{Ee as default};
