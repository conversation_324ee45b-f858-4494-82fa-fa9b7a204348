import{g as p}from"./index-DpMxJ5Hx.js";/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var f,u;function O(){if(u)return f;u=1;var i=Object.getOwnPropertySymbols,b=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;function g(t){if(t==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}function j(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de",Object.getOwnPropertyNames(t)[0]==="5")return!1;for(var s={},r=0;r<10;r++)s["_"+String.fromCharCode(r)]=r;var o=Object.getOwnPropertyNames(s).map(function(e){return s[e]});if(o.join("")!=="**********")return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach(function(e){n[e]=e}),Object.keys(Object.assign({},n)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}return f=j()?Object.assign:function(t,s){for(var r,o=g(t),n,e=1;e<arguments.length;e++){r=Object(arguments[e]);for(var c in r)b.call(r,c)&&(o[c]=r[c]);if(i){n=i(r);for(var a=0;a<n.length;a++)l.call(r,n[a])&&(o[n[a]]=r[n[a]])}}return o},f}var m=O();const h=p(m);export{h as o};
