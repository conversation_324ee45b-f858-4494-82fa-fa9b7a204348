import{r as b,S as E,j as e,t as i,f as s}from"./index-DD5OQCzb.js";import{l as U,e as R,f as L,g as O}from"./Layout-f_j_aP34.js";import{C as F,S as q,B as H}from"./BulkActionDrawer-BEXxVAnh.js";import{T as k,D as V,u as I,M as P}from"./DrawerButton-C1kY46U5.js";import{u as z,E as v}from"./index.esm-BPZGYcl8.js";import{I as A}from"./InputArea-Cu6xCoGw.js";import{L as T}from"./LabelArea-Zz4acQmF.js";import{S as G}from"./SwitchToggle-CiShsGtJ.js";import{C as w}from"./CurrencyServices-CicNeQxs.js";import{a as D,n as B}from"./toast-C_V_NPJL.js";import{D as M,E as J}from"./EditDeleteButton-B2PjzFtp.js";import{P as K}from"./PageTitle-DUWCiaui.js";import{u as Q}from"./useAsync-CdFiuEZy.js";import{u as W}from"./useFilter-BHZ5O6jw.js";import{T as X}from"./TableLoading-D3D_P-TQ.js";import{N as Y}from"./NotFound-DG_8Itz7.js";import{A as Z}from"./AnimatedContent-DbKaf3qr.js";import"./iconBase-DTk8F31e.js";import"./index.prod-CisttSXz.js";import"./ProductServices-CGXRs0W4.js";import"./index-0EvDzr9j.js";import"./CouponServices-BvJiM6D0.js";import"./ParentCategory-BL1vwhP5.js";import"./useDisableForDemo-aTnQzb5-.js";import"./SelectLanguageTwo-CQlbeojL.js";import"./spinner-CkndCogW.js";import"./AdminServices-Crgje1Fu.js";import"./Tooltip-DrdTd94n.js";const $=r=>{const[n,c]=b.useState(!0),[h,t]=b.useState(!1),{isDrawerOpen:x,closeDrawer:d,setIsUpdate:g}=b.useContext(E),{handleSubmit:l,register:j,setValue:u,clearErrors:p,formState:{errors:N}}=z(),C=async({symbol:f,name:m})=>{var a,y;try{t(!0);const o={name:m,symbol:f,status:n?"show":"hide"};if(r){const S=await w.updateCurrency(r,o);g(!0),t(!1),B(S.message),d()}else{const S=await w.addCurrency(o);g(!0),t(!1),B(S.message),d()}}catch(o){t(!1),D(((y=(a=o==null?void 0:o.response)==null?void 0:a.data)==null?void 0:y.message)||(o==null?void 0:o.message)),d()}};return b.useEffect(()=>{if(!x){u("name"),u("symbol"),c(!0),p("symbol"),p("name");return}r&&(async()=>{var f,m;try{const a=await w.getCurrencyById(r);a&&(u("name",a.name),u("symbol",a.symbol),c(a.status==="show"))}catch(a){D(((m=(f=a==null?void 0:a.response)==null?void 0:f.data)==null?void 0:m.message)||(a==null?void 0:a.message))}})()},[p,r,x,u]),{errors:N,onSubmit:C,register:j,status:n,setStatus:c,isSubmitting:h,handleSubmit:l}},_=({id:r})=>{const{errors:n,onSubmit:c,register:h,status:t,setStatus:x,isSubmitting:d,handleSubmit:g}=$(r);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:r?e.jsx(k,{title:i("UpdateCurrency"),description:i("UpdateCurrencyText")}):e.jsx(k,{title:i("AddCurrency"),description:i("AddCurrencyText")})}),e.jsx(U.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:g(c),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(T,{label:i("CurrenciesName")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(A,{required:!0,register:h,label:"Name",name:"name",type:"text",placeholder:"Name"}),e.jsx(v,{errorName:n.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(T,{label:i("CurrenciesSymbol")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(A,{required:!0,register:h,label:"Symbol",name:"symbol",type:"text",placeholder:"Symbol"}),e.jsx(v,{errorName:n.symbol})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(T,{label:i("CurrenciesEnabled")}),e.jsx("div",{className:"col-span-8 sm:col-span-1 text-align-left",children:e.jsx(G,{processOption:t,handleProcess:x})})]})]}),e.jsx(V,{id:r,title:"Currency",isSubmitting:d})]})})]})},ee=({currency:r,isCheck:n,setIsCheck:c})=>{const{title:h,serviceId:t,handleModalOpen:x,handleUpdate:d}=I(),g=l=>{const{id:j,checked:u}=l.target;c([...n,j]),u||c(n.filter(p=>p!==j))};return e.jsxs(e.Fragment,{children:[n.length<1&&e.jsx(M,{id:t,title:h}),e.jsx(P,{children:e.jsx(_,{id:t})}),e.jsx(s.TableBody,{children:r==null?void 0:r.map(l=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx(F,{type:"checkbox",name:l.symbol,id:l._id,handleClick:g,isChecked:n.includes(l._id)})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx("span",{className:"font-medium text-sm",children:l.name})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx("span",{className:"font-medium text-sm",children:l.symbol})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(q,{id:l._id,status:l.status,currencyStatusName:"status"})}),e.jsx(s.TableCell,{children:e.jsx(J,{title:l.name,id:l._id,handleUpdate:d,handleModalOpen:x})})]},l._id))})]})},De=()=>{const{toggleDrawer:r}=b.useContext(E),{allId:n,handleUpdateMany:c,handleDeleteMany:h}=I(),{data:t,loading:x,error:d}=Q(w.getAllCurrency),{totalResults:g,resultsPerPage:l,dataTable:j,handleChangePage:u,handleSubmitCurrency:p,currencyRef:N}=W(t),[C,f]=b.useState(!1),[m,a]=b.useState([]),y=()=>{f(!C),a(t.map(o=>o._id)),C&&a([])};return e.jsxs(e.Fragment,{children:[e.jsx(K,{children:"Currencies"}),e.jsx(H,{ids:n,title:"Currencies"}),e.jsx(P,{children:e.jsx(_,{})}),e.jsx(M,{ids:n,setIsCheck:a,title:"Selected Currencies"}),e.jsx(Z,{children:e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:p,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex md:justify-between",children:[e.jsx("div",{className:"w-full",children:e.jsx(s.Input,{ref:N,type:"search",placeholder:i("SearchIsoCode")})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(s.Button,{disabled:m.length<1,onClick:()=>c(m),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(R,{})}),"Bulk Action"]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32 mr-3 mb-3 lg:mb-0",children:e.jsxs(s.Button,{disabled:m.length<1,onClick:()=>h(m),className:"w-full rounded-md h-12 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(L,{})}),"Delete"]})}),e.jsxs(s.Button,{onClick:r,className:"rounded-md h-12 w-48",children:[e.jsx("span",{className:"mr-2",children:e.jsx(O,{})}),"Add Currency"]})]})]})})})}),x?e.jsx(X,{row:12,col:7,width:163,height:20}):d?e.jsx("span",{className:"text-center mx-auto text-red-500",children:d}):t.length!==0&&e.jsxs(s.TableContainer,{className:"mb-8 rounded-b-lg",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(F,{type:"checkbox",name:"selectAll",id:"selectAll",isChecked:C,handleClick:y})}),e.jsx(s.TableCell,{className:"text-center",children:i("CurrenciesName")}),e.jsx(s.TableCell,{className:"text-center",children:i("CurrenciesSymbol")}),e.jsx(s.TableCell,{className:"text-center",children:i("CurrenciesEnabled")}),e.jsx(s.TableCell,{className:"text-right",children:i("CurrenciesActions")})]})}),e.jsx(ee,{currency:j,isCheck:m,setIsCheck:a})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:g,resultsPerPage:l,onChange:u,label:"Table navigation"})})]}),!x&&t.length===0&&!d&&e.jsx(Y,{title:"Sorry, There are no currency right now."})]})};export{De as default};
