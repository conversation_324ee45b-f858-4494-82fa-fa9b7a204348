import{j as e,f as s,s as U,r as u,S as H,k as O}from"./index-DD5OQCzb.js";import{S as q,u as k,h as z,g as G,e as J,f as K}from"./Layout-f_j_aP34.js";import{T,D as Q,u as B,M as F}from"./DrawerButton-C1kY46U5.js";import{C as P,S as W,B as X}from"./BulkActionDrawer-BEXxVAnh.js";import{D as E,E as Y}from"./EditDeleteButton-B2PjzFtp.js";import{E as C}from"./index.esm-BPZGYcl8.js";import{I as Z}from"./InputArea-Cu6xCoGw.js";import{L as S}from"./LabelArea-Zz4acQmF.js";import{S as $}from"./SwitchToggle-CiShsGtJ.js";import{u as ee}from"./useAttributeSubmit-BHLRuQLh.js";import{L as se}from"./Loading-D8j96Z5Y.js";import{N as le}from"./NotFound-DG_8Itz7.js";import{P as ae}from"./PageTitle-DUWCiaui.js";import{u as v}from"./useAsync-CdFiuEZy.js";import{u as re}from"./useFilter-BHZ5O6jw.js";import{A as D}from"./ProductServices-CGXRs0W4.js";import{A as te}from"./AnimatedContent-DbKaf3qr.js";import"./iconBase-DTk8F31e.js";import"./SelectLanguageTwo-CQlbeojL.js";import"./spinner-CkndCogW.js";import"./index.prod-CisttSXz.js";import"./CouponServices-BvJiM6D0.js";import"./CurrencyServices-CicNeQxs.js";import"./toast-C_V_NPJL.js";import"./ParentCategory-BL1vwhP5.js";import"./useDisableForDemo-aTnQzb5-.js";import"./AdminServices-Crgje1Fu.js";import"./Tooltip-DrdTd94n.js";import"./useTranslationValue-d_-eYXcs.js";import"./index-0EvDzr9j.js";const I=({id:t})=>{const{handleSubmit:j,onSubmits:r,register:i,errors:d,published:x,isSubmitting:o,setPublished:a,handleSelectLanguage:n}=ee(t);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:t?e.jsx(T,{register:i,handleSelectLanguage:n,title:"Add/Update Attribute Valu",description:"Add your attribute values and necessary information from here"}):e.jsx(T,{register:i,handleSelectLanguage:n,title:"Add/Update Attribute Values",description:"Add your attribute values and necessary information from here"})}),e.jsx(q,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:j(r),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 items-center",children:[e.jsx(S,{label:"Display Name"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(Z,{required:!0,register:i,label:"Display Name",name:"name",type:"text",placeholder:"Color or Size or Dimension or Material or Fabric"}),e.jsx(C,{errorName:d.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 items-center",children:[e.jsx(S,{label:"Published"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx($,{handleProcess:a,processOption:x}),e.jsx(C,{errorName:d.published})]})]})]}),e.jsx(Q,{id:t,title:"Attribute",isSubmitting:o})]})})]})},ie=({att:t,loading:j,isCheck:r,setIsCheck:i,childAttributes:d})=>{const{title:x,serviceId:o,handleModalOpen:a,handleUpdate:n}=B(),{showingTranslateValue:h}=k(),f=l=>{const{id:c,checked:m}=l.target;i([...r,c]),m||i(r.filter(N=>N!==c))};return e.jsxs(e.Fragment,{children:[r.length<1&&e.jsx(E,{id:o,title:x}),r.length<2&&e.jsx(F,{children:e.jsx(I,{id:o})}),e.jsx(s.TableBody,{children:d==null?void 0:d.map((l,c)=>{var m;return e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx(P,{type:"checkbox",name:"child-attribute",id:l._id,handleClick:f,isChecked:r==null?void 0:r.includes(l._id)})}),e.jsx(s.TableCell,{className:"font-semibold uppercase text-xs",children:(m=l==null?void 0:l._id)==null?void 0:m.substring(20,24)}),e.jsx(s.TableCell,{className:"font-medium text-sm",children:h(l==null?void 0:l.name)}),e.jsx(s.TableCell,{className:"font-medium text-sm",children:t==null?void 0:t.option}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(W,{id:l._id,status:l.status})}),e.jsx(s.TableCell,{children:e.jsx(Y,{id:l._id,isCheck:r,setIsCheck:i,handleUpdate:n,handleModalOpen:a,title:h(l.name)})})]},c+1)})})]})},_e=()=>{let{id:t}=U();const{handleDeleteMany:j,allId:r,serviceId:i,handleUpdateMany:d}=B(),{toggleDrawer:x,lang:o}=u.useContext(H),{data:a,loading:n,error:h}=v(()=>D.getAttributeById(t)),{showingTranslateValue:f}=k(),{data:l}=v(()=>D.getAllAttributes({type:"attribute",option:"Dropdown",option1:"Radio"})),{totalResults:c,resultsPerPage:m,dataTable:N,serviceData:A,handleChangePage:V}=re(a==null?void 0:a.variants),[w,M]=u.useState(!1),[p,g]=u.useState([]),[_,L]=u.useState([]),R=()=>{var b;M(!w),g((b=a==null?void 0:a.variants)==null?void 0:b.map(y=>y._id)),w&&g([])};return u.useEffect(()=>{const b=l==null?void 0:l.filter(y=>y._id!==t);L(b)},[l,t]),e.jsxs(e.Fragment,{children:[e.jsx(ae,{children:"Attributes Values"}),e.jsx(E,{ids:r,setIsCheck:g,title:"Selected Attribute Value(s)"}),e.jsx(X,{attributes:_,ids:r,title:"Attribute Value(s)",childId:t}),e.jsx(F,{children:e.jsx(I,{id:i})}),e.jsxs(te,{children:[e.jsx("div",{className:"flex items-center pb-4",children:e.jsxs("ol",{className:"flex items-center w-full overflow-hidden font-serif",children:[e.jsx("li",{className:"text-sm pr-1 transition duration-200 ease-in cursor-pointer hover:text-emerald-500 font-semibold",children:e.jsx(O,{className:"text-blue-700",to:"/attributes",children:"Attributes"})}),e.jsxs("span",{className:"flex items-center font-serif dark:text-gray-400",children:[e.jsxs("li",{className:"text-sm mt-[1px]",children:[" ",e.jsx(z,{})," "]}),e.jsx("li",{className:"text-sm pl-1 font-semibold dark:text-gray-400",children:!n&&f(a==null?void 0:a.title)})]})]})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsxs(s.CardBody,{className:"py-3 grid gap-4 justify-end lg:gap-4 xl:gap-4 md:flex xl:flex",children:[e.jsx("div",{className:"flex justify-end items-end",children:e.jsxs(s.Button,{onClick:x,className:"rounded-md h-12",children:[e.jsx("span",{className:"mr-3",children:e.jsx(G,{})}),"Add Value"]})}),e.jsx("div",{className:"w-full md:w-24 lg:w-24 xl:w-24",children:e.jsxs(s.Button,{disabled:p.length<1,onClick:()=>d(p),className:"w-full rounded-md h-12",children:[e.jsx(J,{}),"Bulk Action"]})}),e.jsxs(s.Button,{disabled:p.length<1,onClick:()=>j(p),className:"rounded-md h-12 bg-red-500",children:[e.jsx("span",{className:"mr-3",children:e.jsx(K,{})}),"Delete"]})]})})]}),n?e.jsx(se,{loading:n}):h?e.jsx("span",{className:"text-center mx-auto text-red-500",children:h}):(A==null?void 0:A.length)!==0?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(P,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:R,isChecked:w})}),e.jsx(s.TableCell,{children:"Id"}),e.jsx(s.TableCell,{children:"Name"}),e.jsx(s.TableCell,{children:"Type"}),e.jsx(s.TableCell,{className:"text-center",children:"Status"}),e.jsx(s.TableCell,{className:"text-right",children:"Actions"})]})}),e.jsx(ie,{att:a,lang:o,loading:n,isCheck:p,setIsCheck:g,childAttributes:N})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:c,resultsPerPage:m,onChange:V,label:"Table navigation"})})]}):e.jsx(le,{title:"Sorry, There are no attributes right now."})]})};export{_e as default};
