import{j as t,r,f as n,k as E}from"./index-DD5OQCzb.js";import{u as F,N as o,t as P,f as b,S as $,A as O}from"./Layout-f_j_aP34.js";import{P as z}from"./PageTitle-DUWCiaui.js";import{a as x,n as u}from"./toast-C_V_NPJL.js";import"./iconBase-DTk8F31e.js";const w=({id:l,name:d,type:f,handleClick:g,isChecked:N})=>t.jsx("input",{id:l,name:d,type:f,onChange:g,checked:N}),J=()=>{const[l,d]=r.useState([]),[f,g]=r.useState(0),[N,m]=r.useState(0),[k,p]=r.useState(2),[c,h]=r.useState([]),[j,C]=r.useState(!1),{showDateTimeFormat:D}=F(),S=async a=>{var i,s;try{await o.updateStatusNotification(a,{status:"read"});const e=await o.getAllNotification();d(e==null?void 0:e.notifications),m(e==null?void 0:e.totalUnreadDoc),window.location.reload(!1)}catch(e){x(((s=(i=e==null?void 0:e.response)==null?void 0:i.data)==null?void 0:s.message)||(e==null?void 0:e.message))}},T=async a=>{var i,s;try{await o.deleteNotification(a);const e=await o.getAllNotification();d(e==null?void 0:e.notifications),m(e==null?void 0:e.totalUnreadDoc),g(e==null?void 0:e.totalDoc)}catch(e){x(((s=(i=e==null?void 0:e.response)==null?void 0:i.data)==null?void 0:s.message)||(e==null?void 0:e.message))}},U=async a=>{var i,s;try{const e=await o.getAllNotification(a);d(y=>[...y,...e==null?void 0:e.notifications]),m(e==null?void 0:e.totalUnreadDoc),p(y=>y+1)}catch(e){x(((s=(i=e==null?void 0:e.response)==null?void 0:i.data)==null?void 0:s.message)||(e==null?void 0:e.message))}},B=async()=>{var a,i;try{const s=await o.updateManyStatusNotification({ids:c,status:"read"});h([]),u(s.message),p(1);const e=await o.getAllNotification();d(e==null?void 0:e.notifications),m(e==null?void 0:e.totalUnreadDoc)}catch(s){x(((i=(a=s==null?void 0:s.response)==null?void 0:a.data)==null?void 0:i.message)||(s==null?void 0:s.message))}},I=async()=>{var a,i;try{const s=await o.deleteManyNotification({ids:c});u(s.message),h([]),p(1);const e=await o.getAllNotification();d(e==null?void 0:e.notifications),m(e==null?void 0:e.totalUnreadDoc)}catch(s){x(((i=(a=s==null?void 0:s.response)==null?void 0:a.data)==null?void 0:i.message)||(s==null?void 0:s.message))}},M=()=>{C(!j),h(l==null?void 0:l.map(a=>a._id)),j&&h([])},_=a=>{const{id:i,checked:s}=a.target;h([...c,i]),s||h(c.filter(e=>e!==i))};return r.useEffect(()=>{(async()=>{var a,i;try{const s=await o.getAllNotification();d(s==null?void 0:s.notifications),m(s==null?void 0:s.totalUnreadDoc),g(s==null?void 0:s.totalDoc),p(1)}catch(s){x(((i=(a=s==null?void 0:s.response)==null?void 0:a.data)==null?void 0:i.message)||(s==null?void 0:s.message))}})()},[]),t.jsxs(t.Fragment,{children:[t.jsx(z,{children:"Notifications"}),t.jsx(n.Card,{className:"shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:t.jsxs(n.CardBody,{className:"flex justify-between",children:[t.jsx("div",{className:"",children:t.jsxs(n.Button,{disabled:(c==null?void 0:c.length)<1,onClick:B,className:"w-full rounded-md h-10 flex items-center justify-center bg-blue-500 text-white px-1 hover:bg-blue-700",children:[t.jsx("span",{className:"mr-2",children:t.jsx(P,{})}),"Mark is read"]})}),t.jsx("div",{className:"",children:t.jsxs(n.Button,{disabled:(c==null?void 0:c.length)<1,onClick:I,className:"w-full rounded-md h-10 bg-red-500 btn-red",children:[t.jsx("span",{className:"mr-3",children:t.jsx(b,{})}),"Delete"]})})]})}),t.jsx(n.Card,{className:"shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:t.jsxs(n.CardBody,{style:{padding:0},children:[t.jsx("div",{className:"p-4 dark:text-gray-300",children:t.jsxs("p",{className:"text-sm font-semibold text-teal-700",children:["Unread Notification (",N,")"]})}),t.jsxs("div",{className:"border rounded-md",children:[t.jsxs("div",{className:"bg-gray-200 border-gray-400 p-2 dark:bg-gray-700 dark:text-gray-400 flex justify-between",children:[t.jsxs("div",{className:"flex",children:[t.jsx(w,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:M,isChecked:j}),t.jsx("p",{className:"text-xs font-semibold text-gray-500 my-auto dark:text-gray-300 ml-6 uppercase",children:"Notification"})]}),t.jsx("div",{className:"text-right",children:t.jsx("p",{className:"text-xs font-semibold text-gray-500 my-auto dark:text-gray-300 mr-2 uppercase",children:"Action"})})]}),t.jsx("div",{className:"w-full lg:h-lg md:h-sm h-md relative",children:t.jsx($,{className:"scrollbar-hide",children:t.jsxs(n.TableContainer,{className:"border-none p-2",children:[t.jsx(n.Table,{children:t.jsx(n.TableBody,{className:"w-full h-440",children:l.map((a,i)=>t.jsxs(n.TableRow,{className:"border-none",children:[t.jsx(n.TableCell,{style:{padding:0},children:t.jsx(w,{type:"checkbox",name:a==null?void 0:a._id,id:a._id,handleClick:_,isChecked:c==null?void 0:c.includes(a._id)})}),t.jsx(n.TableCell,{className:"md:w-full w-1/5",style:{paddingRight:0},children:t.jsxs(E,{to:a.productId?`/product/${a.productId}`:`/order/${a.orderId}`,className:"flex items-center",onClick:()=>S(a._id),children:[t.jsx(n.Avatar,{className:"mr-2 md:block hidden bg-gray-50 border border-gray-200",src:a.image,alt:"image"}),t.jsxs("div",{className:"notification-content",children:[t.jsx("div",{className:"md:inline-block hidden",children:t.jsx("h6",{className:"font-medium text-gray-500",children:a==null?void 0:a.message})}),t.jsx("div",{className:"md:hidden",children:t.jsx("h6",{className:"font-medium text-gray-500",children:(a==null?void 0:a.message.substring(0,33))+"..."})}),t.jsxs("p",{className:"flex items-center text-xs text-gray-400",children:[a.productId?t.jsx(n.Badge,{type:"danger",children:"Stock Out"}):t.jsx(n.Badge,{type:"success",children:"New Order"}),t.jsx("span",{className:"ml-2",children:D(a==null?void 0:a.createdAt)})]})]}),a.status==="unread"&&t.jsx("span",{className:"px-2 md:flex hidden focus:outline-none text-emerald-600",children:t.jsx("img",{src:O,width:12,height:12,alt:"ellipse",className:"w-3 h-3 text-emerald-600"})})]})}),t.jsx(n.TableCell,{className:"text-right",style:{padding:`${window.innerWidth<420?"0":"0.5rem"}`},children:t.jsxs("div",{className:"group inline-block relative",children:[t.jsx("button",{onClick:()=>T(a._id),type:"button",className:"px-2 group-hover:text-blue-500 text-red-500 focus:outline-none",children:t.jsx(b,{})}),t.jsx("div",{className:"absolute hidden group-hover:inline-block bg-gray-50 dark:text-white mr-8 mb-1 right-0 z-50 px-3 py-2 text-sm font-medium text-red-600 rounded-lg shadow-sm tooltip dark:bg-gray-700",children:"Delete"})]})})]},i+1))})}),t.jsx("div",{children:f>5&&l.length!==f?t.jsx("div",{className:"text-center py-2",children:t.jsx("button",{onClick:()=>U(k+1),type:"button",className:"focus:outline-none text-blue-700 hover:underline transition ease-out duration-200 dark:text-gray-400",children:"See more notifications"})}):null})]})})})]})]})})]})};export{J as default};
