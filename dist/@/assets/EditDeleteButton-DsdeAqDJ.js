import{p as L,r as w,S as P,u as T,k as D,j as t,h as u,L as v,l as $}from"./index-DpMxJ5Hx.js";import{f as B,i as I,e as C}from"./Layout-B-UGxzbM.js";import{s as O}from"./spinner-CkndCogW.js";import{A as R}from"./AdminServices-CIs7colP.js";import{P as M,C as y,b as H,A as p}from"./ProductServices-CnM1m97m.js";import{C as S}from"./CouponServices-vUOVn0Wx.js";import{u as K}from"./DrawerButton-CKK8nF3h.js";import{C as N}from"./CurrencyServices-Dk3mpScu.js";import{n as l,a as b}from"./toast-Be5Wd3gm.js";import{u as V}from"./useDisableForDemo-DczgqPm6.js";import{T as x}from"./Tooltip-BQ_BZ_s8.js";const Z=({id:o,ids:n,setIsCheck:m,category:g,title:f,useParamId:h})=>{const{isModalOpen:j,closeModal:s,setIsUpdate:a}=w.useContext(P),{setServiceId:i}=K(),c=T(),[E,r]=w.useState(!1),{handleDisableForDemo:F}=V(),A=async()=>{if(!F())try{if(r(!0),c.pathname==="/products")if(n){const e=await M.deleteManyProducts({ids:n});a(!0),l(e.message),m([]),i(),s(),r(!1)}else{const e=await M.deleteProduct(o);a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname==="/coupons")if(n){const e=await S.deleteManyCoupons({ids:n});a(!0),l(e.message),m([]),i(),s(),r(!1)}else{const e=await S.deleteCoupon(o);a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname==="/categories"||g)if(n){const e=await y.deleteManyCategory({ids:n});a(!0),l(e.message),m([]),i(),s(),r(!1)}else{if(o===void 0||!o)return b("Please select a category first!"),r(!1),s();const e=await y.deleteCategory(o);a(!0),l(e.message),s(),i(),r(!1)}else if(c.pathname===`/categories/${h}`||g){if(o===void 0||!o)return b("Please select a category first!"),r(!1),s();const e=await y.deleteCategory(o);a(!0),l(e.message),s(),i(),r(!1)}if(c.pathname==="/customers"){const e=await H.deleteCustomer(o);a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname==="/attributes")if(n){const e=await p.deleteManyAttribute({ids:n});a(!0),l(e.message),m([]),i(),s(),r(!1)}else{const e=await p.deleteAttribute(o);a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname===`/attributes/${c.pathname.split("/")[2]}`)if(n){const e=await p.deleteManyChildAttribute({id:c.pathname.split("/")[2],ids:n});a(!0),l(e.message),i(),m([]),s(),r(!1)}else{const e=await p.deleteChildAttribute({id:o,ids:c.pathname.split("/")[2]});a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname==="/our-staff"){const e=await R.deleteStaff(o);a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname==="/languages")if(n){const e=await v.deleteManyLanguage({ids:n});a(!0),l(e.message),m([]),s(),r(!1)}else{const e=await v.deleteLanguage(o);a(!0),l(e.message),i(),s(),r(!1)}if(c.pathname==="/currencies")if(n){const e=await N.deleteManyCurrency({ids:n});a(!0),l(e.message),m([]),s(),r(!1)}else{const e=await N.deleteCurrency(o);a(!0),l(e.message),i(),s(),r(!1)}}catch(e){b(e?e?.response?.data?.message:e?.message),i(),m([]),s(),r(!1)}},{t:d}=D();return t.jsx(t.Fragment,{children:t.jsxs(u.Modal,{isOpen:j,onClose:s,children:[t.jsxs(u.ModalBody,{className:"text-center custom-modal px-8 pt-6 pb-4",children:[t.jsx("span",{className:"flex justify-center text-3xl mb-6 text-red-500",children:t.jsx(B,{})}),t.jsxs("h2",{className:"text-xl font-medium mb-2",children:[d("DeleteModalH2")," ",t.jsx("span",{className:"text-red-500",children:f}),"?"]}),t.jsx("p",{children:d("DeleteModalPtag")})]}),t.jsxs(u.ModalFooter,{className:"justify-center",children:[t.jsx(u.Button,{className:"w-full sm:w-auto hover:bg-white hover:border-gray-50",layout:"outline",onClick:s,children:d("modalKeepBtn")}),t.jsx("div",{className:"flex justify-end",children:E?t.jsxs(u.Button,{disabled:!0,type:"button",className:"w-full h-12 sm:w-auto",children:[t.jsx("img",{src:O,alt:"Loading",width:20,height:10})," ",t.jsx("span",{className:"font-serif ml-2 font-light",children:d("Processing")})]}):t.jsx(u.Button,{onClick:A,className:"w-full h-12 sm:w-auto",children:d("modalDeletBtn")})})]})]})})},ee=L.memo(Z),te=({id:o,title:n,handleUpdate:m,handleModalOpen:g,isCheck:f,product:h,parent:j,children:s})=>{const{t:a}=D();return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"flex justify-end text-right",children:[s?.length>0?t.jsxs(t.Fragment,{children:[t.jsx($,{to:`/categories/${j?._id}`,className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:t.jsx(x,{id:"view",Icon:I,title:a("View"),bgColor:"#10B981"})}),t.jsx("button",{disabled:f?.length>0,onClick:()=>m(o),className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:t.jsx(x,{id:"edit",Icon:C,title:a("Edit"),bgColor:"#10B981"})})]}):t.jsx("button",{disabled:f?.length>0,onClick:()=>m(o),className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:t.jsx(x,{id:"edit",Icon:C,title:a("Edit"),bgColor:"#10B981"})}),t.jsx("button",{disabled:f?.length>0,onClick:()=>g(o,n,h),className:"p-2 cursor-pointer text-gray-400 hover:text-red-600 focus:outline-none",children:t.jsx(x,{id:"delete",Icon:B,title:a("Delete"),bgColor:"#EF4444"})})]})})};export{ee as D,te as E};
