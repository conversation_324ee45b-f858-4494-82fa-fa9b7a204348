import{s as R,r as o,S as U,i as H,j as e,k as y,f as s}from"./index-DD5OQCzb.js";import{u as V,h as $,g as q,e as z,f as G}from"./Layout-f_j_aP34.js";import{a as J}from"./CategoryTable-CoigXHiA.js";import{B as K,C as O}from"./BulkActionDrawer-BEXxVAnh.js";import{D as Q}from"./EditDeleteButton-B2PjzFtp.js";import{L as W}from"./Loading-D8j96Z5Y.js";import{N as X}from"./NotFound-DG_8Itz7.js";import{P as Y}from"./PageTitle-DUWCiaui.js";import{u as Z}from"./useAsync-CdFiuEZy.js";import{u as ee}from"./useFilter-BHZ5O6jw.js";import{u as se}from"./DrawerButton-C1kY46U5.js";import{C as te}from"./ProductServices-CGXRs0W4.js";import{A as re}from"./AnimatedContent-DbKaf3qr.js";import"./iconBase-DTk8F31e.js";import"./ParentCategory-BL1vwhP5.js";import"./toast-C_V_NPJL.js";import"./index.esm-BPZGYcl8.js";import"./InputArea-Cu6xCoGw.js";import"./LabelArea-Zz4acQmF.js";import"./SwitchToggle-CiShsGtJ.js";import"./index.prod-CisttSXz.js";import"./Uploader-Bz_6zC0v.js";import"./_commonjs-dynamic-modules-LM44EJN2.js";import"./index-0EvDzr9j.js";import"./useTranslationValue-d_-eYXcs.js";import"./CouponServices-BvJiM6D0.js";import"./CurrencyServices-CicNeQxs.js";import"./useDisableForDemo-aTnQzb5-.js";import"./spinner-CkndCogW.js";import"./AdminServices-Crgje1Fu.js";import"./Tooltip-DrdTd94n.js";import"./SelectLanguageTwo-CQlbeojL.js";const Le=()=>{const{id:c}=R(),[d,A]=o.useState([]),[g,v]=o.useState([]),[j,P]=o.useState(!1),[m,x]=o.useState([]),{toggleDrawer:S,lang:b}=o.useContext(U),{handleDeleteMany:F,allId:T,handleUpdateMany:B}=se(),{data:h,loading:p,error:N}=Z(te.getAllCategory),{showingTranslateValue:D}=V(),{t:r}=H();o.useEffect(()=>{var k,w;const l=(t,n,i=[])=>{for(let a of n){if(a._id===t)return i.concat(a);const f=l(t,a==null?void 0:a.children,i==null?void 0:i.concat(a));if(f)return f}},u=(t,n)=>{var i;return t._id===n?t:(i=t==null?void 0:t.children)==null?void 0:i.reduce((a,f)=>a??u(f,n),void 0)};if(!p){const t=u(h[0],c),n=l(c,(k=h[0])==null?void 0:k.children);((w=t==null?void 0:t.children)==null?void 0:w.length)>0&&(A(t==null?void 0:t.children),v(n))}},[c,p,h,d]);const{totalResults:I,resultsPerPage:E,dataTable:_,serviceData:C,handleChangePage:L}=ee(d),M=()=>{P(!j),x(d==null?void 0:d.map(l=>l._id)),j&&x([])};return e.jsxs(e.Fragment,{children:[e.jsx(Y,{children:r("CategoryPageTitle")}),e.jsx(Q,{ids:T,setIsCheck:x,category:!0}),e.jsx(K,{ids:T,title:"Child Categories",lang:b,data:h,childId:c}),e.jsxs(re,{children:[e.jsx("div",{className:"flex items-center pb-4",children:e.jsxs("ol",{className:"flex items-center w-full overflow-hidden font-serif",children:[e.jsx("li",{className:"text-sm pr-1 transition duration-200 ease-in cursor-pointer hover:text-emerald-500 font-semibold",children:e.jsx(y,{to:"/categories",children:r("Categories")})}),g==null?void 0:g.map((l,u)=>e.jsxs("span",{className:"flex items-center font-serif",children:[e.jsxs("li",{className:"text-sm mt-[1px]",children:[" ",e.jsx($,{})," "]}),e.jsx("li",{className:"text-sm pl-1 transition duration-200 ease-in cursor-pointer text-blue-700 hover:text-emerald-500 font-semibold ",children:e.jsx(y,{to:`/categories/${l._id}`,children:D(l==null?void 0:l.name)})})]},u+1))]})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("div",{className:"flex justify-end items-end",children:[e.jsxs(s.Button,{onClick:S,className:"rounded-md h-12",children:[e.jsx("span",{className:"mr-3",children:e.jsx(q,{})}),r("AddCategory")]}),e.jsx("div",{className:"ml-3 w-full md:w-24 lg:w-24 xl:w-24",children:e.jsxs(s.Button,{disabled:m.length<1,onClick:()=>B(m),className:"w-full rounded-md h-12",children:[e.jsx(z,{}),r("BulkAction")]})}),e.jsxs(s.Button,{disabled:m.length<1,onClick:()=>F(m),className:"ml-3 rounded-md h-12 bg-red-500",children:[e.jsx("span",{className:"mr-3",children:e.jsx(G,{})}),r("Delete")]})]})})})]}),p?e.jsx(W,{loading:p}):N?e.jsx("span",{className:"text-center mx-auto text-red-500",children:N}):(C==null?void 0:C.length)!==0?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(O,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:M,isChecked:j})}),e.jsx(s.TableCell,{children:r("catIdTbl")}),e.jsx(s.TableCell,{children:r("catIconTbl")}),e.jsx(s.TableCell,{children:r("Name")}),e.jsx(s.TableCell,{children:r("Description")}),e.jsx(s.TableCell,{className:"text-center",children:r("catPublishedTbl")}),e.jsx(s.TableCell,{className:"text-right",children:r("catActionsTbl")})]})}),e.jsx(J,{categories:_,data:h,lang:b,isCheck:m,setIsCheck:x,useParamId:c})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:I,resultsPerPage:E,onChange:L,label:"Table navigation"})})]}):e.jsx(X,{title:"Sorry, There are no categories right now."})]})};export{Le as default};
