const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["@/assets/Dashboard-D9esJu_v.js","@/assets/index-DD5OQCzb.js","@/assets/useFilter-BHZ5O6jw.js","@/assets/ProductServices-CGXRs0W4.js","@/assets/index-0EvDzr9j.js","@/assets/useDisableForDemo-aTnQzb5-.js","@/assets/toast-C_V_NPJL.js","@/assets/CouponServices-BvJiM6D0.js","@/assets/CurrencyServices-CicNeQxs.js","@/assets/index.esm-fBXCQugp.js","@/assets/iconBase-DTk8F31e.js","@/assets/useAsync-CdFiuEZy.js","@/assets/TableLoading-D3D_P-TQ.js","@/assets/OrderTable-kC8zYnrq.js","@/assets/Status-dJqttBU1.js","@/assets/Tooltip-DrdTd94n.js","@/assets/index-Cu0r7Io9.js","@/assets/OrderServices-DB9CIA_J.js","@/assets/SelectStatus-FXtu805S.js","@/assets/NotFound-DG_8Itz7.js","@/assets/PageTitle-DUWCiaui.js","@/assets/AnimatedContent-DbKaf3qr.js","@/assets/Attributes-RuJSFj-r.js","@/assets/DrawerButton-C1kY46U5.js","@/assets/SelectLanguageTwo-CQlbeojL.js","@/assets/spinner-CkndCogW.js","@/assets/EditDeleteButton-B2PjzFtp.js","@/assets/AdminServices-Crgje1Fu.js","@/assets/BulkActionDrawer-BEXxVAnh.js","@/assets/index.prod-CisttSXz.js","@/assets/ParentCategory-BL1vwhP5.js","@/assets/index.esm-BPZGYcl8.js","@/assets/LabelArea-Zz4acQmF.js","@/assets/SwitchToggle-CiShsGtJ.js","@/assets/InputArea-Cu6xCoGw.js","@/assets/useAttributeSubmit-BHLRuQLh.js","@/assets/useTranslationValue-d_-eYXcs.js","@/assets/UploadMany-Bdp4IRJJ.js","@/assets/exportFromJSON-fDIoOtpr.js","@/assets/ChildAttributes-DWkvJFdI.js","@/assets/Loading-D8j96Z5Y.js","@/assets/Products-COSfDiv3.js","@/assets/ProductDrawer-CF8xFIB0.js","@/assets/index-CgLdKLua.js","@/assets/InputValue-CWXhc3PW.js","@/assets/Uploader-Bz_6zC0v.js","@/assets/_commonjs-dynamic-modules-LM44EJN2.js","@/assets/ProductDetails-CnmQ5SFK.js","@/assets/Category-CV45qYOK.js","@/assets/CategoryTable-CoigXHiA.js","@/assets/ChildCategory-BfvSylE4.js","@/assets/Staff-D3v-HZkc.js","@/assets/useStaffSubmit-D1nPnR2p.js","@/assets/SelectRole-C6-enEQn.js","@/assets/Customers-mgZi8_es.js","@/assets/CustomerOrder-jDWVQlwW.js","@/assets/Orders-gqilpHO6.js","@/assets/OrderInvoice-DkyEodCO.js","@/assets/index-ncuftIDZ.js","@/assets/Coupons-B56ML6Lh.js","@/assets/404-DrvXymuP.js","@/assets/ComingSoon-Rh0tuLLi.js","@/assets/EditProfile-BrjieRfC.js","@/assets/Languages-Bebrg4Dd.js","@/assets/Currencies-CSRYMsEt.js","@/assets/Setting-7vT99jj8.js","@/assets/InputAreaTwo-CcvTYRvk.js","@/assets/SettingContainer-1j4x83wF.js","@/assets/StoreHome-BIIraSI9.js","@/assets/StoreSetting-qksk1b_f.js","@/assets/Notifications-CJRyvcfq.js"])))=>i.map(i=>d[i]);
import{r as g,R as ge,A as Ve,u as Ne,j as n,c as ze,a as Ie,p as ut,g as Re,S as be,b as We,d as dt,L as ht,e as ae,f as X,h as De,i as Ee,k as ye,l as pe,N as Ze,_ as M,T as ft,m as gt,n as pt}from"./index-DD5OQCzb.js";import{G as w}from"./iconBase-DTk8F31e.js";function Ue(e=ge){return function(){return g.useContext(e)}}const mt=Ue();function Ye(e=ge){const s=e===ge?mt:Ue(e);return function(){const{store:d}=s();return d}}const vt=Ye();function yt(e=ge){const s=e===ge?vt:Ye(e);return function(){return s().dispatch}}const xt=yt(),qe=()=>{var v;const{state:e}=g.useContext(Ve),{adminInfo:s}=e,u=Ne(),d=(v=u==null?void 0:u.pathname)==null?void 0:v.split("?")[0].split("/")[1],[h,k]=g.useState(),[f,H]=g.useState([]),$=async(V,L)=>{const C=await crypto.subtle.digest("SHA-256",new TextEncoder().encode("856305f1a5b7ba87b8448e69b3bb7a4631c23f0afa2ca5331fa1373f7e372345")),F=new Uint8Array(V.match(/.{1,2}/g).map(p=>parseInt(p,16))),K=new Uint8Array(L.match(/.{1,2}/g).map(p=>parseInt(p,16)));try{const p=await crypto.subtle.decrypt({name:"AES-CBC",iv:K},await crypto.subtle.importKey("raw",C,{name:"AES-CBC"},!1,["decrypt"]),F);return new TextDecoder().decode(p)}catch(p){return console.error("Decryption failed:",p),null}};return g.useEffect(()=>{(async()=>{if(s!=null&&s.data&&(s!=null&&s.iv))try{const L=await $(s.data,s.iv),B=JSON.parse(L),C=B.pop();k(C),H(B)}catch(L){console.error("Failed to decrypt and parse data:",L)}})()},[s]),{role:h,path:d,accessList:f}},kt="/@/assets/404-DUm08_0T.svg",wt=()=>{const e=Ne(),s=e==null?void 0:e.pathname;return g.useEffect(()=>{s==="/"&&window.location.replace("/dashboard")},[s]),n.jsx(n.Fragment,{children:n.jsx("div",{className:"px-6 py-16 lg:py-20 h-screen flex flex-wrap content-center",children:n.jsxs("div",{className:"block justify-items-stretch mx-auto items-center text-center",children:[n.jsx("img",{width:650,height:450,src:kt,alt:"404"}),n.jsx("h2",{className:"font-bold font-serif dark:text-gray-200 font-2xl lg:text-4xl leading-7 mb-4",children:"Page is not found!"}),n.jsx("p",{className:"text-red-400 text-lg",children:"Sorry you don't have access to this page!"})]})})})},bt=({children:e})=>{const{path:s,accessList:u}=qe();return u!=null&&u.includes(s)?n.jsx("main",{className:"h-full overflow-y-auto",children:n.jsx("div",{className:"sm:container grid lg:px-6 sm:px-4 px-2 mx-auto",children:e})}):n.jsx(wt,{})};var Oe={},Ge={},He={exports:{}},fe={exports:{}};(function(){var e,s,u,d,h,k;typeof performance<"u"&&performance!==null&&performance.now?fe.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(fe.exports=function(){return(e()-h)/1e6},s=process.hrtime,e=function(){var f;return f=s(),f[0]*1e9+f[1]},d=e(),k=process.uptime()*1e9,h=d-k):Date.now?(fe.exports=function(){return Date.now()-u},u=Date.now()):(fe.exports=function(){return new Date().getTime()-u},u=new Date().getTime())}).call(ze);var Ht=fe.exports,St=Ht,ee=typeof window>"u"?ze:window,xe=["moz","webkit"],se="AnimationFrame",le=ee["request"+se],me=ee["cancel"+se]||ee["cancelRequest"+se];for(var de=0;!le&&de<xe.length;de++)le=ee[xe[de]+"Request"+se],me=ee[xe[de]+"Cancel"+se]||ee[xe[de]+"CancelRequest"+se];if(!le||!me){var Me=0,Fe=0,oe=[],Lt=1e3/60;le=function(e){if(oe.length===0){var s=St(),u=Math.max(0,Lt-(s-Me));Me=u+s,setTimeout(function(){var d=oe.slice(0);oe.length=0;for(var h=0;h<d.length;h++)if(!d[h].cancelled)try{d[h].callback(Me)}catch(k){setTimeout(function(){throw k},0)}},Math.round(u))}return oe.push({handle:++Fe,callback:e,cancelled:!1}),Fe},me=function(e){for(var s=0;s<oe.length;s++)oe[s].handle===e&&(oe[s].cancelled=!0)}}He.exports=function(e){return le.call(ee,e)};He.exports.cancel=function(){me.apply(ee,arguments)};He.exports.polyfill=function(e){e||(e=ee),e.requestAnimationFrame=le,e.cancelAnimationFrame=me};var Tt=He.exports,Se={exports:{}},Ce=null,Ae=["Webkit","Moz","O","ms"],_t=function(s){Ce||(Ce=document.createElement("div"));var u=Ce.style;if(s in u)return s;for(var d=s.charAt(0).toUpperCase()+s.slice(1),h=Ae.length;h>=0;h--){var k=Ae[h]+d;if(k in u)return k}return!1},Mt=Vt,Ct=/\s/,jt=/(_|-|\.|:)/,Dt=/([a-z][A-Z]|[A-Z][a-z])/;function Vt(e){return Ct.test(e)?e.toLowerCase():jt.test(e)?(zt(e)||e).toLowerCase():Dt.test(e)?Ot(e).toLowerCase():e.toLowerCase()}var Nt=/[\W_]+(.|$)/g;function zt(e){return e.replace(Nt,function(s,u){return u?" "+u:""})}var Et=/(.)([A-Z]+)/g;function Ot(e){return e.replace(Et,function(s,u,d){return u+" "+d.toLowerCase().split("").join(" ")})}var $t=Mt,Wt=Ft;function Ft(e){return $t(e).replace(/[\W_]+(.|$)/g,function(s,u){return u?" "+u:""}).trim()}var At=Wt,Pt=Bt;function Bt(e){return At(e).replace(/\s(\w)/g,function(s,u){return u.toUpperCase()})}var It={animationIterationCount:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridColumn:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,stopOpacity:!0,strokeDashoffset:!0,strokeOpacity:!0,strokeWidth:!0},Rt=function(e,s){return typeof s=="number"&&!It[e]?s+"px":s},Zt=_t,Ut=Pt,ke={float:"cssFloat"},Yt=Rt;function we(e,s,u){var d=ke[s];if(typeof d>"u"&&(d=Gt(s)),d){if(u===void 0)return e.style[d];e.style[d]=Yt(d,u)}}function qt(e,s){for(var u in s)s.hasOwnProperty(u)&&we(e,u,s[u])}function Gt(e){var s=Ut(e),u=Zt(s);return ke[s]=ke[e]=ke[u]=u,u}function Xe(){arguments.length===2?typeof arguments[1]=="string"?arguments[0].style.cssText=arguments[1]:qt(arguments[0],arguments[1]):we(arguments[0],arguments[1],arguments[2])}Se.exports=Xe;Se.exports.set=Xe;Se.exports.get=function(e,s){return Array.isArray(s)?s.reduce(function(u,d){return u[d]=we(e,d||""),u},{}):we(e,s||"")};var Ke=Se.exports,Je={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=s;function s(u){return typeof u=="string"}})(Je);var Qe={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=k;var s=Ke,u=d(s);function d(f){return f&&f.__esModule?f:{default:f}}var h=!1;function k(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;if(f&&h!==!1)return h;if(typeof document<"u"){var H=document.createElement("div");(0,u.default)(H,{width:100,height:100,position:"absolute",top:-9999,overflow:"scroll",MsOverflowStyle:"scrollbar"}),document.body.appendChild(H),h=H.offsetWidth-H.clientWidth,document.body.removeChild(H)}else h=0;return h||0}})(Qe);var et={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=s;function s(){return!1}})(et);var tt={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=s;function s(u){var d=u.clientWidth,h=getComputedStyle(u),k=h.paddingLeft,f=h.paddingRight;return d-parseFloat(k)-parseFloat(f)}})(tt);var rt={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=s;function s(u){var d=u.clientHeight,h=getComputedStyle(u),k=h.paddingTop,f=h.paddingBottom;return d-parseFloat(k)-parseFloat(f)}})(rt);var Z={};Object.defineProperty(Z,"__esModule",{value:!0});Z.containerStyleDefault={position:"relative",overflow:"hidden",width:"100%",height:"100%"};Z.containerStyleAutoHeight={height:"auto"};Z.viewStyleDefault={position:"absolute",top:0,left:0,right:0,bottom:0,overflow:"scroll",WebkitOverflowScrolling:"touch"};Z.viewStyleAutoHeight={position:"relative",top:void 0,left:void 0,right:void 0,bottom:void 0};Z.viewStyleUniversalInitial={overflow:"hidden",marginRight:0,marginBottom:0};Z.trackHorizontalStyleDefault={position:"absolute",height:6};Z.trackVerticalStyleDefault={position:"absolute",width:6};Z.thumbHorizontalStyleDefault={position:"relative",display:"block",height:"100%"};Z.thumbVerticalStyleDefault={position:"relative",display:"block",width:"100%"};Z.disableSelectStyle={userSelect:"none"};Z.disableSelectStyleReset={userSelect:""};var ie={};Object.defineProperty(ie,"__esModule",{value:!0});var ne=Object.assign||function(e){for(var s=1;s<arguments.length;s++){var u=arguments[s];for(var d in u)Object.prototype.hasOwnProperty.call(u,d)&&(e[d]=u[d])}return e};ie.renderViewDefault=Jt;ie.renderTrackHorizontalDefault=Qt;ie.renderTrackVerticalDefault=er;ie.renderThumbHorizontalDefault=tr;ie.renderThumbVerticalDefault=rr;var Xt=Ie(),ve=Kt(Xt);function Kt(e){return e&&e.__esModule?e:{default:e}}function Le(e,s){var u={};for(var d in e)s.indexOf(d)>=0||Object.prototype.hasOwnProperty.call(e,d)&&(u[d]=e[d]);return u}function Jt(e){return ve.default.createElement("div",e)}function Qt(e){var s=e.style,u=Le(e,["style"]),d=ne({},s,{right:2,bottom:2,left:2,borderRadius:3});return ve.default.createElement("div",ne({style:d},u))}function er(e){var s=e.style,u=Le(e,["style"]),d=ne({},s,{right:2,bottom:2,top:2,borderRadius:3});return ve.default.createElement("div",ne({style:d},u))}function tr(e){var s=e.style,u=Le(e,["style"]),d=ne({},s,{cursor:"pointer",borderRadius:"inherit",backgroundColor:"rgba(0,0,0,.2)"});return ve.default.createElement("div",ne({style:d},u))}function rr(e){var s=e.style,u=Le(e,["style"]),d=ne({},s,{cursor:"pointer",borderRadius:"inherit",backgroundColor:"rgba(0,0,0,.2)"});return ve.default.createElement("div",ne({style:d},u))}(function(e){Object.defineProperty(e,"__esModule",{value:!0});var s=Object.assign||function(S){for(var y=1;y<arguments.length;y++){var l=arguments[y];for(var r in l)Object.prototype.hasOwnProperty.call(l,r)&&(S[r]=l[r])}return S},u=function(){function S(y,l){for(var r=0;r<l.length;r++){var a=l[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(y,a.key,a)}}return function(y,l,r){return l&&S(y.prototype,l),r&&S(y,r),y}}(),d=Tt,h=N(d),k=Ke,f=N(k),H=Ie(),$=ut,v=N($),V=Je,L=N(V),B=Qe,C=N(B),F=et,K=N(F),p=tt,U=N(p),ce=rt,J=N(ce),A=Z,P=ie;function N(S){return S&&S.__esModule?S:{default:S}}function te(S,y){var l={};for(var r in S)y.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(S,r)&&(l[r]=S[r]);return l}function re(S,y){if(!(S instanceof y))throw new TypeError("Cannot call a class as a function")}function Y(S,y){if(!S)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return y&&(typeof y=="object"||typeof y=="function")?y:S}function j(S,y){if(typeof y!="function"&&y!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof y);S.prototype=Object.create(y&&y.prototype,{constructor:{value:S,enumerable:!1,writable:!0,configurable:!0}}),y&&(Object.setPrototypeOf?Object.setPrototypeOf(S,y):S.__proto__=y)}var b=function(S){j(y,S);function y(l){var r;re(this,y);for(var a=arguments.length,o=Array(a>1?a-1:0),i=1;i<a;i++)o[i-1]=arguments[i];var t=Y(this,(r=y.__proto__||Object.getPrototypeOf(y)).call.apply(r,[this,l].concat(o)));return t.getScrollLeft=t.getScrollLeft.bind(t),t.getScrollTop=t.getScrollTop.bind(t),t.getScrollWidth=t.getScrollWidth.bind(t),t.getScrollHeight=t.getScrollHeight.bind(t),t.getClientWidth=t.getClientWidth.bind(t),t.getClientHeight=t.getClientHeight.bind(t),t.getValues=t.getValues.bind(t),t.getThumbHorizontalWidth=t.getThumbHorizontalWidth.bind(t),t.getThumbVerticalHeight=t.getThumbVerticalHeight.bind(t),t.getScrollLeftForOffset=t.getScrollLeftForOffset.bind(t),t.getScrollTopForOffset=t.getScrollTopForOffset.bind(t),t.scrollLeft=t.scrollLeft.bind(t),t.scrollTop=t.scrollTop.bind(t),t.scrollToLeft=t.scrollToLeft.bind(t),t.scrollToTop=t.scrollToTop.bind(t),t.scrollToRight=t.scrollToRight.bind(t),t.scrollToBottom=t.scrollToBottom.bind(t),t.handleTrackMouseEnter=t.handleTrackMouseEnter.bind(t),t.handleTrackMouseLeave=t.handleTrackMouseLeave.bind(t),t.handleHorizontalTrackMouseDown=t.handleHorizontalTrackMouseDown.bind(t),t.handleVerticalTrackMouseDown=t.handleVerticalTrackMouseDown.bind(t),t.handleHorizontalThumbMouseDown=t.handleHorizontalThumbMouseDown.bind(t),t.handleVerticalThumbMouseDown=t.handleVerticalThumbMouseDown.bind(t),t.handleWindowResize=t.handleWindowResize.bind(t),t.handleScroll=t.handleScroll.bind(t),t.handleDrag=t.handleDrag.bind(t),t.handleDragEnd=t.handleDragEnd.bind(t),t.state={didMountUniversal:!1},t}return u(y,[{key:"componentDidMount",value:function(){this.addListeners(),this.update(),this.componentDidMountUniversal()}},{key:"componentDidMountUniversal",value:function(){var r=this.props.universal;r&&this.setState({didMountUniversal:!0})}},{key:"componentDidUpdate",value:function(){this.update()}},{key:"componentWillUnmount",value:function(){this.removeListeners(),(0,d.cancel)(this.requestFrame),clearTimeout(this.hideTracksTimeout),clearInterval(this.detectScrollingInterval)}},{key:"getScrollLeft",value:function(){return this.view?this.view.scrollLeft:0}},{key:"getScrollTop",value:function(){return this.view?this.view.scrollTop:0}},{key:"getScrollWidth",value:function(){return this.view?this.view.scrollWidth:0}},{key:"getScrollHeight",value:function(){return this.view?this.view.scrollHeight:0}},{key:"getClientWidth",value:function(){return this.view?this.view.clientWidth:0}},{key:"getClientHeight",value:function(){return this.view?this.view.clientHeight:0}},{key:"getValues",value:function(){var r=this.view||{},a=r.scrollLeft,o=a===void 0?0:a,i=r.scrollTop,t=i===void 0?0:i,c=r.scrollWidth,m=c===void 0?0:c,x=r.scrollHeight,T=x===void 0?0:x,_=r.clientWidth,D=_===void 0?0:_,z=r.clientHeight,E=z===void 0?0:z;return{left:o/(m-D)||0,top:t/(T-E)||0,scrollLeft:o,scrollTop:t,scrollWidth:m,scrollHeight:T,clientWidth:D,clientHeight:E}}},{key:"getThumbHorizontalWidth",value:function(){var r=this.props,a=r.thumbSize,o=r.thumbMinSize,i=this.view,t=i.scrollWidth,c=i.clientWidth,m=(0,U.default)(this.trackHorizontal),x=Math.ceil(c/t*m);return m<=x?0:a||Math.max(x,o)}},{key:"getThumbVerticalHeight",value:function(){var r=this.props,a=r.thumbSize,o=r.thumbMinSize,i=this.view,t=i.scrollHeight,c=i.clientHeight,m=(0,J.default)(this.trackVertical),x=Math.ceil(c/t*m);return m<=x?0:a||Math.max(x,o)}},{key:"getScrollLeftForOffset",value:function(r){var a=this.view,o=a.scrollWidth,i=a.clientWidth,t=(0,U.default)(this.trackHorizontal),c=this.getThumbHorizontalWidth();return r/(t-c)*(o-i)}},{key:"getScrollTopForOffset",value:function(r){var a=this.view,o=a.scrollHeight,i=a.clientHeight,t=(0,J.default)(this.trackVertical),c=this.getThumbVerticalHeight();return r/(t-c)*(o-i)}},{key:"scrollLeft",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.view&&(this.view.scrollLeft=r)}},{key:"scrollTop",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.view&&(this.view.scrollTop=r)}},{key:"scrollToLeft",value:function(){this.view&&(this.view.scrollLeft=0)}},{key:"scrollToTop",value:function(){this.view&&(this.view.scrollTop=0)}},{key:"scrollToRight",value:function(){this.view&&(this.view.scrollLeft=this.view.scrollWidth)}},{key:"scrollToBottom",value:function(){this.view&&(this.view.scrollTop=this.view.scrollHeight)}},{key:"addListeners",value:function(){if(!(typeof document>"u"||!this.view)){var r=this.view,a=this.trackHorizontal,o=this.trackVertical,i=this.thumbHorizontal,t=this.thumbVertical;r.addEventListener("scroll",this.handleScroll),(0,C.default)()&&(a.addEventListener("mouseenter",this.handleTrackMouseEnter),a.addEventListener("mouseleave",this.handleTrackMouseLeave),a.addEventListener("mousedown",this.handleHorizontalTrackMouseDown),o.addEventListener("mouseenter",this.handleTrackMouseEnter),o.addEventListener("mouseleave",this.handleTrackMouseLeave),o.addEventListener("mousedown",this.handleVerticalTrackMouseDown),i.addEventListener("mousedown",this.handleHorizontalThumbMouseDown),t.addEventListener("mousedown",this.handleVerticalThumbMouseDown),window.addEventListener("resize",this.handleWindowResize))}}},{key:"removeListeners",value:function(){if(!(typeof document>"u"||!this.view)){var r=this.view,a=this.trackHorizontal,o=this.trackVertical,i=this.thumbHorizontal,t=this.thumbVertical;r.removeEventListener("scroll",this.handleScroll),(0,C.default)()&&(a.removeEventListener("mouseenter",this.handleTrackMouseEnter),a.removeEventListener("mouseleave",this.handleTrackMouseLeave),a.removeEventListener("mousedown",this.handleHorizontalTrackMouseDown),o.removeEventListener("mouseenter",this.handleTrackMouseEnter),o.removeEventListener("mouseleave",this.handleTrackMouseLeave),o.removeEventListener("mousedown",this.handleVerticalTrackMouseDown),i.removeEventListener("mousedown",this.handleHorizontalThumbMouseDown),t.removeEventListener("mousedown",this.handleVerticalThumbMouseDown),window.removeEventListener("resize",this.handleWindowResize),this.teardownDragging())}}},{key:"handleScroll",value:function(r){var a=this,o=this.props,i=o.onScroll,t=o.onScrollFrame;i&&i(r),this.update(function(c){var m=c.scrollLeft,x=c.scrollTop;a.viewScrollLeft=m,a.viewScrollTop=x,t&&t(c)}),this.detectScrolling()}},{key:"handleScrollStart",value:function(){var r=this.props.onScrollStart;r&&r(),this.handleScrollStartAutoHide()}},{key:"handleScrollStartAutoHide",value:function(){var r=this.props.autoHide;r&&this.showTracks()}},{key:"handleScrollStop",value:function(){var r=this.props.onScrollStop;r&&r(),this.handleScrollStopAutoHide()}},{key:"handleScrollStopAutoHide",value:function(){var r=this.props.autoHide;r&&this.hideTracks()}},{key:"handleWindowResize",value:function(){(0,C.default)(!1),this.forceUpdate()}},{key:"handleHorizontalTrackMouseDown",value:function(r){r.preventDefault();var a=r.target,o=r.clientX,i=a.getBoundingClientRect(),t=i.left,c=this.getThumbHorizontalWidth(),m=Math.abs(t-o)-c/2;this.view.scrollLeft=this.getScrollLeftForOffset(m)}},{key:"handleVerticalTrackMouseDown",value:function(r){r.preventDefault();var a=r.target,o=r.clientY,i=a.getBoundingClientRect(),t=i.top,c=this.getThumbVerticalHeight(),m=Math.abs(t-o)-c/2;this.view.scrollTop=this.getScrollTopForOffset(m)}},{key:"handleHorizontalThumbMouseDown",value:function(r){r.preventDefault(),this.handleDragStart(r);var a=r.target,o=r.clientX,i=a.offsetWidth,t=a.getBoundingClientRect(),c=t.left;this.prevPageX=i-(o-c)}},{key:"handleVerticalThumbMouseDown",value:function(r){r.preventDefault(),this.handleDragStart(r);var a=r.target,o=r.clientY,i=a.offsetHeight,t=a.getBoundingClientRect(),c=t.top;this.prevPageY=i-(o-c)}},{key:"setupDragging",value:function(){(0,f.default)(document.body,A.disableSelectStyle),document.addEventListener("mousemove",this.handleDrag),document.addEventListener("mouseup",this.handleDragEnd),document.onselectstart=K.default}},{key:"teardownDragging",value:function(){(0,f.default)(document.body,A.disableSelectStyleReset),document.removeEventListener("mousemove",this.handleDrag),document.removeEventListener("mouseup",this.handleDragEnd),document.onselectstart=void 0}},{key:"handleDragStart",value:function(r){this.dragging=!0,r.stopImmediatePropagation(),this.setupDragging()}},{key:"handleDrag",value:function(r){if(this.prevPageX){var a=r.clientX,o=this.trackHorizontal.getBoundingClientRect(),i=o.left,t=this.getThumbHorizontalWidth(),c=t-this.prevPageX,m=-i+a-c;this.view.scrollLeft=this.getScrollLeftForOffset(m)}if(this.prevPageY){var x=r.clientY,T=this.trackVertical.getBoundingClientRect(),_=T.top,D=this.getThumbVerticalHeight(),z=D-this.prevPageY,E=-_+x-z;this.view.scrollTop=this.getScrollTopForOffset(E)}return!1}},{key:"handleDragEnd",value:function(){this.dragging=!1,this.prevPageX=this.prevPageY=0,this.teardownDragging(),this.handleDragEndAutoHide()}},{key:"handleDragEndAutoHide",value:function(){var r=this.props.autoHide;r&&this.hideTracks()}},{key:"handleTrackMouseEnter",value:function(){this.trackMouseOver=!0,this.handleTrackMouseEnterAutoHide()}},{key:"handleTrackMouseEnterAutoHide",value:function(){var r=this.props.autoHide;r&&this.showTracks()}},{key:"handleTrackMouseLeave",value:function(){this.trackMouseOver=!1,this.handleTrackMouseLeaveAutoHide()}},{key:"handleTrackMouseLeaveAutoHide",value:function(){var r=this.props.autoHide;r&&this.hideTracks()}},{key:"showTracks",value:function(){clearTimeout(this.hideTracksTimeout),(0,f.default)(this.trackHorizontal,{opacity:1}),(0,f.default)(this.trackVertical,{opacity:1})}},{key:"hideTracks",value:function(){var r=this;if(!this.dragging&&!this.scrolling&&!this.trackMouseOver){var a=this.props.autoHideTimeout;clearTimeout(this.hideTracksTimeout),this.hideTracksTimeout=setTimeout(function(){(0,f.default)(r.trackHorizontal,{opacity:0}),(0,f.default)(r.trackVertical,{opacity:0})},a)}}},{key:"detectScrolling",value:function(){var r=this;this.scrolling||(this.scrolling=!0,this.handleScrollStart(),this.detectScrollingInterval=setInterval(function(){r.lastViewScrollLeft===r.viewScrollLeft&&r.lastViewScrollTop===r.viewScrollTop&&(clearInterval(r.detectScrollingInterval),r.scrolling=!1,r.handleScrollStop()),r.lastViewScrollLeft=r.viewScrollLeft,r.lastViewScrollTop=r.viewScrollTop},100))}},{key:"raf",value:function(r){var a=this;this.requestFrame&&h.default.cancel(this.requestFrame),this.requestFrame=(0,h.default)(function(){a.requestFrame=void 0,r()})}},{key:"update",value:function(r){var a=this;this.raf(function(){return a._update(r)})}},{key:"_update",value:function(r){var a=this.props,o=a.onUpdate,i=a.hideTracksWhenNotNeeded,t=this.getValues();if((0,C.default)()){var c=t.scrollLeft,m=t.clientWidth,x=t.scrollWidth,T=(0,U.default)(this.trackHorizontal),_=this.getThumbHorizontalWidth(),D=c/(x-m)*(T-_),z={width:_,transform:"translateX("+D+"px)"},E=t.scrollTop,I=t.clientHeight,W=t.scrollHeight,O=(0,J.default)(this.trackVertical),R=this.getThumbVerticalHeight(),Q=E/(W-I)*(O-R),q={height:R,transform:"translateY("+Q+"px)"};if(i){var Te={visibility:x>m?"visible":"hidden"},_e={visibility:W>I?"visible":"hidden"};(0,f.default)(this.trackHorizontal,Te),(0,f.default)(this.trackVertical,_e)}(0,f.default)(this.thumbHorizontal,z),(0,f.default)(this.thumbVertical,q)}o&&o(t),typeof r=="function"&&r(t)}},{key:"render",value:function(){var r=this,a=(0,C.default)(),o=this.props;o.onScroll,o.onScrollFrame,o.onScrollStart,o.onScrollStop,o.onUpdate;var i=o.renderView,t=o.renderTrackHorizontal,c=o.renderTrackVertical,m=o.renderThumbHorizontal,x=o.renderThumbVertical,T=o.tagName;o.hideTracksWhenNotNeeded;var _=o.autoHide;o.autoHideTimeout;var D=o.autoHideDuration;o.thumbSize,o.thumbMinSize;var z=o.universal,E=o.autoHeight,I=o.autoHeightMin,W=o.autoHeightMax,O=o.style,R=o.children,Q=te(o,["onScroll","onScrollFrame","onScrollStart","onScrollStop","onUpdate","renderView","renderTrackHorizontal","renderTrackVertical","renderThumbHorizontal","renderThumbVertical","tagName","hideTracksWhenNotNeeded","autoHide","autoHideTimeout","autoHideDuration","thumbSize","thumbMinSize","universal","autoHeight","autoHeightMin","autoHeightMax","style","children"]),q=this.state.didMountUniversal,Te=s({},A.containerStyleDefault,E&&s({},A.containerStyleAutoHeight,{minHeight:I,maxHeight:W}),O),_e=s({},A.viewStyleDefault,{marginRight:a?-a:0,marginBottom:a?-a:0},E&&s({},A.viewStyleAutoHeight,{minHeight:(0,L.default)(I)?"calc("+I+" + "+a+"px)":I+a,maxHeight:(0,L.default)(W)?"calc("+W+" + "+a+"px)":W+a}),E&&z&&!q&&{minHeight:I,maxHeight:W},z&&!q&&A.viewStyleUniversalInitial),$e={transition:"opacity "+D+"ms",opacity:0},lt=s({},A.trackHorizontalStyleDefault,_&&$e,(!a||z&&!q)&&{display:"none"}),ct=s({},A.trackVerticalStyleDefault,_&&$e,(!a||z&&!q)&&{display:"none"});return(0,H.createElement)(T,s({},Q,{style:Te,ref:function(G){r.container=G}}),[(0,H.cloneElement)(i({style:_e}),{key:"view",ref:function(G){r.view=G}},R),(0,H.cloneElement)(t({style:lt}),{key:"trackHorizontal",ref:function(G){r.trackHorizontal=G}},(0,H.cloneElement)(m({style:A.thumbHorizontalStyleDefault}),{ref:function(G){r.thumbHorizontal=G}})),(0,H.cloneElement)(c({style:ct}),{key:"trackVertical",ref:function(G){r.trackVertical=G}},(0,H.cloneElement)(x({style:A.thumbVerticalStyleDefault}),{ref:function(G){r.thumbVertical=G}}))])}}]),y}(H.Component);e.default=b,b.propTypes={onScroll:v.default.func,onScrollFrame:v.default.func,onScrollStart:v.default.func,onScrollStop:v.default.func,onUpdate:v.default.func,renderView:v.default.func,renderTrackHorizontal:v.default.func,renderTrackVertical:v.default.func,renderThumbHorizontal:v.default.func,renderThumbVertical:v.default.func,tagName:v.default.string,thumbSize:v.default.number,thumbMinSize:v.default.number,hideTracksWhenNotNeeded:v.default.bool,autoHide:v.default.bool,autoHideTimeout:v.default.number,autoHideDuration:v.default.number,autoHeight:v.default.bool,autoHeightMin:v.default.oneOfType([v.default.number,v.default.string]),autoHeightMax:v.default.oneOfType([v.default.number,v.default.string]),universal:v.default.bool,style:v.default.object,children:v.default.node},b.defaultProps={renderView:P.renderViewDefault,renderTrackHorizontal:P.renderTrackHorizontalDefault,renderTrackVertical:P.renderTrackVerticalDefault,renderThumbHorizontal:P.renderThumbHorizontalDefault,renderThumbVertical:P.renderThumbVerticalDefault,tagName:"div",thumbMinSize:30,hideTracksWhenNotNeeded:!1,autoHide:!1,autoHideTimeout:1e3,autoHideDuration:200,autoHeight:!1,autoHeightMin:0,autoHeightMax:200,universal:!1}})(Ge);(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.Scrollbars=void 0;var s=Ge,u=d(s);function d(h){return h&&h.__esModule?h:{default:h}}e.default=u.default,e.Scrollbars=u.default})(Oe);const nn=Re(Oe);function nr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"}},{tag:"path",attr:{d:"M13.73 21a2 2 0 0 1-3.46 0"}}]})(e)}function an(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 6 9 17 4 12"}}]})(e)}function on(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"9 18 15 12 9 6"}}]})(e)}function ar(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"polygon",attr:{points:"16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"}}]})(e)}function sn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"7 10 12 15 17 10"}},{tag:"line",attr:{x1:"12",y1:"15",x2:"12",y2:"3"}}]})(e)}function ln(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}},{tag:"path",attr:{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}}]})(e)}function or(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"line",attr:{x1:"2",y1:"12",x2:"22",y2:"12"}},{tag:"path",attr:{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"}}]})(e)}function nt(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"3",width:"7",height:"7"}},{tag:"rect",attr:{x:"14",y:"3",width:"7",height:"7"}},{tag:"rect",attr:{x:"14",y:"14",width:"7",height:"7"}},{tag:"rect",attr:{x:"3",y:"14",width:"7",height:"7"}}]})(e)}function ir(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}},{tag:"polyline",attr:{points:"16 17 21 12 16 7"}},{tag:"line",attr:{x1:"21",y1:"12",x2:"9",y2:"12"}}]})(e)}function cn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}},{tag:"polyline",attr:{points:"22,6 12,13 2,6"}}]})(e)}function sr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"3",y1:"12",x2:"21",y2:"12"}},{tag:"line",attr:{x1:"3",y1:"6",x2:"21",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"18",x2:"21",y2:"18"}}]})(e)}function lr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"}}]})(e)}function un(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"5",x2:"12",y2:"19"}},{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}}]})(e)}function dn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"6 9 6 2 18 2 18 9"}},{tag:"path",attr:{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"}},{tag:"rect",attr:{x:"6",y:"14",width:"12",height:"8"}}]})(e)}function hn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 4 23 10 17 10"}},{tag:"polyline",attr:{points:"1 20 1 14 7 14"}},{tag:"path",attr:{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"}}]})(e)}function at(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"3"}},{tag:"path",attr:{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}}]})(e)}function fn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"9",cy:"21",r:"1"}},{tag:"circle",attr:{cx:"20",cy:"21",r:"1"}},{tag:"path",attr:{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"}}]})(e)}function Pe(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z"}},{tag:"path",attr:{d:"M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"}},{tag:"path",attr:{d:"M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z"}},{tag:"path",attr:{d:"M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z"}},{tag:"path",attr:{d:"M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z"}},{tag:"path",attr:{d:"M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"}},{tag:"path",attr:{d:"M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z"}},{tag:"path",attr:{d:"M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z"}}]})(e)}function cr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"5"}},{tag:"line",attr:{x1:"12",y1:"1",x2:"12",y2:"3"}},{tag:"line",attr:{x1:"12",y1:"21",x2:"12",y2:"23"}},{tag:"line",attr:{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}},{tag:"line",attr:{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}},{tag:"line",attr:{x1:"1",y1:"12",x2:"3",y2:"12"}},{tag:"line",attr:{x1:"21",y1:"12",x2:"23",y2:"12"}},{tag:"line",attr:{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}},{tag:"line",attr:{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"}}]})(e)}function ur(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"6"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"2"}}]})(e)}function dr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"3 6 5 6 21 6"}},{tag:"path",attr:{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}},{tag:"line",attr:{x1:"10",y1:"11",x2:"10",y2:"17"}},{tag:"line",attr:{x1:"14",y1:"11",x2:"14",y2:"17"}}]})(e)}function gn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"1",y:"3",width:"15",height:"13"}},{tag:"polygon",attr:{points:"16 8 20 8 23 11 23 16 16 16 16 8"}},{tag:"circle",attr:{cx:"5.5",cy:"18.5",r:"2.5"}},{tag:"circle",attr:{cx:"18.5",cy:"18.5",r:"2.5"}}]})(e)}function pn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"16 16 12 12 8 16"}},{tag:"line",attr:{x1:"12",y1:"12",x2:"12",y2:"21"}},{tag:"path",attr:{d:"M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3"}},{tag:"polyline",attr:{points:"16 16 12 12 8 16"}}]})(e)}function mn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"17 8 12 3 7 8"}},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"}}]})(e)}function hr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"12",cy:"7",r:"4"}}]})(e)}function fr(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"9",cy:"7",r:"4"}},{tag:"path",attr:{d:"M23 21v-2a4 4 0 0 0-3-3.87"}},{tag:"path",attr:{d:"M16 3.13a4 4 0 0 1 0 7.75"}}]})(e)}function vn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"line",attr:{x1:"15",y1:"9",x2:"9",y2:"15"}},{tag:"line",attr:{x1:"9",y1:"9",x2:"15",y2:"15"}}]})(e)}function yn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}function xn(e){return w({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"11",cy:"11",r:"8"}},{tag:"line",attr:{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}},{tag:"line",attr:{x1:"11",y1:"8",x2:"11",y2:"14"}},{tag:"line",attr:{x1:"8",y1:"11",x2:"14",y2:"11"}}]})(e)}const gr="data:image/svg+xml,%3csvg%20viewBox='0%200%20512%20512'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M256%20464c-114.69%200-208-93.31-208-208S141.31%2048%20256%2048s208%2093.31%20208%20208-93.31%20208-208%20208Z'%20fill='%23148f45'%20class='fill-000000'%3e%3c/path%3e%3c/svg%3e",pr=()=>{xt();const[e,s]=g.useState(null),[u,d]=g.useState(!1);return{socket:e,updated:u,setUpdated:d}};var ot={exports:{}};(function(e,s){(function(u,d){e.exports=d()})(ze,function(){var u=1e3,d=6e4,h=36e5,k="millisecond",f="second",H="minute",$="hour",v="day",V="week",L="month",B="quarter",C="year",F="date",K="Invalid Date",p=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,U=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,ce={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(l){var r=["th","st","nd","rd"],a=l%100;return"["+l+(r[(a-20)%10]||r[a]||r[0])+"]"}},J=function(l,r,a){var o=String(l);return!o||o.length>=r?l:""+Array(r+1-o.length).join(a)+l},A={s:J,z:function(l){var r=-l.utcOffset(),a=Math.abs(r),o=Math.floor(a/60),i=a%60;return(r<=0?"+":"-")+J(o,2,"0")+":"+J(i,2,"0")},m:function l(r,a){if(r.date()<a.date())return-l(a,r);var o=12*(a.year()-r.year())+(a.month()-r.month()),i=r.clone().add(o,L),t=a-i<0,c=r.clone().add(o+(t?-1:1),L);return+(-(o+(a-i)/(t?i-c:c-i))||0)},a:function(l){return l<0?Math.ceil(l)||0:Math.floor(l)},p:function(l){return{M:L,y:C,w:V,d:v,D:F,h:$,m:H,s:f,ms:k,Q:B}[l]||String(l||"").toLowerCase().replace(/s$/,"")},u:function(l){return l===void 0}},P="en",N={};N[P]=ce;var te="$isDayjsObject",re=function(l){return l instanceof S||!(!l||!l[te])},Y=function l(r,a,o){var i;if(!r)return P;if(typeof r=="string"){var t=r.toLowerCase();N[t]&&(i=t),a&&(N[t]=a,i=t);var c=r.split("-");if(!i&&c.length>1)return l(c[0])}else{var m=r.name;N[m]=r,i=m}return!o&&i&&(P=i),i||!o&&P},j=function(l,r){if(re(l))return l.clone();var a=typeof r=="object"?r:{};return a.date=l,a.args=arguments,new S(a)},b=A;b.l=Y,b.i=re,b.w=function(l,r){return j(l,{locale:r.$L,utc:r.$u,x:r.$x,$offset:r.$offset})};var S=function(){function l(a){this.$L=Y(a.locale,null,!0),this.parse(a),this.$x=this.$x||a.x||{},this[te]=!0}var r=l.prototype;return r.parse=function(a){this.$d=function(o){var i=o.date,t=o.utc;if(i===null)return new Date(NaN);if(b.u(i))return new Date;if(i instanceof Date)return new Date(i);if(typeof i=="string"&&!/Z$/i.test(i)){var c=i.match(p);if(c){var m=c[2]-1||0,x=(c[7]||"0").substring(0,3);return t?new Date(Date.UTC(c[1],m,c[3]||1,c[4]||0,c[5]||0,c[6]||0,x)):new Date(c[1],m,c[3]||1,c[4]||0,c[5]||0,c[6]||0,x)}}return new Date(i)}(a),this.init()},r.init=function(){var a=this.$d;this.$y=a.getFullYear(),this.$M=a.getMonth(),this.$D=a.getDate(),this.$W=a.getDay(),this.$H=a.getHours(),this.$m=a.getMinutes(),this.$s=a.getSeconds(),this.$ms=a.getMilliseconds()},r.$utils=function(){return b},r.isValid=function(){return this.$d.toString()!==K},r.isSame=function(a,o){var i=j(a);return this.startOf(o)<=i&&i<=this.endOf(o)},r.isAfter=function(a,o){return j(a)<this.startOf(o)},r.isBefore=function(a,o){return this.endOf(o)<j(a)},r.$g=function(a,o,i){return b.u(a)?this[o]:this.set(i,a)},r.unix=function(){return Math.floor(this.valueOf()/1e3)},r.valueOf=function(){return this.$d.getTime()},r.startOf=function(a,o){var i=this,t=!!b.u(o)||o,c=b.p(a),m=function(W,O){var R=b.w(i.$u?Date.UTC(i.$y,O,W):new Date(i.$y,O,W),i);return t?R:R.endOf(v)},x=function(W,O){return b.w(i.toDate()[W].apply(i.toDate("s"),(t?[0,0,0,0]:[23,59,59,999]).slice(O)),i)},T=this.$W,_=this.$M,D=this.$D,z="set"+(this.$u?"UTC":"");switch(c){case C:return t?m(1,0):m(31,11);case L:return t?m(1,_):m(0,_+1);case V:var E=this.$locale().weekStart||0,I=(T<E?T+7:T)-E;return m(t?D-I:D+(6-I),_);case v:case F:return x(z+"Hours",0);case $:return x(z+"Minutes",1);case H:return x(z+"Seconds",2);case f:return x(z+"Milliseconds",3);default:return this.clone()}},r.endOf=function(a){return this.startOf(a,!1)},r.$set=function(a,o){var i,t=b.p(a),c="set"+(this.$u?"UTC":""),m=(i={},i[v]=c+"Date",i[F]=c+"Date",i[L]=c+"Month",i[C]=c+"FullYear",i[$]=c+"Hours",i[H]=c+"Minutes",i[f]=c+"Seconds",i[k]=c+"Milliseconds",i)[t],x=t===v?this.$D+(o-this.$W):o;if(t===L||t===C){var T=this.clone().set(F,1);T.$d[m](x),T.init(),this.$d=T.set(F,Math.min(this.$D,T.daysInMonth())).$d}else m&&this.$d[m](x);return this.init(),this},r.set=function(a,o){return this.clone().$set(a,o)},r.get=function(a){return this[b.p(a)]()},r.add=function(a,o){var i,t=this;a=Number(a);var c=b.p(o),m=function(_){var D=j(t);return b.w(D.date(D.date()+Math.round(_*a)),t)};if(c===L)return this.set(L,this.$M+a);if(c===C)return this.set(C,this.$y+a);if(c===v)return m(1);if(c===V)return m(7);var x=(i={},i[H]=d,i[$]=h,i[f]=u,i)[c]||1,T=this.$d.getTime()+a*x;return b.w(T,this)},r.subtract=function(a,o){return this.add(-1*a,o)},r.format=function(a){var o=this,i=this.$locale();if(!this.isValid())return i.invalidDate||K;var t=a||"YYYY-MM-DDTHH:mm:ssZ",c=b.z(this),m=this.$H,x=this.$m,T=this.$M,_=i.weekdays,D=i.months,z=i.meridiem,E=function(O,R,Q,q){return O&&(O[R]||O(o,t))||Q[R].slice(0,q)},I=function(O){return b.s(m%12||12,O,"0")},W=z||function(O,R,Q){var q=O<12?"AM":"PM";return Q?q.toLowerCase():q};return t.replace(U,function(O,R){return R||function(Q){switch(Q){case"YY":return String(o.$y).slice(-2);case"YYYY":return b.s(o.$y,4,"0");case"M":return T+1;case"MM":return b.s(T+1,2,"0");case"MMM":return E(i.monthsShort,T,D,3);case"MMMM":return E(D,T);case"D":return o.$D;case"DD":return b.s(o.$D,2,"0");case"d":return String(o.$W);case"dd":return E(i.weekdaysMin,o.$W,_,2);case"ddd":return E(i.weekdaysShort,o.$W,_,3);case"dddd":return _[o.$W];case"H":return String(m);case"HH":return b.s(m,2,"0");case"h":return I(1);case"hh":return I(2);case"a":return W(m,x,!0);case"A":return W(m,x,!1);case"m":return String(x);case"mm":return b.s(x,2,"0");case"s":return String(o.$s);case"ss":return b.s(o.$s,2,"0");case"SSS":return b.s(o.$ms,3,"0");case"Z":return c}return null}(O)||c.replace(":","")})},r.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},r.diff=function(a,o,i){var t,c=this,m=b.p(o),x=j(a),T=(x.utcOffset()-this.utcOffset())*d,_=this-x,D=function(){return b.m(c,x)};switch(m){case C:t=D()/12;break;case L:t=D();break;case B:t=D()/3;break;case V:t=(_-T)/6048e5;break;case v:t=(_-T)/864e5;break;case $:t=_/h;break;case H:t=_/d;break;case f:t=_/u;break;default:t=_}return i?t:b.a(t)},r.daysInMonth=function(){return this.endOf(L).$D},r.$locale=function(){return N[this.$L]},r.locale=function(a,o){if(!a)return this.$L;var i=this.clone(),t=Y(a,o,!0);return t&&(i.$L=t),i},r.clone=function(){return b.w(this.$d,this)},r.toDate=function(){return new Date(this.valueOf())},r.toJSON=function(){return this.isValid()?this.toISOString():null},r.toISOString=function(){return this.$d.toISOString()},r.toString=function(){return this.$d.toUTCString()},l}(),y=S.prototype;return j.prototype=y,[["$ms",k],["$s",f],["$m",H],["$H",$],["$W",v],["$M",L],["$y",C],["$D",F]].forEach(function(l){y[l[1]]=function(r){return this.$g(r,l[0],l[1])}}),j.extend=function(l,r){return l.$i||(l(r,S,j),l.$i=!0),j},j.locale=Y,j.isDayjs=re,j.unix=function(l){return j(1e3*l)},j.en=N[P],j.Ls=N,j.p={},j})})(ot);var mr=ot.exports;const je=Re(mr),it=()=>{const{lang:e}=g.useContext(be),{error:s,isLoading:u,data:d}=We({queryKey:["globalSetting"],queryFn:async()=>await dt.getGlobalSetting(),staleTime:20*60*1e3,gcTime:25*60*1e3}),{data:h,error:k,isLoading:f}=We({queryKey:["languages"],queryFn:async()=>await ht.getShowingLanguage(),staleTime:20*60*1e3,gcTime:25*60*1e3}),H=(p,U)=>je(p).format(U),$=p=>je(p).format(d==null?void 0:d.default_date_format),v=p=>je(p).format(`${d==null?void 0:d.default_date_format}  h:mm A`),V=(p=0)=>Number(parseFloat(p||0).toFixed(2)),L=(p=0)=>parseFloat(p||0).toFixed((d==null?void 0:d.floating_number)||2),B=g.useMemo(()=>p=>!p||typeof p!="object"?"":p[e]??p[d==null?void 0:d.default_language]??p.en??"",[e,d==null?void 0:d.default_language]),C=p=>p!==void 0&&p,F=p=>p!==void 0?p:"!#",K=(d==null?void 0:d.default_currency)||"$";return{error:s,loading:u,currency:K,getNumber:V,langError:k,langLoading:f,getNumberTwo:L,showTimeFormat:H,showDateFormat:$,showingImage:C,showingUrl:F,languages:h,globalSetting:d,showDateTimeFormat:v,showingTranslateValue:B}},vr=({title:e})=>n.jsx("div",{className:"flex justify-center item h-full items-center",children:n.jsxs("h2",{className:"text-lg md:text-xl text-center mt-2 font-medium font-serif text-gray-600",children:[e,n.jsx("span",{role:"img","aria-labelledby":"img",children:"😞"})]})}),he={addNotification:async e=>ae.post("/notification/add",e),getAllNotification:async e=>ae.get(`/notification?page=${e}`),updateStatusNotification:async(e,s)=>ae.put(`/notification/${e}`,s),updateManyStatusNotification:async e=>ae.patch("/notification/update/many",e),deleteNotification:async e=>ae.delete(`/notification/${e}`),deleteNotificationByProductId:async e=>ae.delete(`/notification/product-id/${e}`),deleteManyNotification:async e=>ae.patch("/notification/delete/many",e)},yr=({handleLanguageChange:e})=>{const{languages:s,langError:u,langLoading:d}=it();return n.jsx("ul",{className:"dropdown-content w-full",children:!u&&!d&&(s==null?void 0:s.map(h=>n.jsxs("li",{className:"cursor-pointer flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-md",onClick:()=>e(h),children:[n.jsx("div",{className:"flag bg-start",style:{backgroundImage:`url(https://flagcdn.com/w20/${h.flag.toLowerCase()}.png)`}}),n.jsx("span",{className:"text-gray-900 dark:text-gray-600 pr-8 text-right",children:h==null?void 0:h.name})]},h._id)))})},xr=()=>{var a;const{toggleSidebar:e,handleLanguageChange:s,setNavBar:u,navBar:d,currLang:h}=g.useContext(be),{state:k,dispatch:f}=g.useContext(Ve),{adminInfo:H}=k,{mode:$,toggleMode:v}=g.useContext(X.WindmillContext),V=g.useRef(),L=g.useRef();De.get("i18next");const{t:B}=Ee(),{updated:C,setUpdated:F}=pr(),{showDateTimeFormat:K}=it(),[p,U]=g.useState([]),[ce,J]=g.useState(0),[A,P]=g.useState(0),[N,te]=g.useState(!1),[re,Y]=g.useState(!1),j=()=>{f({type:"USER_LOGOUT"}),De.remove("adminInfo"),window.location.replace("https://dashtar-admin.netlify.app/login/login")},b=async()=>{Y(!re),te(!1),await r()},S=()=>{te(!N),Y(!1)},y=async o=>{var i,t;try{await he.updateStatusNotification(o,{status:"read"});const c=await he.getAllNotification();U(c==null?void 0:c.notifications),P(c==null?void 0:c.totalUnreadDoc),window.location.reload(!1)}catch(c){notifyError(((t=(i=c==null?void 0:c.response)==null?void 0:i.data)==null?void 0:t.message)||(c==null?void 0:c.message))}},l=async o=>{var i,t;try{await he.deleteNotification(o);const c=await he.getAllNotification();U(c==null?void 0:c.notifications),P(c==null?void 0:c.totalUnreadDoc),J(c==null?void 0:c.totalDoc)}catch(c){notifyError(((t=(i=c==null?void 0:c.response)==null?void 0:i.data)==null?void 0:t.message)||(c==null?void 0:c.message))}},r=async()=>{var o,i;try{const t=await he.getAllNotification();U(t==null?void 0:t.notifications),P(t==null?void 0:t.totalUnreadDoc),J(t==null?void 0:t.totalDoc),F(!1)}catch(t){F(!1),notifyError(((i=(o=t==null?void 0:t.response)==null?void 0:o.data)==null?void 0:i.message)||(t==null?void 0:t.message))}};return g.useEffect(()=>{const o=i=>{var t,c;(t=V==null?void 0:V.current)!=null&&t.contains(i.target)||te(!1),(c=L==null?void 0:L.current)!=null&&c.contains(i.target)||Y(!1)};document.addEventListener("mousedown",o)},[V,L]),g.useEffect(()=>{r()},[C]),n.jsx(n.Fragment,{children:n.jsx("header",{className:"z-30 py-4 bg-white shadow-sm dark:bg-gray-800",children:n.jsxs("div",{className:"container flex items-center justify-between h-full px-6 mx-auto text-emerald-500 dark:text-emerald-500",children:[n.jsx("button",{type:"button",onClick:()=>u(!d),className:"hidden lg:block outline-0 focus:outline-none",children:n.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 18 18",xmlns:"http://www.w3.org/2000/svg",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})}),n.jsx("button",{className:"p-1 mr-5 -ml-1 rounded-md lg:hidden focus:outline-none",onClick:e,"aria-label":"Menu",children:n.jsx(sr,{className:"w-6 h-6","aria-hidden":"true"})}),n.jsx("span",{}),n.jsxs("ul",{className:"flex justify-end items-center flex-shrink-0 space-x-6",children:[n.jsx("li",{className:"changeLanguage",children:n.jsxs("div",{className:"dropdown",children:[n.jsxs("button",{className:"dropbtn focus:outline-none flex",children:[n.jsx("div",{className:`text-sm flag ${(a=h==null?void 0:h.flag)==null?void 0:a.toLowerCase()}`})," ",n.jsx("span",{className:"md:inline-block hidden text-gray-900 dark:text-gray-300",children:h==null?void 0:h.name}),n.jsx("span",{className:"md:hidden uppercase",children:h==null?void 0:h.iso_code})]}),n.jsx(yr,{handleLanguageChange:s})]})}),n.jsx("li",{className:"flex",children:n.jsx("button",{className:"rounded-md focus:outline-none",onClick:v,"aria-label":"Toggle color mode",children:$==="dark"?n.jsx(cr,{className:"w-5 h-5","aria-hidden":"true"}):n.jsx(lr,{className:"w-5 h-5","aria-hidden":"true"})})}),n.jsxs("li",{className:"relative inline-block text-left",ref:L,children:[n.jsxs("button",{className:"relative align-middle rounded-md focus:outline-none",onClick:b,children:[n.jsx(nr,{className:"w-5 h-5 text-emerald-500","aria-hidden":"true"}),n.jsx("span",{className:"absolute z-10 top-0 right-0 inline-flex items-center justify-center p-1 h-5 w-5 text-xs font-medium leading-none text-red-100 transform -translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full",children:A})]}),re&&n.jsx("div",{className:"origin-top-right absolute md:right-0 -right-3 top-2 rounded-md shadow-lg bg-white dark:bg-gray-800  focus:outline-none",children:n.jsx("div",{className:`${(p==null?void 0:p.length)===0||(p==null?void 0:p.length)<=2?"h-40":(p==null?void 0:p.length)<=3?"h-56":"h-330"} md:w-400 w-300`,children:n.jsxs(Oe.Scrollbars,{children:[(p==null?void 0:p.length)===0?n.jsx(vr,{title:"No new notification"}):n.jsx("ul",{className:"block text-sm border-t border-gray-100 dark:border-gray-700 rounded-md",children:p==null?void 0:p.map((o,i)=>n.jsxs("li",{className:`flex justify-between items-center font-serif font-normal text-sm py-3 border-b border-gray-100 dark:border-gray-700 px-3 transition-colors duration-150 hover:bg-gray-100 ${o.status==="unread"&&"bg-gray-50"} hover:text-gray-800 dark:text-gray-400 ${o.status==="unread"&&"dark:bg-gray-800"} dark:hover:bg-gray-900  dark:hover:text-gray-100 cursor-pointer`,children:[n.jsxs(ye,{to:o.productId?`/product/${o.productId}`:o.orderId?`/order/${o.orderId}`:"/our-staff",className:"flex items-center",onClick:()=>y(o._id),children:[n.jsx(X.Avatar,{className:"mr-2 md:block bg-gray-50 border border-gray-200",src:o.image,alt:"image"}),n.jsxs("div",{className:"notification-content",children:[n.jsx("h6",{className:"font-medium text-gray-500",children:o==null?void 0:o.message}),n.jsxs("p",{className:"flex items-center text-xs text-gray-400",children:[o.productId?n.jsx(X.Badge,{type:"danger",children:"Stock Out"}):n.jsx(X.Badge,{type:"success",children:"New Order"}),n.jsx("span",{className:"ml-2",children:K(o.createdAt)})]})]}),o.status==="unread"&&n.jsx("span",{className:"px-2 focus:outline-none",children:n.jsx("img",{src:gr,width:12,height:12,alt:"ellipse",className:"w-3 h-3 text-emerald-600"})})]}),n.jsxs("div",{className:"group inline-block relative",children:[n.jsx("button",{type:"button",onClick:()=>l(o._id),className:"px-2 group-hover:text-blue-500 text-red-500 focus:outline-none",children:n.jsx(dr,{})}),n.jsx("div",{className:"absolute hidden group-hover:inline-block bg-gray-50 dark:text-red-400 mr-6 mb-1 right-0 z-50 px-3 py-2 text-sm font-medium text-red-600 rounded-lg shadow-sm tooltip dark:bg-gray-700",children:"Delete"})]})]},i+1))}),ce>5&&n.jsx("div",{className:"text-center py-2",children:n.jsx(ye,{onClick:()=>Y(!1),to:"/notifications",className:"focus:outline-none hover:underline transition ease-out duration-200",children:"Show all notifications"})})]})})})]}),n.jsxs("li",{className:"relative inline-block text-left",ref:V,children:[n.jsx("button",{className:"rounded-full dark:bg-gray-500 bg-emerald-500 text-white h-8 w-8 font-medium mx-auto focus:outline-none",onClick:S,children:H.image?n.jsx(X.Avatar,{className:"align-middle",src:`${H.image}`,"aria-hidden":"true"}):n.jsx("span",{children:H.email[0].toUpperCase()})}),N&&n.jsxs("ul",{className:"origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 focus:outline-none",children:[n.jsx("li",{className:"justify-between font-serif font-medium py-2 pl-4 transition-colors duration-150 hover:bg-gray-100 text-gray-500 hover:text-emerald-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",children:n.jsx(ye,{to:"/dashboard",children:n.jsxs("span",{className:"flex items-center text-sm",children:[n.jsx(nt,{className:"w-4 h-4 mr-3","aria-hidden":"true"}),n.jsx("span",{children:B("Dashboard")})]})})}),n.jsx("li",{className:"justify-between font-serif font-medium py-2 pl-4 transition-colors duration-150 hover:bg-gray-100 text-gray-500 hover:text-emerald-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",children:n.jsx(ye,{to:"/edit-profile",children:n.jsxs("span",{className:"flex items-center text-sm",children:[n.jsx(at,{className:"w-4 h-4 mr-3","aria-hidden":"true"}),n.jsx("span",{children:B("EditProfile")})]})})}),n.jsx("li",{onClick:j,className:"cursor-pointer justify-between font-serif font-medium py-2 pl-4 transition-colors duration-150 hover:bg-gray-100 text-gray-500 hover:text-emerald-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",children:n.jsxs("span",{className:"flex items-center text-sm",children:[n.jsx(ir,{className:"w-4 h-4 mr-3","aria-hidden":"true"}),n.jsx("span",{children:B("LogOut")})]})})]})]})]})]})})})};function kn(e){return w({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M454.65 169.4A31.82 31.82 0 00432 160h-64v-16a112 112 0 00-224 0v16H80a32 32 0 00-32 32v216c0 39 33 72 72 72h272a72.22 72.22 0 0050.48-20.55 69.48 69.48 0 0021.52-50.2V192a31.75 31.75 0 00-9.35-22.6zM176 144a80 80 0 01160 0v16H176zm192 96a112 112 0 01-224 0v-16a16 16 0 0132 0v16a80 80 0 00160 0v-16a16 16 0 0132 0z"}}]})(e)}function kr(e){return w({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 184l144 144 144-144"}}]})(e)}function wr(e){return w({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M184 112l144 144-144 144"}}]})(e)}function wn(e){return w({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M320 336h76c55 0 100-21.21 100-75.6s-53-73.47-96-75.6C391.11 99.74 329 48 256 48c-69 0-113.44 45.79-128 91.2-60 5.7-112 35.88-112 98.4S70 336 136 336h56m0 64.1l64 63.9 64-63.9M256 224v224.03"}}]})(e)}function br(e){return w({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M304 336v40a40 40 0 01-40 40H104a40 40 0 01-40-40V136a40 40 0 0140-40h152c22.09 0 48 17.91 48 40v40m64 160l80-80-80-80m-192 80h256"}}]})(e)}function Be(e){return w({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"square",strokeLinejoin:"round",strokeWidth:"32",d:"M400 256H112"}}]})(e)}const Hr=[{path:"/dashboard",icon:nt,name:"Dashboard"},{icon:Pe,name:"Catalog",routes:[{path:"/products",name:"Products"},{path:"/categories",name:"Categories"},{path:"/attributes",name:"Attributes"},{path:"/coupons",name:"Coupons"}]},{path:"/customers",icon:fr,name:"Customers"},{path:"/orders",icon:ar,name:"Orders"},{path:"/our-staff",icon:hr,name:"OurStaff"},{path:"/settings?settingTab=common-settings",icon:at,name:"Settings"},{icon:or,name:"International",routes:[{path:"/languages",name:"Languages"},{path:"/currencies",name:"Currencies"}]},{icon:ur,name:"OnlineStore",routes:[{name:"ViewStore",path:"/store",outside:"store"},{path:"/store/customization",name:"StoreCustomization"},{path:"/store/store-settings",name:"StoreSettings"}]},{icon:Pe,name:"Pages",routes:[{path:"/404",name:"404"},{path:"/coming-soon",name:"Coming Soon"}]}],Sr="data:image/svg+xml,%3csvg%20width='87'%20height='25'%20viewBox='0%200%2087%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M26.2173%2024H29.1612C30.8267%2024%2031.6435%2023.1513%2031.6435%2022.0185C31.6435%2020.9176%2030.8622%2020.2713%2030.0881%2020.2322V20.1612C30.7983%2019.9943%2031.3594%2019.4972%2031.3594%2018.6129C31.3594%2017.5298%2030.5781%2016.7273%2029.0014%2016.7273H26.2173V24ZM27.5348%2022.8991V20.7685H28.9872C29.8004%2020.7685%2030.3047%2021.2656%2030.3047%2021.9155C30.3047%2022.4943%2029.907%2022.8991%2028.9517%2022.8991H27.5348ZM27.5348%2019.8203V17.8139H28.8665C29.6406%2017.8139%2030.0419%2018.2223%2030.0419%2018.7834C30.0419%2019.4226%2029.5234%2019.8203%2028.8381%2019.8203H27.5348ZM40.4235%2024L41.0237%2022.2067H43.7581L44.3618%2024H45.768L43.2041%2016.7273H41.5777L39.0173%2024H40.4235ZM41.3788%2021.1484L42.3625%2018.2188H42.4193L43.4029%2021.1484H41.3788ZM53.291%2024H58.6745V22.8956H54.9281L58.6532%2017.5476V16.7273H53.2697V17.8317H57.0197L53.291%2023.1797V24ZM67.5833%2024L68.1835%2022.2067H70.9178L71.5215%2024H72.9278L70.3638%2016.7273H68.7374L66.1771%2024H67.5833ZM68.5386%2021.1484L69.5222%2018.2188H69.579L70.5627%2021.1484H68.5386ZM80.5857%2024H81.9032V21.3366H83.2455L84.673%2024H86.1432L84.5701%2021.1165C85.4259%2020.772%2085.8769%2020.0405%2085.8769%2019.0533C85.8769%2017.6648%2084.982%2016.7273%2083.313%2016.7273H80.5857V24ZM81.9032%2020.2429V17.8281H83.1105C84.0942%2017.8281%2084.5346%2018.2791%2084.5346%2019.0533C84.5346%2019.8274%2084.0942%2020.2429%2083.1176%2020.2429H81.9032Z'%20fill='%23334155'/%3e%3cpath%20d='M26.3807%2014H29.1932V10.8466L30.3182%209.42614L33.2159%2014H36.5795L32.3807%207.55682L36.5057%202.36364H33.1932L29.3466%207.28409H29.1932V2.36364H26.3807V14ZM40.1321%2014L40.8991%2011.5568H44.9162L45.6832%2014H48.706L44.7798%202.36364H41.0355L37.1094%2014H40.1321ZM41.5696%209.42045L42.8594%205.29545H42.9503L44.2457%209.42045H41.5696ZM59.9744%206.57955C59.6903%203.79545%2057.6165%202.20455%2054.804%202.20455C51.7017%202.20455%2049.2869%204.33523%2049.2869%208.18182C49.2869%2012.0114%2051.6449%2014.1591%2054.804%2014.1591C57.9972%2014.1591%2059.7642%2011.9886%2059.9744%209.92614L57.1335%209.90909C56.9347%2011.0284%2056.0938%2011.6932%2054.8608%2011.6932C53.2017%2011.6932%2052.1562%2010.4943%2052.1562%208.18182C52.1562%205.96023%2053.179%204.67045%2054.8778%204.67045C56.1562%204.67045%2056.9915%205.41477%2057.1335%206.57955H59.9744ZM61.5838%2014H64.3963V9.32386H68.9815V14H71.7884V2.36364H68.9815V7.03409H64.3963V2.36364H61.5838V14ZM76.0696%2014L76.8366%2011.5568H80.8537L81.6207%2014H84.6435L80.7173%202.36364H76.973L73.0469%2014H76.0696ZM77.5071%209.42045L78.7969%205.29545H78.8878L80.1832%209.42045H77.5071Z'%20fill='%23334155'/%3e%3cg%20clip-path='url(%23clip0)'%3e%3cpath%20d='M15.9108%208.23217V6.78574C15.9108%203.59545%2013.3153%201%2010.125%201C6.93475%201%204.33931%203.59545%204.33931%206.78574V8.23217H0V20.5269C0%2022.524%201.61897%2024.143%203.61609%2024.143H16.634C18.6311%2024.143%2020.2501%2022.524%2020.2501%2020.5269V8.23217H15.9108ZM7.23218%206.78574C7.23218%205.19059%208.5299%203.89287%2010.125%203.89287C11.7202%203.89287%2013.0179%205.19059%2013.0179%206.78574V8.23217H7.23218V6.78574ZM14.4644%2012.2099C13.8652%2012.2099%2013.3795%2011.7242%2013.3795%2011.125C13.3795%2010.5259%2013.8652%2010.0402%2014.4644%2010.0402C15.0635%2010.0402%2015.5492%2010.5259%2015.5492%2011.125C15.5492%2011.7242%2015.0635%2012.2099%2014.4644%2012.2099ZM5.78574%2012.2099C5.1866%2012.2099%204.70091%2011.7242%204.70091%2011.125C4.70091%2010.5259%205.1866%2010.0402%205.78574%2010.0402C6.38488%2010.0402%206.87057%2010.5259%206.87057%2011.125C6.87057%2011.7242%206.38488%2012.2099%205.78574%2012.2099Z'%20fill='%2310B981'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0'%3e%3crect%20width='20.2501'%20height='23.143'%20fill='white'%20transform='translate(0%201)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",Lr="data:image/svg+xml,%3csvg%20width='87'%20height='25'%20viewBox='0%200%2087%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M26.2173%2024H29.1612C30.8267%2024%2031.6435%2023.1513%2031.6435%2022.0185C31.6435%2020.9176%2030.8622%2020.2713%2030.0881%2020.2322V20.1612C30.7983%2019.9943%2031.3594%2019.4972%2031.3594%2018.6129C31.3594%2017.5298%2030.5781%2016.7273%2029.0014%2016.7273H26.2173V24ZM27.5348%2022.8991V20.7685H28.9872C29.8004%2020.7685%2030.3047%2021.2656%2030.3047%2021.9155C30.3047%2022.4943%2029.907%2022.8991%2028.9517%2022.8991H27.5348ZM27.5348%2019.8203V17.8139H28.8665C29.6406%2017.8139%2030.0419%2018.2223%2030.0419%2018.7834C30.0419%2019.4226%2029.5234%2019.8203%2028.8381%2019.8203H27.5348ZM40.4235%2024L41.0237%2022.2067H43.7581L44.3618%2024H45.768L43.2041%2016.7273H41.5777L39.0173%2024H40.4235ZM41.3788%2021.1484L42.3625%2018.2188H42.4193L43.4029%2021.1484H41.3788ZM53.291%2024H58.6745V22.8956H54.9281L58.6532%2017.5476V16.7273H53.2697V17.8317H57.0197L53.291%2023.1797V24ZM67.5833%2024L68.1835%2022.2067H70.9178L71.5215%2024H72.9278L70.3638%2016.7273H68.7374L66.1771%2024H67.5833ZM68.5386%2021.1484L69.5222%2018.2188H69.579L70.5627%2021.1484H68.5386ZM80.5857%2024H81.9032V21.3366H83.2455L84.673%2024H86.1432L84.5701%2021.1165C85.4259%2020.772%2085.8769%2020.0405%2085.8769%2019.0533C85.8769%2017.6648%2084.982%2016.7273%2083.313%2016.7273H80.5857V24ZM81.9032%2020.2429V17.8281H83.1105C84.0942%2017.8281%2084.5346%2018.2791%2084.5346%2019.0533C84.5346%2019.8274%2084.0942%2020.2429%2083.1176%2020.2429H81.9032Z'%20fill='%23575757'/%3e%3cpath%20d='M26.3807%2014H29.1932V10.8466L30.3182%209.42614L33.2159%2014H36.5795L32.3807%207.55682L36.5057%202.36364H33.1932L29.3466%207.28409H29.1932V2.36364H26.3807V14ZM40.1321%2014L40.8991%2011.5568H44.9162L45.6832%2014H48.706L44.7798%202.36364H41.0355L37.1094%2014H40.1321ZM41.5696%209.42045L42.8594%205.29545H42.9503L44.2457%209.42045H41.5696ZM59.9744%206.57955C59.6903%203.79545%2057.6165%202.20455%2054.804%202.20455C51.7017%202.20455%2049.2869%204.33523%2049.2869%208.18182C49.2869%2012.0114%2051.6449%2014.1591%2054.804%2014.1591C57.9972%2014.1591%2059.7642%2011.9886%2059.9744%209.92614L57.1335%209.90909C56.9347%2011.0284%2056.0938%2011.6932%2054.8608%2011.6932C53.2017%2011.6932%2052.1562%2010.4943%2052.1562%208.18182C52.1562%205.96023%2053.179%204.67045%2054.8778%204.67045C56.1562%204.67045%2056.9915%205.41477%2057.1335%206.57955H59.9744ZM61.5838%2014H64.3963V9.32386H68.9815V14H71.7884V2.36364H68.9815V7.03409H64.3963V2.36364H61.5838V14ZM76.0696%2014L76.8366%2011.5568H80.8537L81.6207%2014H84.6435L80.7173%202.36364H76.973L73.0469%2014H76.0696ZM77.5071%209.42045L78.7969%205.29545H78.8878L80.1832%209.42045H77.5071Z'%20fill='%23575757'/%3e%3cg%20clip-path='url(%23clip0)'%3e%3cpath%20d='M15.9108%208.23217V6.78574C15.9108%203.59545%2013.3153%201%2010.125%201C6.93475%201%204.33931%203.59545%204.33931%206.78574V8.23217H0V20.5269C0%2022.524%201.61897%2024.143%203.61609%2024.143H16.634C18.6311%2024.143%2020.2501%2022.524%2020.2501%2020.5269V8.23217H15.9108ZM7.23218%206.78574C7.23218%205.19059%208.5299%203.89287%2010.125%203.89287C11.7202%203.89287%2013.0179%205.19059%2013.0179%206.78574V8.23217H7.23218V6.78574ZM14.4644%2012.2099C13.8652%2012.2099%2013.3795%2011.7242%2013.3795%2011.125C13.3795%2010.5259%2013.8652%2010.0402%2014.4644%2010.0402C15.0635%2010.0402%2015.5492%2010.5259%2015.5492%2011.125C15.5492%2011.7242%2015.0635%2012.2099%2014.4644%2012.2099ZM5.78574%2012.2099C5.1866%2012.2099%204.70091%2011.7242%204.70091%2011.125C4.70091%2010.5259%205.1866%2010.0402%205.78574%2010.0402C6.38488%2010.0402%206.87057%2010.5259%206.87057%2011.125C6.87057%2011.7242%206.38488%2012.2099%205.78574%2012.2099Z'%20fill='%23575757'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0'%3e%3crect%20width='20.2501'%20height='23.143'%20fill='white'%20transform='translate(0%201)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",Tr=({route:e})=>{const{t:s}=Ee(),[u,d]=g.useState(!1);return n.jsx(n.Fragment,{children:n.jsxs("li",{className:"relative px-6 py-3",children:[n.jsx("button",{className:"inline-flex items-center justify-between focus:outline-none w-full text-sm font-semibold transition-colors duration-150 hover:text-emerald-600 dark:hover:text-gray-200",onClick:()=>d(!u),"aria-haspopup":"true",children:n.jsxs("span",{className:"inline-flex items-center",children:[n.jsx(e.icon,{className:"w-5 h-5","aria-hidden":"true"}),n.jsx("span",{className:"ml-4 mt-1",children:s(`${e.name}`)}),n.jsx("span",{className:"pl-4 mt-1",children:u?n.jsx(kr,{}):n.jsx(wr,{})})]})}),u&&n.jsx("ul",{className:"p-2  overflow-hidden text-sm font-medium text-gray-500 rounded-md dark:text-gray-400 dark:bg-gray-900","aria-label":"submenu",children:e.routes.map((h,k)=>n.jsx("li",{children:h!=null&&h.outside?n.jsxs("a",{href:"http://localhost:3000",target:"_blank",className:"flex items-center font-serif py-1 text-sm text-gray-600 hover:text-emerald-600 cursor-pointer",rel:"noreferrer",children:[n.jsx(pe,{path:h.path,exact:h.exact,children:n.jsx("span",{className:"absolute inset-y-0 left-0 w-1 bg-emerald-500 rounded-tr-lg rounded-br-lg","aria-hidden":"true"})}),n.jsx("span",{className:"text-xs text-gray-500 pr-1",children:n.jsx(Be,{})}),n.jsx("span",{className:"text-gray-500 hover:text-emerald-600 dark:hover:text-gray-200",children:s(`${h.name}`)})]}):n.jsxs(Ze,{to:h.path,className:"flex items-center font-serif py-1 text-sm text-gray-600 hover:text-emerald-600 cursor-pointer",rel:"noreferrer",children:[n.jsx(pe,{path:h.path,exact:e.exact,children:n.jsx("span",{className:"absolute inset-y-0 left-0 w-1 bg-emerald-600 rounded-tr-lg rounded-br-lg","aria-hidden":"true"})}),n.jsx("span",{className:"text-xs text-gray-500 pr-1",children:n.jsx(Be,{})}),n.jsx("span",{className:"text-gray-500 hover:text-emerald-600 dark:hover:text-gray-200",children:s(`${h.name}`)})]})},k+1))})]},e.name)})},st=()=>{const{t:e}=Ee(),{mode:s}=g.useContext(X.WindmillContext),{dispatch:u}=g.useContext(Ve),{accessList:d}=qe(),h=()=>{u({type:"USER_LOGOUT"}),De.remove("adminInfo")},k=Hr.map(f=>{var $;if(f.routes){const v=f.routes.filter(V=>{const L=V.path.split("?")[0].split("/")[1];return d.includes(L)});return v.length>0?{...f,routes:v}:null}const H=($=f.path)==null?void 0:$.split("?")[0].split("/")[1];return H&&d.includes(H)?f:null}).filter(Boolean);return n.jsxs("div",{className:"py-4 text-gray-500 dark:text-gray-400",children:[n.jsx("a",{className:" text-gray-900 dark:text-gray-200",href:"/dashboard",children:s==="dark"?n.jsx("img",{src:Lr,alt:"gloopi",width:"135",className:"pl-6"}):n.jsx("img",{src:Sr,alt:"gloopi",width:"135",className:"pl-6"})}),n.jsx("ul",{className:"mt-8",children:k==null?void 0:k.map(f=>f.routes?n.jsx(Tr,{route:f},f.name):n.jsx("li",{className:"relative",children:n.jsxs(Ze,{exact:!0,to:f.path,target:`${f!=null&&f.outside?"_blank":"_self"}`,className:"px-6 py-4 inline-flex items-center w-full text-sm font-semibold transition-colors duration-150 hover:text-emerald-700 dark:hover:text-gray-200",activeStyle:{color:"#0d9e6d"},rel:"noreferrer",children:[n.jsx(pe,{path:f.path,exact:f.exact,children:n.jsx("span",{className:"absolute inset-y-0 left-0 w-1 bg-emerald-500 rounded-tr-lg rounded-br-lg","aria-hidden":"true"})}),n.jsx(f.icon,{className:"w-5 h-5","aria-hidden":"true"}),n.jsx("span",{className:"ml-4",children:e(`${f.name}`)})]})},f.name))}),n.jsx("span",{className:"lg:fixed bottom-0 px-6 py-6 w-64 mx-auto relative mt-3 block",children:n.jsx(X.Button,{onClick:h,size:"large",className:"w-full",children:n.jsxs("span",{className:"flex items-center",children:[n.jsx(br,{className:"mr-3 text-lg"}),n.jsx("span",{className:"text-sm",children:e("LogOut")})]})})})]})},_r=()=>n.jsx("aside",{className:"z-30 flex-shrink-0 hidden shadow-sm w-64 overflow-y-auto bg-white dark:bg-gray-800 lg:block",children:n.jsx(st,{})});function Mr(){const{isSidebarOpen:e,closeSidebar:s}=g.useContext(be);return n.jsx(X.Transition,{show:e,children:n.jsxs(n.Fragment,{children:[n.jsx(X.Transition,{enter:"transition ease-in-out duration-150",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition ease-in-out duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:n.jsx(X.Backdrop,{onClick:s})}),n.jsx(X.Transition,{enter:"transition ease-in-out duration-150",enterFrom:"opacity-0 transform -translate-x-20",enterTo:"opacity-100",leave:"transition ease-in-out duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0 transform -translate-x-20",children:n.jsx("aside",{className:"fixed inset-y-0 z-50 flex-shrink-0 w-64 mt-16 overflow-y-auto bg-white dark:bg-gray-800 lg:hidden",children:n.jsx(st,{})})})]})})}const Cr=()=>n.jsxs(n.Fragment,{children:[n.jsx(_r,{}),n.jsx(Mr,{})]}),jr=g.lazy(()=>M(()=>import("./Dashboard-D9esJu_v.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]))),Dr=g.lazy(()=>M(()=>import("./Attributes-RuJSFj-r.js"),__vite__mapDeps([22,1,15,23,24,25,26,27,3,4,7,8,6,5,28,29,30,11,31,32,33,34,35,36,37,38,10,12,19,20,2,21]))),Vr=g.lazy(()=>M(()=>import("./ChildAttributes-DWkvJFdI.js"),__vite__mapDeps([39,1,23,24,25,28,29,3,4,7,8,6,30,11,31,32,33,5,26,27,15,34,35,36,40,19,20,2,21,10]))),Nr=g.lazy(()=>M(()=>import("./Products-COSfDiv3.js"),__vite__mapDeps([41,1,11,23,24,25,37,38,10,3,4,19,20,42,30,6,43,31,34,32,44,36,45,46,15,29,28,7,8,33,5,26,27,12,21]))),zr=g.lazy(()=>M(()=>import("./ProductDetails-CnmQ5SFK.js"),__vite__mapDeps([47,1,11,2,3,4,5,6,7,8,42,30,23,24,25,43,31,34,32,44,36,45,46,15,29,40,20,10]))),Er=g.lazy(()=>M(()=>import("./Category-CV45qYOK.js"),__vite__mapDeps([48,1,11,3,4,23,24,25,2,5,6,7,8,26,27,15,28,29,30,31,32,33,20,49,34,45,46,36,37,38,10,12,19,21]))),Or=g.lazy(()=>M(()=>import("./ChildCategory-BfvSylE4.js"),__vite__mapDeps([50,1,49,28,29,3,4,7,8,6,30,23,24,25,11,31,32,33,5,26,27,15,34,45,46,36,40,19,20,2,21,10]))),$r=g.lazy(()=>M(()=>import("./Staff-D3v-HZkc.js"),__vite__mapDeps([51,1,11,2,3,4,5,6,7,8,23,24,25,43,31,34,52,27,36,53,32,45,46,12,14,15,26,29,19,20,21,10]))),Wr=g.lazy(()=>M(()=>import("./Customers-mgZi8_es.js"),__vite__mapDeps([54,1,37,38,10,25,3,4,23,24,26,27,7,8,6,5,15,31,34,32,12,19,20,11,2,21]))),Fr=g.lazy(()=>M(()=>import("./CustomerOrder-jDWVQlwW.js"),__vite__mapDeps([55,1,11,17,2,3,4,5,6,7,8,20,40,14,18,10]))),Ar=g.lazy(()=>M(()=>import("./Orders-gqilpHO6.js"),__vite__mapDeps([56,1,38,6,11,2,3,4,5,7,8,17,19,20,13,14,15,16,18,12,25,21,10]))),Pr=g.lazy(()=>M(()=>import("./OrderInvoice-DkyEodCO.js"),__vite__mapDeps([57,1,16,4,46,58,11,6,14,17,40,20,25,5,10]))),Br=g.lazy(()=>M(()=>import("./Coupons-B56ML6Lh.js"),__vite__mapDeps([59,1,7,11,23,24,25,2,3,4,5,6,8,20,26,27,15,28,29,30,31,32,33,34,44,45,46,36,12,19,37,38,10,21]))),Ir=g.lazy(()=>M(()=>import("./404-DrvXymuP.js"),__vite__mapDeps([60,1,10]))),Rr=g.lazy(()=>M(()=>import("./ComingSoon-Rh0tuLLi.js"),__vite__mapDeps([61,1]))),Zr=g.lazy(()=>M(()=>import("./EditProfile-BrjieRfC.js"),__vite__mapDeps([62,1,52,31,27,6,36,58,32,45,46,4,34,53,21,10]))),Ur=g.lazy(()=>M(()=>import("./Languages-Bebrg4Dd.js"),__vite__mapDeps([63,1,28,29,3,4,7,8,6,30,23,24,25,11,31,32,33,5,26,27,15,34,12,19,2,20,21,10]))),Yr=g.lazy(()=>M(()=>import("./Currencies-CSRYMsEt.js"),__vite__mapDeps([64,1,28,29,3,4,7,8,6,30,23,24,25,11,31,32,33,5,34,26,27,15,20,2,12,19,21,10]))),qr=g.lazy(()=>M(()=>import("./Setting-7vT99jj8.js"),__vite__mapDeps([65,1,31,20,5,6,34,66,21,33,29,67,25,11,8,10]))),Gr=g.lazy(()=>M(()=>import("./StoreHome-BIIraSI9.js"),__vite__mapDeps([68,1,31,25,66,33,29,45,46,6,4,43,58,5,7,20,24,21,10]))),Xr=g.lazy(()=>M(()=>import("./StoreSetting-qksk1b_f.js"),__vite__mapDeps([69,1,31,20,66,33,29,5,6,21,67,25,10]))),Kr=g.lazy(()=>M(()=>import("./Notifications-CJRyvcfq.js"),__vite__mapDeps([70,1,20,6,10]))),Jr=[{path:"/dashboard",component:jr},{path:"/products",component:Nr},{path:"/attributes",component:Dr},{path:"/attributes/:id",component:Vr},{path:"/product/:id",component:zr},{path:"/categories",component:Er},{path:"/languages",component:Ur},{path:"/currencies",component:Yr},{path:"/categories/:id",component:Or},{path:"/customers",component:Wr},{path:"/customer-order/:id",component:Fr},{path:"/our-staff",component:$r},{path:"/orders",component:Ar},{path:"/order/:id",component:Pr},{path:"/coupons",component:Br},{path:"/settings",component:qr},{path:"/store/customization",component:Gr},{path:"/store/store-settings",component:Xr},{path:"/404",component:Ir},{path:"/coming-soon",component:Rr},{path:"/edit-profile",component:Zr},{path:"/notifications",component:Kr}],bn=[{label:"Dashboard",value:"dashboard"},{label:"Products",value:"products"},{label:"Categories",value:"categories"},{label:"Attributes",value:"attributes"},{label:"Coupons",value:"coupons"},{label:"Customers",value:"customers"},{label:"Orders",value:"orders"},{label:"Staff",value:"our-staff"},{label:"Settings",value:"settings"},{label:"Languages",value:"languages"},{label:"Currencies",value:"currencies"},{label:"ViewStore",value:"store"},{label:"StoreCustomization",value:"customization"},{label:"StoreSettings",value:"store-settings"},{label:"Product Details",value:"product"},{label:"Order Invoice",value:"order"},{label:"Edit Profile",value:"edit-profile"},{label:"Customer Order",value:"customer-order"},{label:"Notification",value:"notifications"},{label:"Coming Soon",value:"coming-soon"}],Qr=g.lazy(()=>M(()=>import("./404-DrvXymuP.js"),__vite__mapDeps([60,1,10]))),en=()=>{const{isSidebarOpen:e,closeSidebar:s,navBar:u}=g.useContext(be);let d=Ne();const h=navigator.onLine;return g.useEffect(()=>{s()},[d]),n.jsxs(n.Fragment,{children:[!h&&n.jsxs("div",{className:"flex justify-center bg-red-600 text-white",children:["You are in offline mode!"," "]}),n.jsxs("div",{className:`flex h-screen bg-gray-50 dark:bg-gray-900 ${e&&"overflow-hidden"}`,children:[u&&n.jsx(Cr,{}),n.jsxs("div",{className:"flex flex-col flex-1 w-full",children:[n.jsx(xr,{}),n.jsx(bt,{children:n.jsx(g.Suspense,{fallback:n.jsx(ft,{}),children:n.jsxs(gt,{children:[Jr.map((k,f)=>k.component?n.jsx(pe,{exact:!0,path:`${k.path}`,render:H=>n.jsx(k.component,{...H})},f):null),n.jsx(pt,{exact:!0,from:"/",to:"/dashboard"}),n.jsx(pe,{component:Qr})]})})})]})]})]})},Hn=Object.freeze(Object.defineProperty({__proto__:null,default:en},Symbol.toStringTag,{value:"Module"}));export{gr as A,fn as F,Be as I,Hn as L,he as N,nn as S,hn as a,gn as b,an as c,je as d,ln as e,dr as f,un as g,on as h,xn as i,yn as j,qe as k,Oe as l,kn as m,kt as n,wn as o,dn as p,Lr as q,bn as r,Sr as s,cn as t,it as u,mn as v,sn as w,pn as x,vn as y,at as z};
