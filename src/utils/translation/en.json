{"DashboardOverview": "Dashboard Overview", "TodayOrder": "Today Orders", "YesterdayOrder": "Yesterday Orders", "ThisMonth": "This Month", "AllTimeSales": "All-Time Sales", "TotalOrder": "Total Orders", "OrderPending": "Orders Pending", "OrderProcessing": "Orders Processing", "OrderDelivered": "Orders Delivered", "POSOrders": "POS Orders", "OnlineOrders": "Online Orders", "WeeklySales": "Weekly Sales", "BestSellingProducts": "Best Selling Products", "Cash": "Cash", "Card": "Card", "Credit": "Credit", "ConversionsThisYear": "Conversions This Year", "ConversionsThisWeek": "Conversions This Week", "FreshVegetable": "Fresh Vegetable", "TopRevenueProduct": "Top Revenue Product", "Sales": "Sales", "RecentOrder": "Recent Order", "OrderTime": "Order Time", "DeliveryAddress": "Delivery Address", "Phone": "Phone", "Orderamount": "Order amount", "Dashboard": "Dashboard", "Setting": "Settings", "Catalog": "Catalog", "Products": "Products", "SideMenuCategory": "Category", "Attributes": "Attributes", "Attribute": "Attribute", "Coupons": "Coupons", "OurStaff": "Our Staff", "Customers": "Customers", "International": "International", "Localization": "Localization", "Locations": "Locations", "Taxes": "Taxes", "Translations": "Translations", "OnlineStore": "Online Store", "Shop": "View your Store ", "StoreHome": "Shop Home", "GlobalSettings": "Global Settings", "UseCompositionProduct": "Use Composition Product", "CashiersCanOverrideInuseItems": "Cashiers can override Inuse items", "EditProfile": "Edit Profile", "LogOut": "Log Out", "StockHandlingMethod": "Stock Handling Method", "Pages": "Pages", "ProductsPage": "Products", "Category": "Category", "Price": "Price", "LowtoHigh": "Low to High", "HightoLow": "High to Low", "AddProduct": "Add Product", "BulkAction": "Bulk Action", "DropCSVfile": "Drop CSV file", "Upload": "Upload", "Download": "Download", "SearchProduct": "Search by product name", "Published": "Published", "Unpublished": "Unpublished", "StatusSelling": "Status - Selling", "StatusStock": " Status - Out of Stock", "DateAddedAsc": "Date Added (Asc)", "DateAddedDesc": "Date Added (Desc)", "DateUpdatedAsc": "Date Updated (Asc)", "DateUpdatedDesc": "Date Updated (Desc)", "SKUTbl": "SKU", "ProductNameTbl": "PRODUCT NAME", "CategoryTbl": "CATEGORY", "PriceTbl": "price", "StockTbl": "STOCK", "StatusTbl": "STATUS", "VIEW": "VIEW", "DiscountTbl": "DISCOUNT", "DetailsTbl": "View", "PublishedTbl": "PUBLISHED", "ActionsTbl": "ACTIONS", "Off": "Off", "Selling": "Selling", "SoldOut": "Sold Out", "Delete": "Delete", "Edit": "Edit", "TypeTbl": "Type", "SelectedCountries": "Selected Countries", "ThisProductHaveVariants": "Does this product have variants?", "UpdateProduct": "Update Products", "UpdateProductDescriptionBulk": "Apply changes to the selected products from the list.", "UpdateProductDescription": "Update products info, combinations and extras.", "DrawerAddProduct": "Add Product", "AddProductDescription": "Add your product and necessary information from here", "ProductImage": "Product Images", "DragYourImage": "Drag your images here", "imageFormat": "(Only *.jpeg, *.webp and *.png images will be accepted)", "ProductID": "Product ID", "ProductSKU": "Product SKU", "ProductBarcode": "Product Barcode", "ProductTitleName": "Product Title/Name", "ProductSlug": "Product Slug", "ProductDescription": "Product Description", "ParentCategory": "Parent Category", "ChildCategory": "Child Category", "ProductType": "Product Type", "Unit": "Unit (kg/pc/lb/ml/g...etc)", "ShowingIn": "Showing In", "Store": "Store", "ProductQuantity": "Product Quantity", "ProductPrice": "Product Price", "SalePrice": "Sale Price", "ProductTag": "Product Tags", "ProductTagPlaseholder": "Product Tag (Write then press enter to add new tag )", "AddVariantBtn": "<PERSON><PERSON>", "CancelBtn": "Cancel", "UpdateBtn": "Update", "AddBtn": "Add", "Selectparentcategory": "Select parent category", "Selectchildcategory": "Select child category", "ProductPriceWithTax": "Product Price (tax incl.)", "ProductPriceWithoutTax": "Product Price", "ProductPriceTaxExcl": "Product Price (tax excl.)", "PROFIT": "PROFIT", "EXCLVAT": "EXCL VAT", "PRICEINCLVAT": "PRICE INCL VAT", "TaxRate": "Tax Rate", "TaxName": "Tax Name", "AddTaxesBtn": "Add Tax", "DefaultCategory": "Default Category", "MasterProduct": "Master Product", "SellIndividualUnits": "Sell Individual Units", "Taxexcl": "tax excl.", "Taxincl": "tax incl.", "CostPrice": "Cost Price", "AllowOutStockOrder": "Allow Out Of Stock Order", "AllowRelatedProductsAndCategories": "Allow Related Products And Categories", "AllowNotesOnCheckout": "Allow Notes On Checkout", "ProductMeasurements": "Product Measurements", "DeleteCombinations": "Delete Combinations", "Apply": "Apply", "SelectedCurrencies": "Selected Currencies", "SelectedLanguages": "Selected Languages", "ProductDetails": "Product Details", "ThisProductShowing": "This product Showing", "ThisProductHidden": "This product Hidden", "StockOut": "Stock Out", "InStock": "In Stock", "EditProduct": "Edit Product", "ProductVariantList": "Product Variant List", "Combination": "Combination", "Sku": "S<PERSON>", "Barcode": "Barcode", "PriceTxExcl": "Price(tx excl.)", "PriceTxIncl": "Price(tx incl.)", "TotalVol": "Total Vol.", "QuantityTbl": "Quantity", "Measurement": "Measurement", "Change": "Change", "quart": "quart", "pint": "pint", "ounce": "ounce", "pound": "pound", "foot": "foot", "inch": "inch", "SelectMeasurement": "Select Measurement", "DeleteModalH2": "Are You Sure! Want to Delete", "DeleteModalPtag": "Do you really want to delete these records? You can't view this in your list anymore if you delete!", "modalKeepBtn": "No, Keep It", "modalDeletBtn": "Yes, Delete It", "Processing": "Processing", "OrderInvoiceDownload": "Order Invoice & Download", "PrintReceipt": "Print Receipt", "DoReStockProducts": "Do you want to Re stock the products?", "AreSureWantDeleteItem": " Are you sure you want to delete this Item?", "YesDeleteIt": "Yes, Delete It", "SelectedAttributes": "Selected Attributes", "CategoryPageTitle": "Category", "CategorySearchplaceholder": "Search by category type", "AddCategoryBtn": "Add Category", "AddCategory": "Add Category", "CatTbName": "Name", "CatTbDescription": "Description", "SearchCategory": "Search by Category name", "ParentsOnly": "Parents Only", "All": " All", "Categoryformenu": "Category for menu", "SelectCategory": "Select Category", "catIdTbl": "ID", "catIconTbl": "Icon", "catParentTbl": "PARENT", "catChildrenTbl": "CHILDREN", "catTypeTbl": "TYPE", "catPublishedTbl": "PUBLISHED", "catActionsTbl": "ACTIONS", "VALUES": "VALUES", "UpdateCategory": "Update Category", "UpdateCategoryDescription": "Updated your Product category and necessary information from here", "AddCategoryTitle": "Add Category", "AddCategoryDescription": "Add your Product category and necessary information from here", "CategoryIcon": "Category Image", "ChildCategoryPlaceholder": "Child category  (Write then press enter to add new child category )", "ParentCategoryPlaceholder": "Category title", "CatName": "Name", "CatDescription": "Description", "CouponspageTitle": "Coupon", "SelectedCoupon": "Selected Coupon", "AttributeTitle": "Attributes", "SearchAttributePlaceholder": "Search by attribute name", "CouponsAddAttributeBtn": "Add Attribute", "AddCouponsBtn": "Add Coupon", "CouponBannerImage": "Coupon Banner Image", "SearchCoupon": "Search by coupon code/name", "CoupTblId": "Id", "CoupTblStartDate": "START DATE", "CoupTblEndDate": "END DATE", "CoupTblCampaignsName": "CAMPAIGN NAME", "CoupTblCode": "CODE", "CoupTblPercentage": "PERCENTAGE", "CoupTblProductType": "PRODUCT TYPE", "CoupTblStatus": "STATUS", "CoupTblActions": "ACTIONS", "UpdateCoupon": "Update Coupon", "UpdateCouponDescription": "Updated your coupon and necessary information from here", "AddCoupon": "Add Coupon", "AddCouponDescription": "Add your coupon and necessary information from here", "CampaignName": " Campaign Name", "CampaignCode": "Campaign Code", "CouponValidityTime": "Coupon Validity Time", "DiscountPercentage": "Discount Percentage", "MinimumAmount": "Minimum Amount", "MinimumAmountPlasholder": "Minimum amount required", "DiscountType": "Discount Type", "Values": "Values", "Id": "ID", "AName": "NAME", "ADisplayName": "DISPLAY NAME", "AOption": "OPTION", "APublished": "PUBLISHED", "Avalues": "VALUES", "AAction": "ACTION", "UpdateAttribute": "Update Attribute Value", "UpdateAttributeDesc": "Updated your attribute values and necessary information from here", "AddAttribute": "Add Attribute Value", "AddAttributeDesc": "Add your attribute values and necessary information from here", "DrawerAttributeTitle": "Attribute Title", "DisplayName": "Display Name", "DrawerOptions": "Options", "DrawerSelecttype": "Select type", "Variants": "Variants", "Dropdown": "Dropdown", "Radio": "Radio", "AddExtra": "Add Extra", "SearchExtraName": "Search by Extra name", "UpdateExtraValue": "Update Extra Value", "UpdateExtraDescription": "Update your Extra values and necessary information from here", "AddExtraValue": "Add Extra Value", "AddExtraDescription": "Add your Extra values and necessary information from here", "OrdersPageTitle": "Orders", "OrdersSearchPhone": "Search by phone", "OrderStatus": "Status", "PageOrderDelivered": "Delivered", "PageOrderPending": "Pending", "PageOrderProcessing": "Processing", "OrderCancel": "Cancel", "Orderlimits": "Order limits", "DaysOrders5": "Last 5 days orders", "DaysOrders7": "Last 7 days orders", "DaysOrders15": "Last 15 days orders", "DaysOrders30": "Last 30 days orders", "DownloadOrdersBtn": "Download All Orders", "ParkOrders": " Park Orders", "POSCompleted": "POS Completed", "SearchByInvoice": "Search By Invoice", "DeleteAllBarOrders": "Delete All Bar Orders", "DeleteAllKitchenOrders": " Delete All Kitchen Orders", "StartDate": "Start Date", "EndDate": "End Date", "CustomerOrderList": "Customer Order List", "CustomerOrderEmpty": "This Customer have no order Yet!", "CustomerOrderId": "Order ID", "CustomerOrderTime": "Time", "CustomerShippingAddress": "Shipping Address", "CustomerOrderMethod": "Method", "CustomerOrderStatus": "Status", "CustomerOrderAction": "Action", "ParkOrdersTitle": "Park Orders", "TableName": "Table Name", "SearchCustomerTableName": "Search Customer / Table Name", "InvoiceNo": "INVOICE NO", "TimeTbl": "ORDER TIME", "ShippingAddressTbl": "SHIPPING ADDRESS", "PhoneTbl": "PHONE", "MethodTbl": "METHOD", "AmountTbl": "AMOUNT", "UserTbl": "USER", "OderStatusTbl": "STATUS", "ActionTbl": "ACTION", "InvoiceTbl": "INVOICE", "ViewInvoice": "View Invoice", "CustomerName": "Customer Name", "OrderNumber": "Order No.", "SearchOrderNumberCustomer": "Search by Order number or customer", "InvoicePageTittle": "Invoice", "Order": "Order", "InvoiceStatus": "Status", "InvoiceDate": "DATE", "InvoiceTo": "INVOICE TO", "Sr": "SR.", "ProductName": "PRODUCT NAME", "Quantity": "QUANTITY", "ItemPrice": "ITEM PRICE", "Amount": "AMOUNT", "InvoicepaymentMethod": "PAYMENT METHOD", "ShippingCost": "SHIPPING COST", "InvoiceDicount": "DISCOUNT", "InvoiceTotalAmount": "TOTAL AMOUNT", "DownloadInvoice": "Download Rechnung  ", "PrintInvoice": "Print Invoice", "ShippingCostLower": "Shipping Cost", "VATTotal": "VAT Total", "GrossTotal": "Gross Total", "Date": "Date", "Item": "<PERSON><PERSON>", "QTY": "QTY", "ThankYouMsg": "Thank you for your order. Come Again...!", "NoofItems": "No of Items", "BillNo": "Bill No", "Discount": "DISCOUNT", "DiscountLower": "Discount", "TotalAmount": "TOTAL AMOUNT", "DownloadInvoiceBtn": "Download Invoice", "OrderHistory": "Order History", "ShippingMethod": "SHIPPING METHOD", "ShippingMethodLower": "Shipping Method", "OrderType": "Order type", "CategorySorting": "Category Sorting", "TableInfo": "Table info", "StatusChanged": "Status changed by", "Seller": "<PERSON><PERSON>", "User": "User", "Extras": "Extras", "Notes": "Notes", "SubTotal": "SubTotal", "VAT": "VAT", "Total": "Total", "Return": "Return", "Inuse": "Inuse", "InuseOrder": "InuseOrder", "Returns": "Returns", "SearchCodeName": "Search by code/name", "SelectClickItemsWantReturn": "Select/Click on the Items want to return", "CartEmpty": "Cart is empty", "NoItem": "No items added in your cart.", "ItemReturned": "<PERSON><PERSON> returned in this bill.", "ReloadAll": "Reload All", "DoWantReceiptReturnOrder": "Do you want to receipt for this return order?", "NoDontReturn": "No, Don t Return", "ReturnOrder": "Return Order", "TotalItems": "Total Items", "SubTotalWithoutVAT": "Sub Total Without VAT", "TotalCost": "Total Cost", "ThisOrderAlreadyHadReturnitems": "This Order Already had return items!", "StaffPageTitle": "All Staff", "StaffSearchBy": "Search by name/email/phone", "StaffRole": "Staff Role", "StaffRoleAdmin": "Admin", "StaffRoleCeo": "CEO", "SelectCashiers": "Cashier", "SelectSuperAdmin": "Super Admin", "StaffRoleManager": "Manager", "StaffRoleAccountant": "Accountant", "StaffRoleDriver": "Driver", "StaffRoleSecurity": "Security Guard", "StaffRoleDelivery": "Delivery Person", "AddStaff": "Add Staff", "StaffIdTbl": "ID", "StaffNameTbl": "Name", "StaffEmailTbl": "Email", "StaffContactTbl": "Contact", "StaffJoiningDateTbl": "Joining Date", "StaffRoleTbl": "Role", "StaffShopStatusTbl": "Shop Status", "StaffPosStatusTbl": "POS Status", "StaffActionsTbl": "ACTIONS", "UpdateStaff": "Update Staff", "UpdateStaffdescription": "Updated your staff necessary information from here", "AddStaffTitle": "Add Staff", "AddStaffdescription": "Add your staff necessary information from here", "StaffImage": "Staff Image", "StaffName": "Name", "StaffNamePlaceholder": "Staff Name", "StaffEmail": "Email", "StaffPassword": "Password", "StaffContactNumber": "Contact Number", "StaffJoiningDate": "Joining Date", "DrawerStaffRole": "Staff Role", "sorryProductNotFound": "Sorry, There are no products right now.", "CustomersPage": "Customers", "CustomersPageSearchPlaceholder": "Search by name/email/phone", "CustomersId": "Id", "CustomersJoiningDate": "Joining Date", "CustomersName": "Name", "CustomersEmail": "Email", "CustomersPhone": "Phone", "CustomersActions": "Actions", "CustomerOrderListPageTitle": "Customer Orders List", "NoOrder": "This Customer have no order Yet!", "OrderIDTbl": "Order ID", "OrderTimeTbl": "Time", "OrderShippingAddressTbl": "ShippingAddress", "OrderPhoneTbl": "Phone", "OrderMethodTbl": "Method", "OrderAmountTbl": "Amount", "OrderStatusTbl": "Status", "OrderActionsTbl": "Actions", "ViewOrder": "View Order", "LoginTitle": "<PERSON><PERSON>", "labelEmail": "Email", "labelPassword": "Password", "LoginBtn": "Log in", "ForgotPassword": "Forgot your password", "CreateAccount": "Create account", "updateProfile": " Update Profile", "ProfilePicture": "Profile Picture", "ProfileName": "Name", "ProfileEmail": "Email", "ProfileContactNumber": "Contact Number", "ProfileYourRole": "Your Role", "LoginWithGoogle": "Login With Google", "LoginWithFacebook": "Login With Facebook", "CreateAccountTitle": "Create account", "CreateAccountName": "Name", "CreateAccountCompanyName": "Company Name", "CreateAccountEmail": "Email", "CreateAccountPassword": "Password", "privacyPolicy": "privacy policy", "TermAndCondition": "Terms and Conditions", "Iagree": "I agree to the", "CreateAccountBtn": "Create Account", "AlreadyAccount": "Already have an account? <PERSON>gin", "Welcome": "Welcome to Kachabazar!", "Cookies": "Cookies", "License": "License", "Content": "Content Liability", "Disclaimer": "Disclaimer", "DisclaimerDes": "To the maximum extent permitted by applicable law, we exclude all representations, warranties and conditions relating to our website and the use of this website. Nothing in this disclaimer will:1. limit or exclude our or your liability for death or personal injury;2. limit or exclude our or your liability for fraud or fraudulent misrepresentation;3. limit any of our or your liabilities in any way that is not permitted under applicable law; or4. exclude any of our or your liabilities that may not be excluded under applicable law.The limitations and prohibitions of liability set in this Section and elsewhere in this disclaimer: (a) are subject to the preceding paragraph; and (b) govern all liabilities arising under the disclaimer, including liabilities arising in contract, in tort and for breach of statutory duty. As long as the website and the information and services on the website are provided free of charge, we will not be liable for any loss or damage of any nature.", "ContentDescription": "We shall not be hold responsible for any content that appears on your Website. You agree to protect and defend us against all claims that is rising on your Website. No link(s) should appear on any Website that may be interpreted as libelous, obscene or criminal, or which infringes, otherwise violates, or advocates the infringement or other violation of, any third party rights.Without prior approval and written permission, you may not create frames around our Webpages that alter in any way the visual presentation or appearance of our Website.", "LicenseDescription": "Unless otherwise stated, Ka<PERSON><PERSON><PERSON> and/or its licensors own the intellectual property rights for all material on Kacha<PERSON><PERSON>. All intellectual property rights are reserved. You may access this from Ka<PERSON><PERSON><PERSON> for your own personal use subjected to restrictions set in these terms and conditions.This Agreement shall begin on the date hereof. Our Terms and Conditions were created with the help of the Terms And Conditions Generator. You must not:1. Identifiers (e.g. name, mailing address, email address, phone number, credit/debit card number)2. Characteristics of protected classifications (e.g. gender, age)3. Commercial information (e.g. products or services purchased, purchase history)4. Internet or other electronic network activity (e.g. browse or search history)5. Geo location data (e.g. latitude or longitude)6. Audio, electronic, visual, or similar information (e.g. recording of Guest service calls)7. Inferences drawn from any of the above (e.g. preferences or characteristics)Parts of this website offer an opportunity for users to post and exchange opinions and information in certain areas of the website. Kachabazar does not filter, edit, publish or review Comments prior to their presence on the website. Comments do not reflect the views and opinions of <PERSON><PERSON><PERSON><PERSON>,its agents and/or affiliates. Comments reflect the views and opinions of the person who post their views and opinions. To the extent permitted by applicable laws, Ka<PERSON><PERSON><PERSON> shall not be liable for the Comments or for any liability, damages or expenses caused and/or suffered as a result of any use of and/or posting of and/or appearance of the Comments on this website.", "CookiesDescription": "We employ the use of cookies. By accessing Kachabazar, you agreed to use cookies in agreement with the Kachabazar's Privacy Policy. Most interactive websites use cookies to let us retrieve the user’s details for each visit. Cookies are used by our website to enable the functionality of certain areas to make it easier for people visiting our website. Some of our affiliate/advertising partners may also use cookies.", "WelcomeDescription": "These terms and conditions outline the rules and regulations for the use of Kachabazar's Website, located at https://dashter.com/. By accessing this website we assume you accept these terms and conditions. Do not continue to use Ka<PERSON><PERSON><PERSON> if you do not agree to take all of the terms and conditions stated on this page. The following terminology applies to these Terms and Conditions, Privacy Statement and Disclaimer Notice and all Agreements: 'Client', 'You' and 'Your' refers to you, the person log on this website and compliant to the Company’s terms and conditions. 'The Company', 'Ourselves', 'We', 'Our' and 'Us', refers to our Company. 'Party', 'Parties', or 'Us', refers to both the Client and ourselves. All terms refer to the offer, acceptance and consideration of payment necessary to undertake the process of our assistance to the Client in the most appropriate manner for the express purpose of meeting the Client’s needs in respect of provision of the Company’s stated services, in accordance with and subject to, prevailing law of Netherlands. Any use of the above terminology or other words in the singular, plural, capitalization and/or he/she or they, are taken as interchangeable and therefore as referring to same.", "ForgotpasswordTitle": "Forgot password", "RecoverpasswordBtn": "Recover password", "InvoiceNumberLength": "Invoice Number Length", "DefaultDateFormat": "Default Date Format", "PosSetting": "POS Setting", "StoreDetails": "Store Details", "ShopLogo": "Shop logo", "ShopName": "Shop name", "CompanyName": "Company name", "VATNumber": "VAT number", "ShopAddressLine1": "Shop address line 1", "ShopAddressLine2": "Shop address line 2", "ShopAddressLine3": "Shop address line 3", "ContactNumber1": "Contact number 1", "ContactNumber2": "Contact number 2", "Email": "Email", "WebSite": "Web site", "AnyInfoDisplayinSlip": "Additional info to display in receipt", "AnyInfoDisplayinSlip1": "Any additional info display in the bottom of the receipt", "Configuration": "Configuration", "ShowImages": "Show images", "DefaultPOSCustomer": "Default POS customer", "EmailInvoiceToAdmin": "Email Invoice To Admin(S)", "ReceiptSize": "Receipt size (width)", "PreviewReceipt": "Preview receipt", "Alloworderingofoutofstockproducts": "Allow ordering of out of stock products", "EnablePinBeforeAction": "Enable pin before any action", "Quantitypopupforextras": "Quantity popup for extras", "ShowPaymentOptions": "Show payment options", "DefaultPaymentMethod": "Default payment method", "AllowDiscounts": "Allow discounts", "PrintSlip": "Print slip", "EmailSlip": "Email Slip to the Customer", "OrderTypeCapText": "This will shown in POS product card", "DefaultTaxRate": "Default tax rate", "SaveBtn": "Save", "previous": "Previous", "Next": "Next", "PaymentMethods": "Payment methods", "Combined": "Combined", "NumberProductShowPOSHomePage": "Number Of Products to Show in POS Home Page", "ProductSorting": "Product sorting", "Billing": "Billing", "NumberOfCategoriestoShowinPOSHomePage": "Number Of Categories to Show in POS Home Page", "ApplyTheCostOfTheHighestDefinedRange": "Apply the cost Of the highest defined range", "AddNewRange": " Add new range", "ShowStockDetailsinProductPage": "Show stock details in product page", "ShowStockDetailsInStore": "Show stock details in store (global)", "ShowLowStockNotification": "Show low stock notification", "LowStockNotificationValue": "Low stock notification value", "Poshomepageview": "POS home page view", "Defaultposcategory": " Default POS category", "DefaultposcategorySmText": "Products in this category will be shown in the POS by default", "TableNavigation": "Table navigation", "sorryCategoryNotFound": "Sorry, There are no categories right now.", "ContactDetails": "Contact Details", "PostCode": "Post Code", "Theme": "Theme", "ICON": "ICON", "ThemeSmallText": "Selected theme will shown in POS", "EmailRecipients": "Email Recipients", "Languages": "Languages", "Currencies": "Currencies", "Geolocation": "Geolocation", "DefaultLanguage": "Default language", "SetLanguageFromBrowser": "Set language from browser", "DefaultCountry": "Default country", "DefaultCurrency": "Default currency", "TimeZone": "Default time zone", "LocalUnits": "Local Units", "WeightUnit": "Weight Unit", "DistanceUnit": "Distance Unit", "VolumeUnit": "Volume Unit", "DimensionUnit": "Dimension Unit", "globalDescription": "You already have child products in the system. Turning this feature off will unpublish them. Would you like to continue ?", "UnitChangeBoxTitle": "Are you sure want to change this?", "UnitChangeBoxDiscription": "Changing unit system could cause misbehavior of existing products and stocks. Are you sure you want to do this ?", "YesChangeItBtn": "Yes, Change It", "InternationalCustom": "International / Custom", "MetricSystemImperialSystem": "Metric System / Imperial System", "Custom": "Custom", "Metric": "Metric", "Imperial": "Imperial", "Length": "Length", "SelectLength": "Select Length", "Centimeter": "Centimeter (cm)", "Meter": "Meter (m)", "Kilometer": "Kilometer (Km)", "Weight": "Weight", "SelectWeight": "Select Weight", "Gram": "Gram (g)", "Volume": "Volume", "SelectVolume": "Select Volume", "Milliliter": "Milliliter (ml)", "Liter": "Liter (l)", "Mile": "Mile", "Yard": "Yard", "Foot": "Foot", "Inch": "Inch", "Ounce": "<PERSON><PERSON><PERSON>", "Pound": "Pound", "Gallon": "<PERSON>allo<PERSON>", "Pint": "<PERSON><PERSON>", "Quart": "Quart", "TotalVolume": "Total Volume", "Width": "<PERSON><PERSON><PERSON>", "Height": "Height", "GenerateVariants": "Generate Variants", "ClearVariants": "Clear Variants", "AttributesFeatures": "Attributes & Features", "AddCombinationsDiscription": "Please add combinations or enable your attribute, you first need to create proper attributes and values in", "AddCombinationsDiscriptionTwo": ". When done, you may enter the wanted attributes (like \"size\" or \"color\") and their respective values (\"XS\",\"red\", \"all\", etc.) in the field below; or simply select them from the right column. Then click on \"Generate\": it will automatically create all the combinations for you!", "GenerateExtras": "Generate Extras", "ClearExtras": "Clear Extras", "BulkActions": "Bulk Actions", "SelectedExtra": "Selected Extra", "LanguagesSr": "SR", "LanguagesNname": "NAME", "LanguagesIsoCode": "ISO CODE", "LanguageCode": "LANGUAGE CODE", "LanguagesFlag": "FLAG", "LanguagesPublished": "PUBLISHED", "LanguagesActions": "ACTIONS", "AddLanguageName": "Language Name", "AddLanguagesIsoCode": "Iso Code", "AddLanguageCode": "Languages Code", "AddLanguagesFlag": "Flag", "AddLanguagesPublished": "Published", "SearchLanguage": "Search by country name and iso code, language code", "CurrenciesName": "Name", "Currencyisocode": "Iso Code", "CurrenciesSymbol": "Symbol", "CurrenciesIsoCode": "Iso Code", "CurrenciesExchangeRate": "Exchange Rate", "CurrenciesEnabled": "Enabled", "CurrenciesLiveExchangeRates": "Live Exchange Rates", "CurrenciesActions": "Actions", "SearchIsoCode": "Search by iso code", "AddLanguage": "Add Language", "AddLanguageText": "Add your Language necessary information from here", "UpdateLanguage": "Update Language", "UpdateLanguageText": "Updated your Language necessary information from here", "LanguageName": "Language Name", "UpdateCurrency": "Update C<PERSON><PERSON>cy", "UpdateCurrencyText": "Updated your currency and necessary information from here", "AddCurrency": "Add <PERSON>cy", "AddCurrencyText": "Add your Currency and necessary information from here", "Countries": "Countries", "States": "States", "TaxZone": "Tax Zone", "ShippingZone": "Shipping Zone", "SearchBy": "Search by zone name", "Status": "Status", "Show": "Show", "Hide": "<PERSON>de", "NameTbl": "NAME", "SRTbl": "SR", "IsoCode": "Iso Code", "CallPrefix": "Call Prefix", "Country": "Country", "Zones": "Zones", "Zone": "Zone", "Rates": "Rates", "SearchByStateNameAndIsoCode": "Search by state name and iso code", "UpdatedZone": "Update Zone", "UpdatedZoneText": "Updated your Zone necessary information from here", "AddZone": "Add Zone", "AddZoneText": "Add your Zone necessary information from here", "ZoneName": "Zone Name", "UpdateCountry": "Update Country", "UpdateCountryText": "Updated your Country necessary information from here", "AddCountry": "Add Country", "AddCountryText": "Add your Country necessary information from here", "CountryName": "Country Name", "UpdateState": "Update State", "UpdateStateText": "Updated your State necessary information from here", "AddState": "Add State", "AddStateText": "Add your State necessary information from here", "StateName": "State Name", "StateBtn": "State", "AddTaxZone": "Add Tax Zone", "AddTaxZoneText": "Add your tax zone necessary information from here", "UpdateTaxZone": "Update Tax Zone", "UpdateTaxZoneText": "Updated your tax zone necessary information from here", "TaxZoneName": "Tax Zone Name", "State": "State", "TaxZoneBtn": "Tax Zone", "UpdateShippingZone": "Update Shipping Zone", "UpdateShippingZoneText": "Updated your Shipping Zone necessary information from here", "AddShippingZone": "Add Shipping Zone", "AddShippingZoneText": "Add your Shipping Zone necessary information from here", "ShippingZoneName": "Shipping Zone Name", "ShippingRate": "Shipping Rate", "ShippingZoneBtn": "Shipping Zone", "AddTaxes": "Add Taxes", "AddTaxesText": "Add your Taxes and necessary information from here", "UpdateTaxes": "Update Taxes", "UpdateTaxesText": "Update your Taxes and necessary information from here", "TaxesBtn": "Taxes", "shippingZoneSearch": "Search by shipping zone name", "taxNameSearch": "Search by tax name", "HomeSettings": "Home Page", "SingleSetting": "Product Slug Page", "AboutUsSetting": "About Us", "PrivacyTCSetting": "Privacy Policy and T&C", "FAQSetting": "FAQs", "OffersStting": "Offers", "ContactUsStting": "Contact Us", "StoreCustomizationPageTitle": "Store Customizations", "Header": "Header", "HeaderContacts": "Header Contacts", "HeaderText": "Header Text", "PhoneNumber": "Phone Number", "HeaderLogo": "Header <PERSON>", "weAreAvailable": "We are available 24/7, Need help?", "MainSlider": "Main Slider", "Slider": "Slide<PERSON>", "SliderImages": "Slider Images", "SliderTitle": "Slider Title", "SliderDescription": "Slider Description", "SliderButtonName": "Slide<PERSON>", "SliderButtonLink": "Slide<PERSON>", "Options": "Options", "LeftRighArrows": "Left and Right Arrows", "BottomDots": "Bottom Dots", "Both": "Both", "DiscountCouponTitle1": "Discount Coupon Code Box", "DiscountCouponCodeTitle2": "Discount Coupon Code", "ShowHide": "Show / Hide", "SuperDiscountActiveCouponCode": "Super Discount Active Coupon Code", "SliderFullWidth": "Slide<PERSON>", "PlaceHolderImage": "Place Holder Image", "ImagesResolution": "Images resolution 500X400px", "PromotionBanner": "Promotion Banner", "EnableThisBlock": "Enable This Block", "Title": "Title", "Description": "Description", "DESCRIPTION": "DESCRIPTION", "PromotionDescription": "Promotion Description", "ButtonName": "Button Name", "ButtonLink": "Button Link", "FeaturedCategories": "Featured Categories", "ProductsLimit": "Products Limit", "PopularProductsTitle": "Popular Products", "PopularDescription": "Popular Description", "QuickDeliverySectionTitle": "Quick Delivery Section", "SubTitle": "Sub Title", "Image": "Image", "LatestDiscountedProductsTitle": "Latest Discounted Products", "GetYourDailyNeedsTitle": "Get Your Daily Needs", "LatestDiscountDescription": "Latest Discount Description", "DailyNeedDescription": "Daily Need Description", "Button1Link": "Button 1 Link", "Button2Link": "Button 2 Link", "Button1image": "Button 1 Image", "Button2image": "Button 2 Image", "ImageLeft": "Image Left", "ImageRight": "Image Right", "FeaturePromoSectionTitle": "Feature Promo Section", "FreeShipping": "Free Shipping", "Support": "Support", "SecurePayment": "Secure Payment", "LatestOffer": "Latest Offer", "TopCategory": "Top Category", "FooterTitle": "Footer", "Block": "Block", "Link": "Link", "FooterLogo": "Footer <PERSON>", "FooterAddress": "Address", "FooterPhone": "Phone", "FooterEmail": "Email", "FishAndMeat": "Fish & Meat", "FooterBottomContact": "Footer Bottom Contact Number", "SocialLinksTitle": "Social Links", "PaymentMethod": "Payment Method", "Statement": "Statement", "SearchStaffName": "Search by open or closing staff name", "SoftDrinks": "Soft Drinks", "BabyCare": "Baby Care", "BeautyAndHealth": "Beauty & Health", "MyAccount": "My Account", "MyOrders": "My Orders", "RecentOrders": "Recent Orders", "UpdatedProfile": "Updated Profile", "SocialLinks": "Social Links", "OnlineStorePageTitle": "Online Store Setting", "General": "General", "MainShopActivity": "Main Shop Activity", "StripePublicKey": "Stripe Public Key", "AllowStripe": "Allow Stripe", "AllowOrderingOfOutOfStock": "Allow Ordering Of Out-Of-Stock Products", "CustomDomainName": "Custom Domain Name", "ShopDomain": "Shop Domain", "Verify": "Verify", "AllowPaypal": "<PERSON>ow <PERSON>", "PaypalPublicKey": "Paypal Public Key", "UseMultipleLanguagesInShop": "I'd like to use multiple languages in my shop, admin and POS", "UseMultipleCurrenciesInShop": "I'd like to use multiple currencies in my shop, admin and POS", "NumberOfImagesPerProduct": "Number of images per product", "PosView": "POS view", "WorkingHours": "Working Hours", "Open": "Open", "Close": "Close", "InvoiceNumberStartValue": "Invoice number start value", "InvoiceNumberLeadingZeros": "Invoice number leading zeros", "RetailPOS": "Retail POS", "Restaurant": "Restaurant", "Shipping": "Shipping", "AddShipping": "Add Shipping ", "SearchByShippingName": "Search by shipping name", "ShippingName": "Shipping Name", "ShippingDescription": "Shipping Description", "Logo": "Logo", "AddShippingText": "Add your Shipping and necessary information from here", "UpdateShipping": "Update Shipping", "UpdateShippingText": "Update your Shipping and necessary information from here", "CarrierLogo": "Carrier Logo", "Reports": "Reports", "Orders": "Orders", "Source": "Source", "SHOP": "SHOP", "Actions": "Actions", "Speedgrade": "Speed grade", "TrackingURL": "Tracking URL", "Select": "Select", "ApplyTheCostTheHighestDefinedRange": "Apply The Cost Of The Highest Defined Range", "DisableCarrier": "Disable Carrier", "InvoiceNumberType": "Invoice number type", "AddressLine": "Address", "GlobalContactNumber": "Contact", "InvoiceCompanyName": "Company Name", "VatNumber": "Vat Number", "Vat": "Vat", "MenuEditor": "Menu Editor", "Categories": "Categories", "AboutUs": "About Us", "ContactUs": "Contact Us", "Careers": "Careers", "LatestNews": "Neuesten Nachrichten", "Offers": "Offers", "FAQ": "FAQ", "PrivacyPolicy": "Privacy Policy", "TermsConditions": "Terms & Conditions", "PageHeader": "<PERSON> Header", "PageHeaderBg": "<PERSON> Header Background", "PageTitle": "Page Title", "AboutPageTopContentLeft": "About Page Top Content Left", "TopTitle": "Top Title", "TopDescription": "Top Description", "BoxOneTitle": "Box One Title", "BoxOneSubtitle": "Box One Subtitle", "BoxOneDescription": "Box One Description", "BoxTwoTitle": "Box Two Title", "BoxTwoSubtitle": "Box Two Subtitle", "BoxTwoDescription": "Box Two Description", "PageTopContentRight": "Page Top Content Right", "TopContentRightImage": "Top Content Right Image", "RightBox": "Right Box", "MiddleContentSection": "Content Section", "MiddleDescriptionOne": "First Paragraph", "MiddleDescriptionTwo": "Second Paragraph", "MiddleContentImage": "Content Image", "OurFounder": "Our Team", "OurFounderTitle": "Our Team Title", "OurFounderDescription": "Our Team Description", "OurFounderOneImage": "Our Team One Image", "OurFounderOneTitle": "Our Team One Title", "OurFounderOneSubTitle": "Our Team One Sub Title", "OurFounderTwoImage": "Our Team Two Image", "OurFounderTwoTitle": "Our Team Two Title", "OurFounderTwoSubTitle": "Our Team Two Sub Title", "OurFounderThreeImage": "Our Team Three Image", "OurFounderThreeTitle": "Our Team Three Title", "OurFounderThreeSubTitle": "Our Team Three Sub Title", "OurFounderFourImage": "Our Team Four Image", "OurFounderFourTitle": "Our Team Four Title", "OurFounderFourSubTitle": "Our Team Four Sub Title", "OurFounderFiveImage": "Our Team Five Image", "OurFounderFiveTitle": "Our Team Five Title", "OurFounderFiveSubTitle": "Our Team Five Sub Title", "OurFounderSixImage": "Our Team Six Image", "OurFounderSixTitle": "Our Team Six Title", "OurFounderSixSubTitle": "Our Team Six Sub Title", "OurTeam": "Member", "PrivacyPolicyTermsTitle": "Privacy Policy and T&C", "PageText": "Page Text", "LeftImage": "Left Image", "FaqTitleOne": "Faq Title One", "FaqDescriptionOne": "Faq Description One", "FaqTitleTwo": "Faq Title Two", "FaqDescriptionTwo": "Faq Description Two", "FaqTitleThree": "Faq Title Three", "FaqDescriptionThree": "Faq Description Three", "FaqTitleFour": "Faq Title Four", "FaqDescriptionFour": "Faq Description Four", "FaqTitleFive": "Faq Title Five", "FaqDescriptionFive": "Faq Description Five", "FaqTitleSix": "Faq Title Six", "FaqDescriptionSix": "Faq Description Six", "FaqTitleSeven": "Faq Title Seven", "FaqDescriptionSeven": "Faq Description Seven", "FaqTitleEight": "Faq Title Eight", "FaqDescriptionEight": "Faq Description Eight", "FAQPageHeader": "FAQs <PERSON>", "FaqLeftCol": "FAQs Left Column", "EmailUs": "Email Us Box", "EboxTitle": "Title", "EboxEmail": "Email", "Eboxtext": "Text", "CallUs": "Call Us Box", "CallusboxTitle": "Title", "CallUsboxPhone": "Phone", "CallUsboxText": "Text", "Address": "Address Box", "AddressboxTitle": "Title", "AddressboxAddressOne": "Address", "AddressboxAddressTwo": " Address Two", "AddressboxAddressThree": " Address Three", "MidLeftCol": "Middle Left Colum", "MidLeftImage": "Middle Left Image", "ContactForm": "Contact Form", "ContactFormTitle": "Contact Form Title", "ContactFormDescription": "Contact Form Description", "FAQS": "FAQs", "ResetPassword": "Reset password", "ConfirmPassword": "Confirm Password", "Reset": "Reset", "AlreadyHaveAccount": "Already have an account? <PERSON>gin", "DoesProductHaveCombinations": " Does this product have combinations?", "Export": "Export", "Action": "Action", "OrginalPrice": "OrginalPrice", "SR": "SR", "View": "View", "Name": "Name", "LastMonth": "Last Month", "Method": "Method", "StoreCustomization": "Store Customization", "ViewStore": "View Store", "HomePageDiscountTitle": "Home Page Discount Title", "CheckOut": "Checkout", "Login": "<PERSON><PERSON>", "Logout": "Logout", "PersonalInfo": "Personal Details", "ShippingInfo": "Shipping Details", "CartItemSection": "Cart Item Section", "firstName": "First Name", "lastName": "Last Name", "emailAddress": "Email Address", "streetAddress": "Street Address", "City": "City", "ZipCode": "Zip / Postal", "Shippingcost": "Shipping Cost", "OrderSummary": "Order Summary", "ApplyButton": "Apply Button", "Subtotal": "Sub Total", "totalCost": "Total Cost", "DashboardSetting": "Dashboard Setting", "InvoiceMessage1st": "Invoice Message First Part", "InvoiceMessage2nd": "Invoice Message Last Part", "PendingOrder": "Pending Order", "ProcessingOrder": "Processing Order", "CompleteOrder": "Complete Order", "MyOrder": "My Order", "UpdateProfile": "Update Profile", "FullName": "Full Name", "UserAddress": "Address", "PhoneMobile": "Phone/Mobile", "EmailAddress": "Email Address", "UpdateButton": "Update <PERSON><PERSON>", "CurrentPassword": "Current Password", "NewPassword": "New Password", "PrintButton": "Print Button", "DownloadButton": "Download Button", "ChangePassword": "Change Password", "Shippingmethod": "Shipping Method", "ContinueButton": "Continue <PERSON>", "ConfirmButton": "Confirm <PERSON>", "Checkout": "Checkout", "ShippingAddress": "Shipping Address", "CommonSetting": "Common Settings", "AdminSetting": "<PERSON><PERSON>s", "SeoSetting": "<PERSON><PERSON>", "StoreSetting": "Store Settings", "Settings": "Settings", "MetaTitle": "Meta Title", "MetaDescription": "Meta Description", "MetaKeyword": "Meta Keywords", "MetaImage": "Meta Image", "EnableCOD": "Enable Cash On Delivery", "EnableStripe": "Enable Stripe Payment", "StripeKey": "Stripe Key", "StripeSecret": "Stripe Secret", "EnableGoogleLogin": "Enable Google Login", "GoogleClientId": "Google Client ID", "GoogleSecret": "Google Secret Key", "EnableGoggleAnalytics": "Enable Goggle Analytics", "GoogleAnalyticKey": "Google Analytic Key", "EnableFacebookPixel": "Enable Facebook Pixel", "FacebookPixelKey": "Facebook Pixel Key", "MetaUrl": "<PERSON><PERSON>", "TawkChatWidgetID": "Tawk Chat Widget ID", "TawkChatPropertyID": "Tawk Chat Property ID", "EnableTawkChat": "Enable <PERSON><PERSON><PERSON>", "Favicon": "Favicon", "StoreSettings": "Store Settings", "ShippingNameOne": "Shipping One Name", "ShippingOneDes": "Shipping One Description", "ShippingOneCost": "Shipping One Cost", "ShippingNameTwo": "Shipping Two Name", "ShippingTwoDes": "Shipping Two Description", "ShippingTwoCost": "Shipping Two Cost", "TranslationSecretKey": "Translation Secret Key", "AllowAutoTranslation": "Allow Auto Translation"}