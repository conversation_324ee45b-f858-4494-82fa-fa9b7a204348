
VITE_APP_API_BASE_URL=http://localhost:5055/api
VITE_APP_STORE_DOMAIN=http://localhost:3000
VITE_APP_API_SOCKET_URL=http://localhost:5055
VITE_APP_ADMIN_DOMAIN=https://dashtar-admin.netlify.app/login



VITE_APP_CLOUD_NAME=your_cloudinary_cloud_name
VITE_APP_CLOUDINARY_API_KEY=your_cloudinary_api_key
VITE_APP_CLOUDINARY_API_SECRET=your_cloudinary_api_secret
VITE_APP_CLOUDINARY_UPLOAD_PRESET=your_cloudinary_upload_preset
VITE_APP_CLOUDINARY_URL=https://api.cloudinary.com/v1_1/your_cloudinary_cloud_name/image/upload



# for auto transltion with my memory api key, this have limit but use this for upto free
VITE_APP_MYMEMORY_API_KEY=a91efbf362a6e399453d


# you must need to add this 32digit hex random key in both admin and backend same key, otherwish roles based will not work. you can generate by this command
# openssl rand -hex 32


VITE_APP_ENCRYPT_PASSWORD=856305f1a5b7ba87b8448e69b3bb7a4631c23f0afa2ca5331fa1373f7e372345
